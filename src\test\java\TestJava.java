import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.Session;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.setting.Setting;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import util.SqliteUtil;
import util.TreeUtil;
import util.Util;

import static util.TreeUtil.queryLogByID;

public class TestJava {

	@Test
	public void test_setting() {
		Setting setting = new Setting("config.setting");
		String url = setting.getByGroup("url", "sqlite");
		System.out.println("url = " + Util.getFileUploadPath());
	}

	@Test
	public void test_query_text() throws SQLException {
		JSONArray objects = SqliteUtil.executeQuery("select * from LAUNCH_CONFIRM");
		System.out.println(objects);
	}

	@Test
	public void test_queryAll() throws SQLException {
		List<Entity> entities = Db.use().findAll("LAUNCH_CONFIRM");
		JSONArray jsonArray = JSONUtil.parseArray(entities);
		System.out.println(jsonArray);
	}

	@Test
	public void test_command() {
		TimeInterval interval = new TimeInterval();
		int i = SqliteUtil.executeCommand("insert into LAUNCH_CONFIRM (ID, PID, NAME, TABLE_NUM,SAVE_DATA)" + "values (3,4,'我的名字','我的表头','表的数据')");
		System.out.println("i = " + i);
		System.out.println("耗时 = " + interval.intervalPretty());
	}

	@Test
	public void test_delete() {
		TimeInterval interval = new TimeInterval();
		int i = SqliteUtil.executeCommand("delete from LAUNCH_CONFIRM where id=1");
		System.out.println("i = " + i);
		System.out.println("耗时 = " + interval.intervalPretty());
	}

	@Test
	public void test_getNowTime() {
		String nowTime = DateUtil.now();

	}

	@Test
	public void test_() {
		String sql = TreeUtil.getQueryAllParentSql("3", "", "");
		System.out.println("sql = " + sql);
		JSONArray objects = SqliteUtil.executeQuery(sql);
		System.out.println(objects);
	}

	@Test
	public void test_replace() {
		String tableData = "'我的名字'是'？？whq？？'";
		tableData = StrUtil.replace(StrUtil.replace(tableData, "'", "′"), "？", "");
		System.out.println("tableData = " + tableData);
	}

	@Test
	public void test_format() {
		TimeInterval interval = new TimeInterval();

		String a = "123{},{{}}";
		String format = StrUtil.format(a, "abc", "123");
		System.out.println("format = " + format);
		System.out.println("耗时 = " + interval.intervalPretty());
	}

	@Test
	public void test_buildTree() {
		JSONArray objects = TreeUtil.buildTree("29", "1");
		Console.log(objects);
	}

	@Test
	public void test_sqllimit() {
		TimeInterval t = new TimeInterval();
		String str = FileUtil.readUtf8String("C://TestOut//51.log");
		Console.log("耗时:", t.intervalPretty());
		byte[] byteArray = str.getBytes(java.nio.charset.StandardCharsets.UTF_8);
		int byteSize = byteArray.length;
		Console.log("byteSize:", byteSize);
		Console.log("耗时:", t.intervalPretty());
		String sql = "update test_limit set test='1' where id=1";
		SqliteUtil.executeCommand(sql);
		Console.log("耗时:", t.intervalPretty());
	}

	@Test
	public void test_sqllimit1() {
		TimeInterval t = new TimeInterval();
		String str = FileUtil.readUtf8String("C://TestOut//651.txt");
		str = str + str + str + str;
		Entity entity = Entity.create("test_limit").set("test", str);
		Entity where = Entity.create("test_limit").set("id", 1);
		Session session = Session.create();
		try {
			session.beginTransaction();
			session.update(entity, where);
			session.commit();
		} catch (SQLException e) {
			session.quietRollback();
		}
		Console.log("耗时:", t.intervalPretty());
	}

	@Test
	public void test_back() {
		TimeInterval t = new TimeInterval();
		String str = RuntimeUtil.execForStr("ipconfig");
		Console.log(DateUtil.now(), "str:", str);
		Console.log("耗时:", t.intervalPretty());
	}

	@Test
	public void testWorkType() {
		JSONArray workType = Util.getPostType();
		System.out.println(workType);
	}

	@Test
	public void test_excelOut() {
		Map<String, Object> row1 = new LinkedHashMap<>();
		row1.put("姓名", "张三");
		row1.put("年龄", 23);
		row1.put("成绩", 88.32);
		row1.put("是否合格", true);
		row1.put("考试日期", DateUtil.date());

		Map<String, Object> row2 = new LinkedHashMap<>();
		row2.put("姓名", "李四");
		row2.put("年龄", 33);
		row2.put("成绩", 59.50);
		row2.put("是否合格", false);
		row2.put("考试日期", DateUtil.date());

		ArrayList<Map<String, Object>> rows = CollUtil.newArrayList(row1, row2);
// 通过工具类创建writer
		ExcelWriter writer = ExcelUtil.getWriter("d:/writeMapTest.xlsx");
// 合并单元格后的标题行，使用默认标题样式
		writer.merge(row1.size() - 1, "一班成绩单");
// 一次性写出内容，使用默认样式，强制输出标题
		writer.write(rows, true);
// 关闭writer，释放内存
		writer.close();

	}

	@Test
	public void test_sort() {
		JSONArray jsonArray = new JSONArray();
		jsonArray.add(new JSONObject().put("name", "Alice").put("age", 22).put("score", 90));
		jsonArray.add(new JSONObject().put("name", "Bob").put("age", 24).put("score", 85));
		jsonArray.add(new JSONObject().put("name", "Charlie").put("age", 21).put("score", 95));


		// 输出排序后的JSONArray
		System.out.println(jsonArray);
	}

	@Test
	public void test_11() {
		JSONArray objects = queryLogByID("19");
		long time = DateUtil.parseDateTime("1702089336000").getTime();
		Console.log(DateUtil.now(), "time:", time);
	}
}

---
globs: *Test.java,src/test/**
description: 测试代码规范和最佳实践
---

# 测试规范指南

## 测试结构
```
src/test/java/
├── ResourceLeakTest.java    # 资源泄漏测试
└── TestJava.java           # 通用功能测试
```

## 测试类命名规范
- 单元测试: `{ClassName}Test.java`
- 集成测试: `{ClassName}IntegrationTest.java`
- 性能测试: `{ClassName}PerformanceTest.java`

## JUnit 4 使用规范

### 1. 基本测试结构
```java
public class TableServletTest {
    
    @Before
    public void setUp() {
        // 测试前准备工作
        // 初始化测试数据
    }
    
    @Test
    public void testOpenEdit() {
        // 测试OpenEdit方法
        // 断言验证结果
    }
    
    @After
    public void tearDown() {
        // 测试后清理工作
        // 清理测试数据
    }
}
```

### 2. 数据库测试
```java
@Test
public void testDatabaseOperation() {
    // 准备测试数据
    String testId = "test_001";
    
    // 执行测试操作
    SqliteUtil.executeUpdate("INSERT INTO LAUNCH_CONFIRM (id, data) VALUES (?, ?)", 
                            testId, testData);
    
    // 验证结果
    JSONArray result = SqliteUtil.executeQuery("SELECT * FROM LAUNCH_CONFIRM WHERE id = ?", testId);
    assertFalse("查询结果不应为空", result.isEmpty());
    
    // 清理测试数据
    SqliteUtil.executeUpdate("DELETE FROM LAUNCH_CONFIRM WHERE id = ?", testId);
}
```

### 3. Servlet测试
```java
@Test
public void testServletResponse() throws Exception {
    // 模拟HTTP请求
    HttpServletRequest mockRequest = mock(HttpServletRequest.class);
    HttpServletResponse mockResponse = mock(HttpServletResponse.class);
    
    // 设置请求参数
    when(mockRequest.getParameter("act")).thenReturn("OpenEdit");
    
    // 执行测试
    TableServlet servlet = new TableServlet();
    servlet.service(mockRequest, mockResponse);
    
    // 验证响应
    verify(mockResponse).setCharacterEncoding("UTF-8");
}
```

## 测试数据管理

### 1. 测试数据隔离
- 使用独立的测试数据库或数据
- 测试完成后及时清理数据
- 避免测试之间的数据污染

### 2. 测试配置
```java
// 测试专用配置
private static final String TEST_DB_PATH = "test_identifier.sqlite";
private static final String TEST_FILE_PATH = "test_files/";
```

## 性能测试规范

### 1. 资源泄漏测试
```java
@Test
public void testResourceLeak() {
    // 监控内存使用
    long initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
    
    // 执行大量操作
    for (int i = 0; i < 1000; i++) {
        // 执行业务操作
    }
    
    // 触发垃圾回收
    System.gc();
    
    // 检查内存是否正常释放
    long finalMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
    assertTrue("可能存在内存泄漏", finalMemory - initialMemory < MEMORY_THRESHOLD);
}
```

### 2. 并发测试
```java
@Test
public void testConcurrentAccess() throws InterruptedException {
    int threadCount = 10;
    CountDownLatch latch = new CountDownLatch(threadCount);
    
    for (int i = 0; i < threadCount; i++) {
        new Thread(() -> {
            try {
                // 并发执行业务操作
                performBusinessOperation();
            } finally {
                latch.countDown();
            }
        }).start();
    }
    
    latch.await(30, TimeUnit.SECONDS);
    // 验证并发操作结果
}
```

## 测试最佳实践
- 测试方法名使用中文描述测试场景
- 每个测试方法只测试一个功能点
- 使用断言验证期望结果
- 测试异常情况和边界条件
- 保持测试代码简洁清晰
- 定期运行测试确保代码质量
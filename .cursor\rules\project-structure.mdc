---
alwaysApply: true
description: TableConfirm项目结构和架构指南
---

# TableConfirm 项目架构指南

## 项目概述
TableConfirm是一个基于Java Servlet的Web应用，用于表格数据确认和编辑管理。

## 核心架构
- **后端**: Java 8 + Servlet API 4.0 + SQLite数据库
- **前端**: jQuery + Handsontable + Layui
- **构建工具**: Maven
- **数据库**: SQLite (identifier.sqlite)

## 项目结构
```
src/main/java/
├── common/           # 公共基础类
│   ├── BaseServlet.java     # Servlet基类，所有业务Servlet继承此类
│   ├── Result.java          # 统一返回结果包装类
│   └── PageMarker.java      # 分页标记类
├── servlet/          # 业务Servlet层
│   ├── TableServlet.java    # 表格相关业务API
│   ├── TreeServlet.java     # 树形结构相关API
│   └── LogServlet.java      # 日志相关API
└── util/            # 工具类
    ├── SqliteUtil.java      # SQLite数据库操作
    ├── ExcelUtil.java       # Excel处理工具
    ├── TableUtil.java       # 表格业务工具
    └── TreeUtil.java        # 树形结构工具

web/                 # 前端静态资源
├── page/confirm/    # 主要业务页面
├── plugins/         # 第三方插件
└── static/          # 静态资源
```

## 关键配置文件
- [pom.xml](mdc:pom.xml) - Maven项目配置
- [config.setting](mdc:src/main/resources/config.setting) - 应用配置
- [web.xml](mdc:web/WEB-INF/web.xml) - Web应用配置

## 开发约定
- 所有Servlet必须继承 [BaseServlet.java](mdc:src/main/java/common/BaseServlet.java)
- 使用hutool工具库进行常用操作
- 前端使用Handsontable进行表格编辑
- 数据库操作统一通过SqliteUtil进行
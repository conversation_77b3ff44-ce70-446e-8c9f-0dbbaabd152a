var table, layer, upload, form, laydate;
layui.use(['layer', 'form', 'upload', 'table', 'laydate'], function () {
    table = layui.table;
    layer = layui.layer;
    upload = layui.upload;
    laydate = layui.laydate;
    form = layui.form;
});
sessionStorage.setItem("userIp", "127.0.0.1");
sessionStorage.setItem("fullname", "设计师");
sessionStorage.setItem("username", "test");

var isAdmin = false;
var userName = getQueryString('userName');
if (userName != null) {
    if (userName == 'admin') {
        isAdmin = true;
    }
}

if (isAdmin) {
    sessionStorage.setItem("fullname", "管理员");
    sessionStorage.setItem("username", "admin");
} else {
    $('#clear-sign').remove();
}


var imgPath = '../../static/img/';
var security = [
    {
        "NAME": "公开",
        "KEY": "0"
    },
    {
        "NAME": "内部",
        "KEY": "1"
    },
    {
        "NAME": "秘密",
        "KEY": "2"
    },
    {
        "NAME": "机密",
        "KEY": "3"
    }
];

function initConstant() {
    postAjax("tree", 'GetWorkType', {}, false, function (datas) {
        HotUtil.workTypes = datas;
    });

    postAjax("tree", 'GetPostType', {}, false, function (datas) {
        datas.unshift("取消设置");
        HotUtil.postTypes = datas;
    });
}


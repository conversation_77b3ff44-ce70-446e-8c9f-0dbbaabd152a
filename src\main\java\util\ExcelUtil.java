package util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.cell.CellHandler;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import static util.TableUtil.dealObjNull;

public class ExcelUtil {


	/**
	 * 将HandsonTable的表格数据导出excel
	 *
	 * @param data     JSON数据
	 * @param tempPath 临时文件目录
	 * @return
	 */
	public static File tableData2Excel(JSONObject data, String tempPath) {

		JSONObject saveData = dealObjNull(data.getStr("SAVE_DATA"));
		String security = data.getStr("SECURITY_NAME");
		int headerRow = data.getInt("TABLE_HEADER", 0);
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(data.getStr("TABLE_NUM"))) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		tableName += "（" + security + "）";
		String path = tempPath + "\\" + tableName + ".xlsx";
		return tableData2Excel(saveData, path, headerRow, tableName);
	}

	/**
	 * 获取单元格样式
	 *
	 * @param writer
	 * @param isHeader
	 * @return
	 */
	public static CellStyle getCellStyle(ExcelWriter writer, boolean isHeader) {
		CellStyle style = writer.createCellStyle();
		// 设置单元格横向和纵向居中对齐
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);
		// 设置单元格四周边框
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setWrapText(true);
		if (isHeader) {
			// 设置标题字体 加粗
			Font font = writer.createFont();
			font.setBold(true);
			style.setFont(font);
			// 设置背景色
			style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		}
		return style;
	}


	/**
	 * 将HandsonTable的表格数据导出excel
	 *
	 * @param saveData  JSON数据
	 * @param path
	 * @param headerRow
	 * @param tableName
	 * @return
	 */
	public static File tableData2Excel(JSONObject saveData, String path, int headerRow, String tableName) {
		JSONArray tableData = saveData.getJSONArray("tableData");
		int maxCol = tableData.getJSONArray(0).size();
		File file = FileUtil.file(path);
		Util.removeEmptyValuesFromArray(tableData);
//		tableData.add(0, tableName);
		ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(file);
		// 一次性写出内容
		writer.write(tableData, false);
		// 设置列宽
//		for (int i = 0; i < writer.getColumnCount(); i++) {
//			writer.setColumnWidth(i, 20);
//		}
		// 设置行高
//		for (int i = 0; i < writer.getRowCount(); i++) {
//			writer.setRowHeight(i, 26);
//		}

		writer.autoSizeColumnAll();
		// 处理合并单元格
		JSONArray mergeds = saveData.getJSONArray("merged");
		//因为增加了一行 所以合并信息中的所有行的数值都要加1
//		mergeds.add(0, new JSONObject()
//				.set("row", 0)
//				.set("col", 0)
//				.set("rowspan", 1)
//				.set("colspan", maxCol)
//				.set("removed", false));
//		for (int i = 1; i < mergeds.size(); i++) {
//			JSONObject merged = mergeds.getJSONObject(i);
//			int row = merged.getInt("row");
//			merged.set("row", row + 1);
//		}
		for (int i = 0; i < mergeds.size(); i++) {
			JSONObject merged = mergeds.getJSONObject(i);
			int row = merged.getInt("row");
			int col = merged.getInt("col");
			int rowspan = merged.getInt("rowspan");
			int colspan = merged.getInt("colspan");
			Cell cell = writer.getCell(col, row);
			String value = Convert.toStr(util.ExcelUtil.getCellValue(cell));
			writer.merge(row, row + rowspan - 1, col, col + colspan - 1, value, false);
		}

		//因为增加了一行 数值要加1
		CellStyle headerStyle = getCellStyle(writer, true);
		CellStyle cellStyle = getCellStyle(writer, false);
		for (int i = 0; i < writer.getRowCount(); i++) {
			if (i < headerRow) {
				writer.setRowStyleIfHasData(i, headerStyle);
			} else {
				writer.setRowStyleIfHasData(i, cellStyle);
			}
		}
		writer.setFreezePane(headerRow);
		writer.close();

		return file;
	}

	/**
	 * 描述：对表格中数值进行格式化
	 *
	 * @param cell
	 * @return
	 */
	public static Object getCellValue(Cell cell) {
		String cellValue = "";
		// 以下是判断数据的类型
		switch (cell.getCellType()) {
			case NUMERIC: // 数字
				if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					cellValue = sdf.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(cell.getNumericCellValue()))
							.toString();
				} else {
					DataFormatter dataFormatter = new DataFormatter();
					cellValue = dataFormatter.formatCellValue(cell);
				}
				break;
			case STRING: // 字符串
				cellValue = cell.getStringCellValue();
				break;
			case BOOLEAN: // Boolean
				cellValue = cell.getBooleanCellValue() + "";
				break;
			case FORMULA: // 公式
				cellValue = cell.getCellFormula() + "";
				break;
			case BLANK: // 空值
				cellValue = "";
				break;
			case ERROR: // 故障
				cellValue = "非法字符";
				break;
			default:
				cellValue = "未知类型";
				break;
		}
		return cellValue;
	}

	/**
	 * 获取最大的列数量
	 *
	 * @param reader
	 * @return
	 */
	public static int getMaxColumnCount(ExcelReader reader) {
		int rowCount = reader.getRowCount();
		int colCount = 0;
		for (int i = 0; i < rowCount; i++) {
			int c = reader.getColumnCount(i);
			if (c > colCount) {
				colCount = c;
			}
		}
		return colCount;
	}

	/**
	 * 读取excel转换为JSONArray 其中空的单元格设置为""空字符串
	 *
	 * @param reader
	 * @return
	 */
	public static JSONArray readAll(ExcelReader reader) {
		int rowCount = reader.getRowCount();
		int colCount = getMaxColumnCount(reader);
		JSONArray readAll = new JSONArray();
		for (int r = 0; r < rowCount; r++) {
			JSONArray rowList = new JSONArray();
			for (int c = 0; c < colCount; c++) {
				rowList.add("");
			}
			readAll.add(rowList);
		}
		CellHandler c = new CellHandler() {
			@Override
			public void handle(Cell cell, Object value) {
				if (cell != null) {
					value = getCellValue(cell);
					readAll.getJSONArray(cell.getRowIndex()).set(cell.getColumnIndex(), StrUtil.trim(Convert.toStr(value)));
				}
			}
		};
		reader.read(c);
		return readAll;
	}

	/**
	 * 读取excel图片转换为JSONObject
	 *
	 * @param sheet
	 * @return
	 */
	public static JSONObject getExcelPicData(Sheet sheet) {
		JSONObject res = new JSONObject();
		JSONArray arr = new JSONArray();
		int maxPicRow = 0, maxPicCol = 0;
		String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		String fileUploadPath = Util.getFileUploadPath();
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		fileUploadPath = fileUploadPath + File.separator + month;
		FileUtil.mkdir(fileUploadPath);
		try {
			XSSFDrawing drawing = (XSSFDrawing) sheet.getDrawingPatriarch();
			int photoIndex = 0;
			for (XSSFShape shape : drawing.getShapes()) {
				if (shape instanceof XSSFPicture) {
					XSSFPicture picture = (XSSFPicture) shape;
					XSSFClientAnchor anchor = picture.getClientAnchor();
					XSSFPictureData pictureData = picture.getPictureData();
					short colNum = anchor.getCol1();
					int rowNum = anchor.getRow1();
					if (maxPicRow < rowNum + 1) {
						maxPicRow = rowNum + 1;
					}
					if (maxPicCol < colNum + 1) {
						maxPicCol = colNum + 1;
					}
					byte[] data = pictureData.getData();

					String photoName = (rowNum + 1) + "行," + (colNum + 1) + "列";
					//图片格式
					String photoFormat = pictureData.suggestFileExtension();
					String uuid = UUID.randomUUID().toString();//随机码

					String photoPath = fileUploadPath + File.separator + uuid;
					String src = "//" + month + "//" + uuid;
					FileUtil.writeBytes(data, photoPath);
					JSONObject photo = new JSONObject();
					photo.set("date", date);
					photo.set("col", colNum);
					photo.set("row", rowNum);
					photo.set("type", "photo");
					photo.set("photoPath", src);
					photo.set("photoName", photoName);
					photo.set("photoFormat", photoFormat);
					arr.add(photo);
				}
			}
		} catch (RuntimeException e) {

		}
		res.set("arr", arr);
		res.set("maxPicRow", maxPicRow);
		res.set("maxPicCol", maxPicCol);
		return res;
	}

	/**
	 * 清除excel的空行
	 *
	 * @param all
	 * @param maxRow
	 * @return
	 */
	public static JSONArray clearExcelEmptyRow(JSONArray all, int maxRow) {
		for (int i = maxRow; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			boolean isEmpty = true;
			for (int j = 0; j < row.size(); j++) {
				if (StrUtil.isNotBlank(row.getStr(j))) {
					isEmpty = false;
					break;
				}
			}
			if (isEmpty) {
				all.remove(i);
				i--;
			}
		}
		return all;
	}

	/**
	 * 清除excel的空列
	 *
	 * @param all
	 * @param maxCol
	 * @return
	 */
	public static JSONArray clearExcelEmptyCol(JSONArray all, int maxCol) {
		int maxRowCol = 0;
		for (int i = 0; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			for (int j = row.size() - 1; j >= 0; j--) {
				if (StrUtil.isNotBlank(row.getStr(j))) {
					if (maxRowCol < j) {
						maxRowCol = j;
					}
					break;
				}
			}
		}
		maxRowCol++;
		if (maxRowCol > maxCol) {
			maxCol = maxRowCol;
		}

		for (int i = 0; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			for (int j = maxCol; j < row.size(); j++) {
				row.remove(j);
				j--;
			}
		}
		return all;
	}

	/**
	 * 提取出字符串中最后一个符合日期格式（yyyy-MM-dd）的子串，并返回一个包含原字符串去除最后一个日期后的字符串及该日期字符串的JSONObject
	 *
	 * @param input 输入的字符串
	 * @return JSONObject 包含 "extractedDate"（提取的日期）和 "remainingString"（剩余字符串）两个字段
	 */
	public static JSONObject extractAndRemoveLastDate(String input) {
		if (input == null || input.isEmpty()) {
			return new JSONObject().set("extractedDate", "").set("remainingString", "");
		}
		String DATE_PATTERN_REGEX = "(\\d{4}-\\d{2}-\\d{2})";
		Pattern pattern = Pattern.compile(DATE_PATTERN_REGEX);
		Matcher matcher = pattern.matcher(input);

		// 查找最后一个匹配项
		String lastMatchedDate = "";
		int lastIndex = -1;
		while (matcher.find()) {
			lastMatchedDate = matcher.group(1);
			lastIndex = matcher.end(1) - 10; // 记录最后一个匹配结束的位置
		}

		// 构造结果JSONObject
		JSONObject result = new JSONObject();
		// 如果找到了日期
		if (!lastMatchedDate.isEmpty() && lastIndex != -1) {
			// 确保找到了有效的日期并且获得了正确的索引位置
			String remainingString = input.substring(0, lastIndex) + input.substring(lastIndex + 10);
			result.set("extractedDate", lastMatchedDate);
			result.set("remainingString", remainingString);
		} else {
			result.set("extractedDate", "");
			result.set("remainingString", input);
		}

		return result;
	}

	/**
	 * 将excel文件读取为handson组件的json格式
	 *
	 * @return
	 */
	public static JSONObject excel2HandsonTable(String excelPath) {
		JSONObject res = new JSONObject();
		try {

			ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(excelPath);
			Sheet sheet = reader.getSheet();
			// 获取合并单元格的坐标集合
			List<CellRangeAddress> listCombineCell = sheet.getMergedRegions();
			JSONArray merged = new JSONArray();

			int maxMergedRow = 0, maxMergedCol = 0;

			for (CellRangeAddress ca : listCombineCell) {
				// 获得合并单元格的起始行, 结束行, 起始列, 结束列
				int firstC = ca.getFirstColumn();
				int lastC = ca.getLastColumn();
				int firstR = ca.getFirstRow();
				int lastR = ca.getLastRow();

				if ((lastR + 1) > maxMergedRow) {
					maxMergedRow = lastR + 1;
				}
				if ((lastC + 1) > maxMergedCol) {
					maxMergedCol = lastC + 1;
				}

				int rowspan = lastR - firstR + 1;
				int colspan = lastC - firstC + 1;
				JSONObject m = new JSONObject();
				m.set("row", firstR).set("col", firstC).set("rowspan", rowspan).set("colspan", colspan).set("removed", false);
				merged.add(m);
			}
			JSONArray all = readAll(reader);

			//获取所有图片信息
			JSONObject picData = getExcelPicData(sheet);
			int maxPicRow = picData.getInt("maxPicRow");
			int maxPicCol = picData.getInt("maxPicCol");
			JSONArray picArr = picData.getJSONArray("arr");
			int maxRow = Math.max(maxPicRow, maxMergedRow);
			int maxCol = Math.max(maxPicCol, maxMergedCol);
			JSONArray clearRow = clearExcelEmptyRow(all, maxRow);
			JSONArray arr = clearExcelEmptyCol(clearRow, maxCol);
			int photoIndex = 0;
			JSONArray metas = new JSONArray();
			for (int i = 0; i < arr.size(); i++) {
				JSONArray row = arr.getJSONArray(i);
				for (int j = 0; j < row.size(); j++) {
					JSONArray eles = new JSONArray();
					JSONObject meta = JSONUtil.createObj().set("row", i).set("col", j);

					for (int x = 0; x < picArr.size(); x++) {
						JSONObject pic = picArr.getJSONObject(x);
						if (i == pic.getInt("row") && j == pic.getInt("col")) {
							String cellValue = row.getStr(j);
							JSONObject dateObj = extractAndRemoveLastDate(cellValue);
							String cellDate = dateObj.getStr("extractedDate");
							String remainingString = dateObj.getStr("remainingString");
							if (StrUtil.isNotBlank(cellDate)) {
								//这个单元格是日期
								DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate(cellDate);
								cellDate = cn.hutool.core.date.DateUtil.format(dateTime, "yyyy-MM-dd");
								row.set(j, remainingString);
							}
							if (StrUtil.isNotBlank(cellDate)) {
								pic.set("date", cellDate);
								pic.set("type", "sign");
								pic.set("src", pic.getStr("photoPath"));
								pic.set("class", "sign-img test-sign");
								meta.set("readOnly", true);
							} else {
								photoIndex++;
								pic.set("src", "/File" + pic.getStr("photoPath"));
								pic.set("photoShowNum", "图" + photoIndex);
								pic.set("class", "sign-img photo");
							}
							eles.add(pic);
						}
					}
					metas.add(meta.set("eles", eles));
				}
			}

			JSONObject data = new JSONObject();
			data.set("tableData", arr);
			data.set("meta", metas);
			data.set("merged", merged);
			res.set("data", data);
			res.set("success", true);
			reader.close();
		} catch (Exception e) {
			e.printStackTrace();
			res.set("success", false);
			res.set("msg", e.getLocalizedMessage());
		}
		return res;
	}

	/**
	 * 将树形结构的数据导出成excel到文件夹中
	 */
	public static void exportTreeToExcel(String path, JSONArray nodes) {
		for (int i = 0; i < nodes.size(); i++) {
			JSONObject node = nodes.getJSONObject(i);
			JSONArray children = node.getJSONArray("children");
			if (ObjectUtil.isNull(children)) {
				children = new JSONArray();
			}
			JSONObject nodeData = node.getJSONObject("data");
			String type = nodeData.getStr("TYPE");
			String name = nodeData.getStr("NAME");
			String tableNum = nodeData.getStr("TABLE_NUM");
			String sort = nodeData.getStr("SORT");
			String securityName = nodeData.getStr("SECURITY_NAME");
			if (type.equals("model")) {
				String folder = path + File.separator + name;
				FileUtil.mkdir(folder);
				if (!children.isEmpty()) {
					exportTreeToExcel(folder, children);
				}
			} else if (type.equals("project")) {
				String folder = path + File.separator + sort + "-" + name;
				FileUtil.mkdir(folder);
				if (!children.isEmpty()) {
					exportTreeToExcel(folder, children);
				}
			} else if (type.equals("a")) {
				String folderName = tableNum + "：" + name + "（" + securityName + "）";
				String folderPath = path + File.separator + folderName;
				FileUtil.mkdir(folderPath);
				nodeToExcel(nodeData, folderPath, folderName);
				if (!children.isEmpty()) {
					exportTreeToExcel(folderPath, children);
				}
			} else if (type.equals("b")) {
				String folderName = tableNum + "：" + name + "（" + securityName + "）";
				nodeToExcel(nodeData, path, folderName);
			}
		}
	}

	public static void nodeToExcel(JSONObject node, String folder, String fileName) {
		String saveDataStr = node.getStr("SAVE_DATA", "");
		if (StrUtil.isNotBlank(saveDataStr)) {
			JSONObject saveData = dealObjNull(saveDataStr);
			String path = folder + File.separator + fileName + ".xlsx";
			int headerRow = node.getInt("TABLE_HEADER", 0);
			tableData2Excel(saveData, path, headerRow, fileName);
		}
	}
}

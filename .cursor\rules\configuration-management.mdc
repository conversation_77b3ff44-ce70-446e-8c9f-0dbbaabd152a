---
description: 配置文件管理和环境配置规范
---

# 配置管理规范

## 配置文件结构
```
src/main/resources/
├── config.setting       # 应用主配置
├── db.setting          # 数据库配置  
└── cron.setting         # 定时任务配置
```

## 主配置文件 config.setting

### 文件路径配置
```properties
# Windows环境
fileStorePath=D://TableConfirm//file
fileBackupPath=D://TableConfirm//backup  
fileTempPath=D://TableConfirm//temp

# Linux环境 (注释状态)
#fileStorePath=/usr/local/src/TableConfirmFile
#fileBackupPath=/usr/local/src/TableConfirmFileBackup
#fileTempPath=/usr/local/src/TableConfirmFileTemp
```

### 业务配置
```properties
# 大表格列数阈值
LargeTableCol=6

# 工作项目类型
workType=加热片粘贴，热敏电阻粘贴，热电偶粘贴，石墨膜粘贴，膜粘贴，加热器接线，热控走线，导热填料，热管装配，接插件焊接，卡箍、扣片，锗膜，多层包覆，其他

# 岗位类型  
postType=一岗，自检，二岗，检验
```

## 配置使用规范

### 1. 配置读取
```java
// 使用hutool读取配置
Setting setting = new Setting("config.setting");
String fileStorePath = setting.getStr("fileStorePath");
String workType = setting.getStr("workType");
```

### 2. 环境配置切换
```java
// 根据环境选择配置
public class ConfigUtil {
    private static final Setting config = new Setting("config.setting");
    
    public static String getFileStorePath() {
        // 优先使用环境变量，其次使用配置文件
        String envPath = System.getenv("FILE_STORE_PATH");
        return StrUtil.isNotBlank(envPath) ? envPath : config.getStr("fileStorePath");
    }
}
```

### 3. 配置验证
```java
@Override
public void contextInitialized(ServletContextEvent sce) {
    // 在应用启动时验证配置
    validateConfiguration();
}

private void validateConfiguration() {
    String fileStorePath = ConfigUtil.getFileStorePath();
    File storeDir = new File(fileStorePath);
    if (!storeDir.exists()) {
        Console.log("创建文件存储目录: {}", fileStorePath);
        FileUtil.mkdir(storeDir);
    }
}
```

## 部署配置管理

### 1. 开发环境
```properties
# 开发环境配置
fileStorePath=D://TableConfirm//file
debug=true
logLevel=DEBUG
```

### 2. 生产环境
```properties  
# 生产环境配置
fileStorePath=/data/tableconfirm/file
debug=false
logLevel=INFO
```

### 3. Docker环境配置
```dockerfile
# Dockerfile中的环境变量
ENV FILE_STORE_PATH=/app/data/file
ENV FILE_BACKUP_PATH=/app/data/backup
ENV FILE_TEMP_PATH=/app/data/temp
```

## 敏感信息管理

### 1. 数据库密码
```properties
# 避免在配置文件中明文存储密码
# 使用环境变量或加密存储
db.password=${DB_PASSWORD}
```

### 2. 配置加密
```java
// 敏感配置加密存储
public class SecureConfig {
    private static final String ENCRYPTION_KEY = "your-encryption-key";
    
    public static String getDecryptedValue(String encryptedValue) {
        return SecureUtil.aes(ENCRYPTION_KEY.getBytes()).decryptStr(encryptedValue);
    }
}
```

## 配置最佳实践
- 不同环境使用不同的配置文件
- 敏感信息使用环境变量或加密存储
- 配置项使用有意义的名称和注释
- 定期检查和清理无用的配置项
- 配置变更需要经过代码审查
- 重要配置变更需要备份原始配置
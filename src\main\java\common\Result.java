package common;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import java.io.File;
import java.util.function.Supplier;

import javax.servlet.http.HttpServletResponse;

public class Result {

	public static void execute(Supplier method, HttpServletResponse response) {
		JSONObject res = new JSONObject();
		try {
			Object result = method.get();
			res.set("success", true);
			res.set("msg", "请求成功！");
			if (result instanceof JSONArray || result instanceof Integer) {
				res.set("data", result);
			} else if (result instanceof String) {
				res.set("msg", result);
			} else if (result instanceof JSONObject) {
				JSONObject resultObj = (JSONObject) result;
				if (resultObj.containsKey("success")) {
					res = resultObj;
				} else {
					res.set("data", result);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			res.set("success", false);
			res.set("msg", "请求出错，原因：" + e);
		}
		ServletUtil.write(response, res.toString(), "application/json");
	}

	public static void executeOutStream(Supplier method, HttpServletResponse response) {
		JSONObject res = new JSONObject();
		try {
			Object result = method.get();
			File file = (File) result;
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.setHeader("Content-Length", String.valueOf(file.length()));
			ServletUtil.write(response, file);
		} catch (Exception e) {
			e.printStackTrace();
			res.set("success", false);
			res.set("msg", "请求出错，原因：" + e);
			ServletUtil.write(response, res.toString(), "application/json");
		}
	}

	public static void outFileRes(HttpServletResponse response, JSONObject res) {
		if (res.getBool("success")) {
			File file = new File(res.getStr("data"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.setHeader("Content-Length", String.valueOf(file.length()));
			ServletUtil.write(response, file);
		} else {
			ServletUtil.write(response, res.toString(), "application/json");
		}
	}
}

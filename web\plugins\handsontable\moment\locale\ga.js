//! moment.js locale configuration
//! locale : Irish or Irish Gaelic [ga]
//! author : <PERSON> : https://github.com/askpt
!function(a,e){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?e(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],e):e(a.moment)}(this,(function(a){"use strict";
//! moment.js locale configuration
return a.defineLocale("ga",{months:["Ean<PERSON>ir","<PERSON><PERSON><PERSON>","Márta","Aibreán","Bealtaine","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>il","<PERSON><PERSON><PERSON>","<PERSON><PERSON>ó<PERSON>hai<PERSON>","<PERSON><PERSON><PERSON> Fómhair","<PERSON>hai<PERSON>","<PERSON><PERSON>ig"],monthsShort:["<PERSON>an","<PERSON>abh","<PERSON>árt","Ai<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","M.F.","<PERSON><PERSON><PERSON>.","<PERSON><PERSON>","<PERSON>ll"],monthsParseExact:!0,weekdays:["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>aoin","Dé hAoine","<PERSON>é Sathairn"],weekdaysShort:["Domh","Luan","Máirt","Céad","Déar","Aoine","Sath"],weekdaysMin:["Do","Lu","Má","Cé","Dé","A","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné ag] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d míonna",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(a){return a+(1===a?"d":a%10==2?"na":"mh")},week:{dow:1,doy:4}})}));
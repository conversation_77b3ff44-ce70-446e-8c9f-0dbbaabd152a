---
description: 构建部署规范和Maven配置指南
---

# 构建部署规范

## Maven配置

### 项目基础信息
```xml
<groupId>org.example</groupId>
<artifactId>TableConfirm</artifactId>
<version>1.0-SNAPSHOT</version>
<packaging>war</packaging>
```

### 编译配置
```xml
<properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
</properties>
```

### 核心依赖版本
- **SQLite**: ********
- **Log4j**: 2.23.1  
- **Apache POI**: 5.2.5
- **Hutool**: 5.8.29
- **Servlet API**: 4.0.1
- **iText**: 8.0.4

## 构建流程

### 1. 开发构建
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package
```

### 2. 生产构建
```bash
# 跳过测试的快速打包
mvn clean package -DskipTests

# 生成部署文件
mvn clean package -Pproduction
```

### 3. 构建产物
```
target/
├── classes/                    # 编译后的class文件
├── TableConfirm-1.0-SNAPSHOT.war  # 部署WAR包
└── test-classes/              # 测试class文件
```

## 部署配置

### 1. Tomcat部署
```xml
<!-- server.xml配置 -->
<Context path="/tableconfirm" 
         docBase="D:\idea-work\TableConfirm\target\TableConfirm-1.0-SNAPSHOT"
         reloadable="true" />
```

### 2. 目录结构
```
部署目录/
├── WEB-INF/
│   ├── classes/           # Java类文件
│   ├── lib/              # 依赖jar包
│   └── web.xml           # Web配置
├── page/                 # 页面文件
├── plugins/              # 前端插件
├── static/               # 静态资源
└── uploads/              # 上传文件目录
```

### 3. 数据文件部署
```bash
# 创建必要的目录
mkdir -p /data/tableconfirm/{file,backup,temp}

# 复制数据库文件
cp identifier.sqlite /data/tableconfirm/

# 设置权限
chmod -R 755 /data/tableconfirm/
```

## Docker部署

### 1. Dockerfile
```dockerfile
FROM tomcat:9-jdk8-alpine

# 复制WAR包
COPY target/TableConfirm-1.0-SNAPSHOT.war /usr/local/tomcat/webapps/tableconfirm.war

# 创建数据目录
RUN mkdir -p /app/data/{file,backup,temp}

# 复制配置文件
COPY src/main/resources/*.setting /app/config/

# 设置环境变量
ENV FILE_STORE_PATH=/app/data/file
ENV FILE_BACKUP_PATH=/app/data/backup  
ENV FILE_TEMP_PATH=/app/data/temp

EXPOSE 8080
```

### 2. docker-compose.yml
```yaml
version: '3.8'
services:
  tableconfirm:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./config:/app/config
    environment:
      - FILE_STORE_PATH=/app/data/file
      - FILE_BACKUP_PATH=/app/data/backup
      - FILE_TEMP_PATH=/app/data/temp
```

## 环境配置

### 1. 开发环境
- JDK 8+
- Maven 3.6+
- Tomcat 9+
- IDE: IntelliJ IDEA

### 2. 生产环境
- Linux CentOS 7+
- JDK 8 (OpenJDK)
- Tomcat 9+
- 足够的磁盘空间存储文件

### 3. 性能调优
```bash
# JVM参数配置
JAVA_OPTS="-Xms512m -Xmx2048m -XX:PermSize=256m -XX:MaxPermSize=512m"

# Tomcat连接池配置
maxThreads="200"
minSpareThreads="10"
maxSpareThreads="50"
```

## 部署检查清单

### 部署前检查
- [ ] 代码已通过所有测试
- [ ] 配置文件已更新为生产环境配置
- [ ] 数据库已备份
- [ ] 静态资源已压缩优化

### 部署后验证
- [ ] 应用正常启动
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] 关键业务功能测试通过
- [ ] 日志输出正常

### 回滚准备
- [ ] 备份当前版本
- [ ] 准备回滚脚本
- [ ] 确认回滚步骤
- [ ] 通知相关人员
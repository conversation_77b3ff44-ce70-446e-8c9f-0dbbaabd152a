{"version": 3, "file": "purify.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = stringToLowerCase(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property])) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\s\\S]*|[\\s\\S]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\s\\S]*|[\\s\\S]*%>/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML =\n    trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML('')\n      : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    typeof implementation.createHTMLDocument !== 'undefined' &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  const CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks? */\n  let SANITIZE_DOM = true;\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR)\n        : DEFAULT_ALLOWED_ATTR;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR)\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS)\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS) : {};\n    FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR) : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? (x) => x\n        : stringToLowerCase;\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, [\n    'foreignobject',\n    'desc',\n    'title',\n    'annotation-xml',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: HTML_NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // Certain elements are allowed in both SVG and HTML\n      // namespace. We need to specify them explicitly\n      // so that they don't get erronously deleted from\n      // HTML namespace.\n      const commonSvgAndHTMLElements = addToSet({}, [\n        'title',\n        'style',\n        'font',\n        'a',\n        'script',\n      ]);\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (commonSvgAndHTMLElements[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG or MathML). Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml') {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? '' : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (stringMatch(currentNode.nodeName, /[\\u0080-\\uFFFF]/)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            parentNode.insertBefore(\n              cloneNode(childNodes[i], true),\n              getNextSibling(currentNode)\n            );\n          }\n        }\n      }\n\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    if (\n      (tagName === 'noscript' || tagName === 'noembed') &&\n      regExpTest(/<\\/no(script|embed)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n      // eslint-disable-next-line no-negated-condition\n    } else if (!value) {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    } else {\n      return false;\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName.indexOf('-') > 0;\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        arrayPop(DOMPurify.removed);\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      // eslint-disable-next-line no-negated-condition\n      if (typeof dirty.toString !== 'function') {\n        throw typeErrorCreate('toString is not a function');\n      } else {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* No special handling necessary for in-place sanitization */\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : emptyHTML;\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "addToSet", "set", "array", "l", "length", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "_", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "RETURN_TRUSTED_TYPE", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "SANITIZE_DOM", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "transformCaseFunc", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "commonSvgAndHTMLElements", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "childCount", "i", "_basicCustomElementTest", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "undefined", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "serializedHTML", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;;;MACEA,iBAKEC,OALFD;MACAE,iBAIED,OAJFC;MACAC,WAGEF,OAHFE;MACAC,iBAEEH,OAFFG;MACAC,2BACEJ,OADFI;MAGIC,SAAyBL,OAAzBK;MAAQC,OAAiBN,OAAjBM;MAAMC,SAAWP,OAAXO;;aACO,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA;MAAvDC,aAAAA;MAAOC,iBAAAA;;EAEb,IAAI,CAACD,KAAL,EAAY;EACVA,UAAQ,eAAUE,GAAV,EAAeC,SAAf,EAA0BC,IAA1B,EAAgC;EACtC,WAAOF,IAAIF,KAAJ,CAAUG,SAAV,EAAqBC,IAArB,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACR,MAAL,EAAa;EACXA,WAAS,gBAAUS,CAAV,EAAa;EACpB,WAAOA,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACR,IAAL,EAAW;EACTA,SAAO,cAAUQ,CAAV,EAAa;EAClB,WAAOA,CAAP;EACD,GAFD;EAGD;;EAED,IAAI,CAACJ,SAAL,EAAgB;EACdA,cAAY,mBAAUK,IAAV,EAAgBF,IAAhB,EAAsB;EAChC,8CAAWE,IAAX,mCAAmBF,IAAnB;EACD,GAFD;EAGD;;EAED,IAAMG,eAAeC,QAAQC,MAAMC,SAAN,CAAgBC,OAAxB,CAArB;AACA,EACA,IAAMC,WAAWJ,QAAQC,MAAMC,SAAN,CAAgBG,GAAxB,CAAjB;EACA,IAAMC,YAAYN,QAAQC,MAAMC,SAAN,CAAgBK,IAAxB,CAAlB;AACA;EAEA,IAAMC,oBAAoBR,QAAQS,OAAOP,SAAP,CAAiBQ,WAAzB,CAA1B;EACA,IAAMC,cAAcX,QAAQS,OAAOP,SAAP,CAAiBU,KAAzB,CAApB;EACA,IAAMC,gBAAgBb,QAAQS,OAAOP,SAAP,CAAiBY,OAAzB,CAAtB;EACA,IAAMC,gBAAgBf,QAAQS,OAAOP,SAAP,CAAiBc,OAAzB,CAAtB;EACA,IAAMC,aAAajB,QAAQS,OAAOP,SAAP,CAAiBgB,IAAzB,CAAnB;;EAEA,IAAMC,aAAanB,QAAQoB,OAAOlB,SAAP,CAAiBmB,IAAzB,CAAnB;;EAEA,IAAMC,kBAAkBC,YAAYC,SAAZ,CAAxB;;AAEA,EAAO,SAASxB,OAAT,CAAiByB,IAAjB,EAAuB;EAC5B,SAAO,UAACC,OAAD;EAAA,sCAAa9B,IAAb;EAAaA,UAAb;EAAA;;EAAA,WAAsBJ,MAAMiC,IAAN,EAAYC,OAAZ,EAAqB9B,IAArB,CAAtB;EAAA,GAAP;EACD;;AAED,EAAO,SAAS2B,WAAT,CAAqBE,IAArB,EAA2B;EAChC,SAAO;EAAA,uCAAI7B,IAAJ;EAAIA,UAAJ;EAAA;;EAAA,WAAaH,UAAUgC,IAAV,EAAgB7B,IAAhB,CAAb;EAAA,GAAP;EACD;;EAED;AACA,EAAO,SAAS+B,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;EACnC,MAAI7C,cAAJ,EAAoB;EAClB;EACA;EACA;EACAA,mBAAe4C,GAAf,EAAoB,IAApB;EACD;;EAED,MAAIE,IAAID,MAAME,MAAd;EACA,SAAOD,GAAP,EAAY;EACV,QAAIE,UAAUH,MAAMC,CAAN,CAAd;EACA,QAAI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;EAC/B,UAAMC,YAAYzB,kBAAkBwB,OAAlB,CAAlB;EACA,UAAIC,cAAcD,OAAlB,EAA2B;EACzB;EACA,YAAI,CAAC/C,SAAS4C,KAAT,CAAL,EAAsB;EACpBA,gBAAMC,CAAN,IAAWG,SAAX;EACD;;EAEDD,kBAAUC,SAAV;EACD;EACF;;EAEDL,QAAII,OAAJ,IAAe,IAAf;EACD;;EAED,SAAOJ,GAAP;EACD;;EAED;AACA,EAAO,SAASM,KAAT,CAAeC,MAAf,EAAuB;EAC5B,MAAMC,YAAY9C,OAAO,IAAP,CAAlB;;EAEA,MAAI+C,iBAAJ;EACA,OAAKA,QAAL,IAAiBF,MAAjB,EAAyB;EACvB,QAAI3C,MAAMV,cAAN,EAAsBqD,MAAtB,EAA8B,CAACE,QAAD,CAA9B,CAAJ,EAA+C;EAC7CD,gBAAUC,QAAV,IAAsBF,OAAOE,QAAP,CAAtB;EACD;EACF;;EAED,SAAOD,SAAP;EACD;;EAED;;;;EAIA,SAASE,YAAT,CAAsBH,MAAtB,EAA8BI,IAA9B,EAAoC;EAClC,SAAOJ,WAAW,IAAlB,EAAwB;EACtB,QAAMK,OAAOrD,yBAAyBgD,MAAzB,EAAiCI,IAAjC,CAAb;EACA,QAAIC,IAAJ,EAAU;EACR,UAAIA,KAAKC,GAAT,EAAc;EACZ,eAAOzC,QAAQwC,KAAKC,GAAb,CAAP;EACD;;EAED,UAAI,OAAOD,KAAKE,KAAZ,KAAsB,UAA1B,EAAsC;EACpC,eAAO1C,QAAQwC,KAAKE,KAAb,CAAP;EACD;EACF;;EAEDP,aAASjD,eAAeiD,MAAf,CAAT;EACD;;EAED,WAASQ,aAAT,CAAuBX,OAAvB,EAAgC;EAC9BY,YAAQC,IAAR,CAAa,oBAAb,EAAmCb,OAAnC;EACA,WAAO,IAAP;EACD;;EAED,SAAOW,aAAP;EACD;;EC/HM,IAAMG,OAAO1D,OAAO,CACzB,GADyB,EAEzB,MAFyB,EAGzB,SAHyB,EAIzB,SAJyB,EAKzB,MALyB,EAMzB,SANyB,EAOzB,OAPyB,EAQzB,OARyB,EASzB,GATyB,EAUzB,KAVyB,EAWzB,KAXyB,EAYzB,KAZyB,EAazB,OAbyB,EAczB,YAdyB,EAezB,MAfyB,EAgBzB,IAhByB,EAiBzB,QAjByB,EAkBzB,QAlByB,EAmBzB,SAnByB,EAoBzB,QApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,KAvByB,EAwBzB,UAxByB,EAyBzB,SAzByB,EA0BzB,MA1ByB,EA2BzB,UA3ByB,EA4BzB,IA5ByB,EA6BzB,WA7ByB,EA8BzB,KA9ByB,EA+BzB,SA/ByB,EAgCzB,KAhCyB,EAiCzB,QAjCyB,EAkCzB,KAlCyB,EAmCzB,KAnCyB,EAoCzB,IApCyB,EAqCzB,IArCyB,EAsCzB,SAtCyB,EAuCzB,IAvCyB,EAwCzB,UAxCyB,EAyCzB,YAzCyB,EA0CzB,QA1CyB,EA2CzB,MA3CyB,EA4CzB,QA5CyB,EA6CzB,MA7CyB,EA8CzB,IA9CyB,EA+CzB,IA/CyB,EAgDzB,IAhDyB,EAiDzB,IAjDyB,EAkDzB,IAlDyB,EAmDzB,IAnDyB,EAoDzB,MApDyB,EAqDzB,QArDyB,EAsDzB,QAtDyB,EAuDzB,IAvDyB,EAwDzB,MAxDyB,EAyDzB,GAzDyB,EA0DzB,KA1DyB,EA2DzB,OA3DyB,EA4DzB,KA5DyB,EA6DzB,KA7DyB,EA8DzB,OA9DyB,EA+DzB,QA/DyB,EAgEzB,IAhEyB,EAiEzB,MAjEyB,EAkEzB,KAlEyB,EAmEzB,MAnEyB,EAoEzB,SApEyB,EAqEzB,MArEyB,EAsEzB,UAtEyB,EAuEzB,OAvEyB,EAwEzB,KAxEyB,EAyEzB,MAzEyB,EA0EzB,IA1EyB,EA2EzB,UA3EyB,EA4EzB,QA5EyB,EA6EzB,QA7EyB,EA8EzB,GA9EyB,EA+EzB,SA/EyB,EAgFzB,KAhFyB,EAiFzB,UAjFyB,EAkFzB,GAlFyB,EAmFzB,IAnFyB,EAoFzB,IApFyB,EAqFzB,MArFyB,EAsFzB,GAtFyB,EAuFzB,MAvFyB,EAwFzB,SAxFyB,EAyFzB,QAzFyB,EA0FzB,QA1FyB,EA2FzB,OA3FyB,EA4FzB,QA5FyB,EA6FzB,QA7FyB,EA8FzB,MA9FyB,EA+FzB,QA/FyB,EAgGzB,QAhGyB,EAiGzB,OAjGyB,EAkGzB,KAlGyB,EAmGzB,SAnGyB,EAoGzB,KApGyB,EAqGzB,OArGyB,EAsGzB,OAtGyB,EAuGzB,IAvGyB,EAwGzB,UAxGyB,EAyGzB,UAzGyB,EA0GzB,OA1GyB,EA2GzB,IA3GyB,EA4GzB,OA5GyB,EA6GzB,MA7GyB,EA8GzB,IA9GyB,EA+GzB,OA/GyB,EAgHzB,IAhHyB,EAiHzB,GAjHyB,EAkHzB,IAlHyB,EAmHzB,KAnHyB,EAoHzB,OApHyB,EAqHzB,KArHyB,CAAP,CAAb;;EAwHP;AACA,EAAO,IAAM2D,MAAM3D,OAAO,CACxB,KADwB,EAExB,GAFwB,EAGxB,UAHwB,EAIxB,aAJwB,EAKxB,cALwB,EAMxB,cANwB,EAOxB,eAPwB,EAQxB,kBARwB,EASxB,QATwB,EAUxB,UAVwB,EAWxB,MAXwB,EAYxB,MAZwB,EAaxB,SAbwB,EAcxB,QAdwB,EAexB,MAfwB,EAgBxB,GAhBwB,EAiBxB,OAjBwB,EAkBxB,UAlBwB,EAmBxB,OAnBwB,EAoBxB,OApBwB,EAqBxB,MArBwB,EAsBxB,gBAtBwB,EAuBxB,QAvBwB,EAwBxB,MAxBwB,EAyBxB,UAzBwB,EA0BxB,OA1BwB,EA2BxB,MA3BwB,EA4BxB,SA5BwB,EA6BxB,SA7BwB,EA8BxB,UA9BwB,EA+BxB,gBA/BwB,EAgCxB,MAhCwB,EAiCxB,MAjCwB,EAkCxB,OAlCwB,EAmCxB,QAnCwB,EAoCxB,QApCwB,EAqCxB,MArCwB,EAsCxB,UAtCwB,EAuCxB,OAvCwB,EAwCxB,MAxCwB,EAyCxB,OAzCwB,EA0CxB,MA1CwB,EA2CxB,OA3CwB,CAAP,CAAZ;;AA8CP,EAAO,IAAM4D,aAAa5D,OAAO,CAC/B,SAD+B,EAE/B,eAF+B,EAG/B,qBAH+B,EAI/B,aAJ+B,EAK/B,kBAL+B,EAM/B,mBAN+B,EAO/B,mBAP+B,EAQ/B,gBAR+B,EAS/B,SAT+B,EAU/B,SAV+B,EAW/B,SAX+B,EAY/B,SAZ+B,EAa/B,SAb+B,EAc/B,gBAd+B,EAe/B,SAf+B,EAgB/B,SAhB+B,EAiB/B,aAjB+B,EAkB/B,cAlB+B,EAmB/B,UAnB+B,EAoB/B,cApB+B,EAqB/B,oBArB+B,EAsB/B,aAtB+B,EAuB/B,QAvB+B,EAwB/B,cAxB+B,CAAP,CAAnB;;EA2BP;EACA;EACA;EACA;AACA,EAAO,IAAM6D,gBAAgB7D,OAAO,CAClC,SADkC,EAElC,eAFkC,EAGlC,QAHkC,EAIlC,SAJkC,EAKlC,cALkC,EAMlC,WANkC,EAOlC,kBAPkC,EAQlC,gBARkC,EASlC,eATkC,EAUlC,eAVkC,EAWlC,eAXkC,EAYlC,OAZkC,EAalC,WAbkC,EAclC,MAdkC,EAelC,cAfkC,EAgBlC,WAhBkC,EAiBlC,SAjBkC,EAkBlC,eAlBkC,EAmBlC,QAnBkC,EAoBlC,KApBkC,EAqBlC,YArBkC,EAsBlC,SAtBkC,EAuBlC,KAvBkC,CAAP,CAAtB;;AA0BP,EAAO,IAAM8D,SAAS9D,OAAO,CAC3B,MAD2B,EAE3B,UAF2B,EAG3B,QAH2B,EAI3B,SAJ2B,EAK3B,OAL2B,EAM3B,QAN2B,EAO3B,IAP2B,EAQ3B,YAR2B,EAS3B,eAT2B,EAU3B,IAV2B,EAW3B,IAX2B,EAY3B,OAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,MAhB2B,EAiB3B,IAjB2B,EAkB3B,QAlB2B,EAmB3B,OAnB2B,EAoB3B,QApB2B,EAqB3B,MArB2B,EAsB3B,MAtB2B,EAuB3B,SAvB2B,EAwB3B,QAxB2B,EAyB3B,KAzB2B,EA0B3B,OA1B2B,EA2B3B,KA3B2B,EA4B3B,QA5B2B,EA6B3B,YA7B2B,CAAP,CAAf;;EAgCP;EACA;AACA,EAAO,IAAM+D,mBAAmB/D,OAAO,CACrC,SADqC,EAErC,aAFqC,EAGrC,YAHqC,EAIrC,UAJqC,EAKrC,WALqC,EAMrC,SANqC,EAOrC,SAPqC,EAQrC,QARqC,EASrC,QATqC,EAUrC,OAVqC,EAWrC,WAXqC,EAYrC,YAZqC,EAarC,gBAbqC,EAcrC,aAdqC,EAerC,MAfqC,CAAP,CAAzB;;AAkBP,EAAO,IAAMgE,OAAOhE,OAAO,CAAC,OAAD,CAAP,CAAb;;ECpRA,IAAM0D,SAAO1D,OAAO,CACzB,QADyB,EAEzB,QAFyB,EAGzB,OAHyB,EAIzB,KAJyB,EAKzB,gBALyB,EAMzB,cANyB,EAOzB,sBAPyB,EAQzB,UARyB,EASzB,YATyB,EAUzB,SAVyB,EAWzB,QAXyB,EAYzB,SAZyB,EAazB,aAbyB,EAczB,aAdyB,EAezB,SAfyB,EAgBzB,MAhByB,EAiBzB,OAjByB,EAkBzB,OAlByB,EAmBzB,OAnByB,EAoBzB,MApByB,EAqBzB,SArByB,EAsBzB,UAtByB,EAuBzB,cAvByB,EAwBzB,QAxByB,EAyBzB,aAzByB,EA0BzB,UA1ByB,EA2BzB,UA3ByB,EA4BzB,SA5ByB,EA6BzB,KA7ByB,EA8BzB,UA9ByB,EA+BzB,yBA/ByB,EAgCzB,uBAhCyB,EAiCzB,UAjCyB,EAkCzB,WAlCyB,EAmCzB,SAnCyB,EAoCzB,cApCyB,EAqCzB,MArCyB,EAsCzB,KAtCyB,EAuCzB,SAvCyB,EAwCzB,QAxCyB,EAyCzB,QAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,UA5CyB,EA6CzB,IA7CyB,EA8CzB,WA9CyB,EA+CzB,WA/CyB,EAgDzB,OAhDyB,EAiDzB,MAjDyB,EAkDzB,OAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,SArDyB,EAsDzB,MAtDyB,EAuDzB,KAvDyB,EAwDzB,KAxDyB,EAyDzB,WAzDyB,EA0DzB,OA1DyB,EA2DzB,QA3DyB,EA4DzB,KA5DyB,EA6DzB,WA7DyB,EA8DzB,UA9DyB,EA+DzB,OA/DyB,EAgEzB,MAhEyB,EAiEzB,OAjEyB,EAkEzB,SAlEyB,EAmEzB,YAnEyB,EAoEzB,QApEyB,EAqEzB,MArEyB,EAsEzB,SAtEyB,EAuEzB,SAvEyB,EAwEzB,aAxEyB,EAyEzB,aAzEyB,EA0EzB,QA1EyB,EA2EzB,SA3EyB,EA4EzB,SA5EyB,EA6EzB,YA7EyB,EA8EzB,UA9EyB,EA+EzB,KA/EyB,EAgFzB,UAhFyB,EAiFzB,KAjFyB,EAkFzB,UAlFyB,EAmFzB,MAnFyB,EAoFzB,MApFyB,EAqFzB,SArFyB,EAsFzB,YAtFyB,EAuFzB,OAvFyB,EAwFzB,UAxFyB,EAyFzB,OAzFyB,EA0FzB,MA1FyB,EA2FzB,OA3FyB,EA4FzB,MA5FyB,EA6FzB,SA7FyB,EA8FzB,OA9FyB,EA+FzB,KA/FyB,EAgGzB,QAhGyB,EAiGzB,MAjGyB,EAkGzB,OAlGyB,EAmGzB,SAnGyB,EAoGzB,UApGyB,EAqGzB,OArGyB,EAsGzB,WAtGyB,EAuGzB,MAvGyB,EAwGzB,QAxGyB,EAyGzB,QAzGyB,EA0GzB,OA1GyB,EA2GzB,OA3GyB,EA4GzB,OA5GyB,EA6GzB,MA7GyB,CAAP,CAAb;;AAgHP,EAAO,IAAM2D,QAAM3D,OAAO,CACxB,eADwB,EAExB,YAFwB,EAGxB,UAHwB,EAIxB,oBAJwB,EAKxB,QALwB,EAMxB,eANwB,EAOxB,eAPwB,EAQxB,SARwB,EASxB,eATwB,EAUxB,gBAVwB,EAWxB,OAXwB,EAYxB,MAZwB,EAaxB,IAbwB,EAcxB,OAdwB,EAexB,MAfwB,EAgBxB,eAhBwB,EAiBxB,WAjBwB,EAkBxB,WAlBwB,EAmBxB,OAnBwB,EAoBxB,qBApBwB,EAqBxB,6BArBwB,EAsBxB,eAtBwB,EAuBxB,iBAvBwB,EAwBxB,IAxBwB,EAyBxB,IAzBwB,EA0BxB,GA1BwB,EA2BxB,IA3BwB,EA4BxB,IA5BwB,EA6BxB,iBA7BwB,EA8BxB,WA9BwB,EA+BxB,SA/BwB,EAgCxB,SAhCwB,EAiCxB,KAjCwB,EAkCxB,UAlCwB,EAmCxB,WAnCwB,EAoCxB,KApCwB,EAqCxB,MArCwB,EAsCxB,cAtCwB,EAuCxB,WAvCwB,EAwCxB,QAxCwB,EAyCxB,aAzCwB,EA0CxB,aA1CwB,EA2CxB,eA3CwB,EA4CxB,aA5CwB,EA6CxB,WA7CwB,EA8CxB,kBA9CwB,EA+CxB,cA/CwB,EAgDxB,YAhDwB,EAiDxB,cAjDwB,EAkDxB,aAlDwB,EAmDxB,IAnDwB,EAoDxB,IApDwB,EAqDxB,IArDwB,EAsDxB,IAtDwB,EAuDxB,YAvDwB,EAwDxB,UAxDwB,EAyDxB,eAzDwB,EA0DxB,mBA1DwB,EA2DxB,QA3DwB,EA4DxB,MA5DwB,EA6DxB,IA7DwB,EA8DxB,iBA9DwB,EA+DxB,IA/DwB,EAgExB,KAhEwB,EAiExB,GAjEwB,EAkExB,IAlEwB,EAmExB,IAnEwB,EAoExB,IApEwB,EAqExB,IArEwB,EAsExB,SAtEwB,EAuExB,WAvEwB,EAwExB,YAxEwB,EAyExB,UAzEwB,EA0ExB,MA1EwB,EA2ExB,cA3EwB,EA4ExB,gBA5EwB,EA6ExB,cA7EwB,EA8ExB,kBA9EwB,EA+ExB,gBA/EwB,EAgFxB,OAhFwB,EAiFxB,YAjFwB,EAkFxB,YAlFwB,EAmFxB,cAnFwB,EAoFxB,cApFwB,EAqFxB,aArFwB,EAsFxB,aAtFwB,EAuFxB,kBAvFwB,EAwFxB,WAxFwB,EAyFxB,KAzFwB,EA0FxB,MA1FwB,EA2FxB,OA3FwB,EA4FxB,QA5FwB,EA6FxB,MA7FwB,EA8FxB,KA9FwB,EA+FxB,MA/FwB,EAgGxB,YAhGwB,EAiGxB,QAjGwB,EAkGxB,UAlGwB,EAmGxB,SAnGwB,EAoGxB,OApGwB,EAqGxB,QArGwB,EAsGxB,aAtGwB,EAuGxB,QAvGwB,EAwGxB,UAxGwB,EAyGxB,aAzGwB,EA0GxB,MA1GwB,EA2GxB,YA3GwB,EA4GxB,qBA5GwB,EA6GxB,kBA7GwB,EA8GxB,cA9GwB,EA+GxB,QA/GwB,EAgHxB,eAhHwB,EAiHxB,qBAjHwB,EAkHxB,gBAlHwB,EAmHxB,GAnHwB,EAoHxB,IApHwB,EAqHxB,IArHwB,EAsHxB,QAtHwB,EAuHxB,MAvHwB,EAwHxB,MAxHwB,EAyHxB,aAzHwB,EA0HxB,WA1HwB,EA2HxB,SA3HwB,EA4HxB,QA5HwB,EA6HxB,QA7HwB,EA8HxB,OA9HwB,EA+HxB,MA/HwB,EAgIxB,iBAhIwB,EAiIxB,kBAjIwB,EAkIxB,kBAlIwB,EAmIxB,cAnIwB,EAoIxB,aApIwB,EAqIxB,cArIwB,EAsIxB,aAtIwB,EAuIxB,YAvIwB,EAwIxB,cAxIwB,EAyIxB,kBAzIwB,EA0IxB,mBA1IwB,EA2IxB,gBA3IwB,EA4IxB,iBA5IwB,EA6IxB,mBA7IwB,EA8IxB,gBA9IwB,EA+IxB,QA/IwB,EAgJxB,cAhJwB,EAiJxB,OAjJwB,EAkJxB,cAlJwB,EAmJxB,gBAnJwB,EAoJxB,UApJwB,EAqJxB,SArJwB,EAsJxB,SAtJwB,EAuJxB,WAvJwB,EAwJxB,aAxJwB,EAyJxB,iBAzJwB,EA0JxB,gBA1JwB,EA2JxB,YA3JwB,EA4JxB,MA5JwB,EA6JxB,IA7JwB,EA8JxB,IA9JwB,EA+JxB,SA/JwB,EAgKxB,QAhKwB,EAiKxB,SAjKwB,EAkKxB,YAlKwB,EAmKxB,SAnKwB,EAoKxB,YApKwB,EAqKxB,eArKwB,EAsKxB,eAtKwB,EAuKxB,OAvKwB,EAwKxB,cAxKwB,EAyKxB,MAzKwB,EA0KxB,cA1KwB,EA2KxB,kBA3KwB,EA4KxB,kBA5KwB,EA6KxB,GA7KwB,EA8KxB,IA9KwB,EA+KxB,IA/KwB,EAgLxB,OAhLwB,EAiLxB,GAjLwB,EAkLxB,IAlLwB,EAmLxB,IAnLwB,EAoLxB,GApLwB,EAqLxB,YArLwB,CAAP,CAAZ;;AAwLP,EAAO,IAAM8D,WAAS9D,OAAO,CAC3B,QAD2B,EAE3B,aAF2B,EAG3B,OAH2B,EAI3B,UAJ2B,EAK3B,OAL2B,EAM3B,cAN2B,EAO3B,aAP2B,EAQ3B,YAR2B,EAS3B,YAT2B,EAU3B,OAV2B,EAW3B,KAX2B,EAY3B,SAZ2B,EAa3B,cAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,OAhB2B,EAiB3B,QAjB2B,EAkB3B,MAlB2B,EAmB3B,IAnB2B,EAoB3B,SApB2B,EAqB3B,QArB2B,EAsB3B,eAtB2B,EAuB3B,QAvB2B,EAwB3B,QAxB2B,EAyB3B,gBAzB2B,EA0B3B,WA1B2B,EA2B3B,UA3B2B,EA4B3B,aA5B2B,EA6B3B,SA7B2B,EA8B3B,SA9B2B,EA+B3B,eA/B2B,EAgC3B,UAhC2B,EAiC3B,UAjC2B,EAkC3B,MAlC2B,EAmC3B,UAnC2B,EAoC3B,UApC2B,EAqC3B,YArC2B,EAsC3B,SAtC2B,EAuC3B,QAvC2B,EAwC3B,QAxC2B,EAyC3B,aAzC2B,EA0C3B,eA1C2B,EA2C3B,sBA3C2B,EA4C3B,WA5C2B,EA6C3B,WA7C2B,EA8C3B,YA9C2B,EA+C3B,UA/C2B,EAgD3B,gBAhD2B,EAiD3B,gBAjD2B,EAkD3B,WAlD2B,EAmD3B,SAnD2B,EAoD3B,OApD2B,EAqD3B,OArD2B,CAAP,CAAf;;AAwDP,EAAO,IAAMiE,MAAMjE,OAAO,CACxB,YADwB,EAExB,QAFwB,EAGxB,aAHwB,EAIxB,WAJwB,EAKxB,aALwB,CAAP,CAAZ;;EChWP;AACA,EAAO,IAAMkE,gBAAgBjE,KAAK,2BAAL,CAAtB;AACP,EAAO,IAAMkE,WAAWlE,KAAK,uBAAL,CAAjB;AACP,EAAO,IAAMmE,YAAYnE,KAAK,4BAAL,CAAlB;AACP,EAAO,IAAMoE,YAAYpE,KAAK,gBAAL,CAAlB;AACP,EAAO,IAAMqE,iBAAiBrE,KAC5B,uFAD4B;EAAA,CAAvB;AAGP,EAAO,IAAMsE,oBAAoBtE,KAAK,uBAAL,CAA1B;AACP,EAAO,IAAMuE,kBAAkBvE,KAC7B,6DAD6B;EAAA,CAAxB;;;;;;ECSP,IAAMwE,YAAY,SAAZA,SAAY;EAAA,SAAO,OAAOC,MAAP,KAAkB,WAAlB,GAAgC,IAAhC,GAAuCA,MAA9C;EAAA,CAAlB;;EAEA;;;;;;;;EAQA,IAAMC,4BAA4B,SAA5BA,yBAA4B,CAAUC,YAAV,EAAwBC,QAAxB,EAAkC;EAClE,MACE,QAAOD,YAAP,yCAAOA,YAAP,OAAwB,QAAxB,IACA,OAAOA,aAAaE,YAApB,KAAqC,UAFvC,EAGE;EACA,WAAO,IAAP;EACD;;EAED;EACA;EACA;EACA,MAAIC,SAAS,IAAb;EACA,MAAMC,YAAY,uBAAlB;EACA,MACEH,SAASI,aAAT,IACAJ,SAASI,aAAT,CAAuBC,YAAvB,CAAoCF,SAApC,CAFF,EAGE;EACAD,aAASF,SAASI,aAAT,CAAuBE,YAAvB,CAAoCH,SAApC,CAAT;EACD;;EAED,MAAMI,aAAa,eAAeL,SAAS,MAAMA,MAAf,GAAwB,EAAvC,CAAnB;;EAEA,MAAI;EACF,WAAOH,aAAaE,YAAb,CAA0BM,UAA1B,EAAsC;EAC3CC,gBAD2C,sBAChC3B,OADgC,EAC1B;EACf,eAAOA,OAAP;EACD;EAH0C,KAAtC,CAAP;EAKD,GAND,CAME,OAAO4B,CAAP,EAAU;EACV;EACA;EACA;EACA9B,YAAQC,IAAR,CACE,yBAAyB2B,UAAzB,GAAsC,wBADxC;EAGA,WAAO,IAAP;EACD;EACF,CArCD;;EAuCA,SAASG,eAAT,GAA+C;EAAA,MAAtBb,MAAsB,uEAAbD,WAAa;;EAC7C,MAAMe,YAAY,SAAZA,SAAY,CAACC,IAAD;EAAA,WAAUF,gBAAgBE,IAAhB,CAAV;EAAA,GAAlB;;EAEA;;;;EAIAD,YAAUE,OAAV,GAAoBC,OAApB;;EAEA;;;;EAIAH,YAAUI,OAAV,GAAoB,EAApB;;EAEA,MAAI,CAAClB,MAAD,IAAW,CAACA,OAAOG,QAAnB,IAA+BH,OAAOG,QAAP,CAAgBgB,QAAhB,KAA6B,CAAhE,EAAmE;EACjE;EACA;EACAL,cAAUM,WAAV,GAAwB,KAAxB;;EAEA,WAAON,SAAP;EACD;;EAED,MAAMO,mBAAmBrB,OAAOG,QAAhC;;EAvB6C,MAyBvCA,QAzBuC,GAyB1BH,MAzB0B,CAyBvCG,QAzBuC;EAAA,MA2B3CmB,gBA3B2C,GAoCzCtB,MApCyC,CA2B3CsB,gBA3B2C;EAAA,MA4B3CC,mBA5B2C,GAoCzCvB,MApCyC,CA4B3CuB,mBA5B2C;EAAA,MA6B3CC,IA7B2C,GAoCzCxB,MApCyC,CA6B3CwB,IA7B2C;EAAA,MA8B3CC,OA9B2C,GAoCzCzB,MApCyC,CA8B3CyB,OA9B2C;EAAA,MA+B3CC,UA/B2C,GAoCzC1B,MApCyC,CA+B3C0B,UA/B2C;EAAA,6BAoCzC1B,MApCyC,CAgC3C2B,YAhC2C;EAAA,MAgC3CA,YAhC2C,wCAgC5B3B,OAAO2B,YAAP,IAAuB3B,OAAO4B,eAhCF;EAAA,MAiC3CC,eAjC2C,GAoCzC7B,MApCyC,CAiC3C6B,eAjC2C;EAAA,MAkC3CC,SAlC2C,GAoCzC9B,MApCyC,CAkC3C8B,SAlC2C;EAAA,MAmC3C5B,YAnC2C,GAoCzCF,MApCyC,CAmC3CE,YAnC2C;;;EAsC7C,MAAM6B,mBAAmBN,QAAQrF,SAAjC;;EAEA,MAAM4F,YAAYxD,aAAauD,gBAAb,EAA+B,WAA/B,CAAlB;EACA,MAAME,iBAAiBzD,aAAauD,gBAAb,EAA+B,aAA/B,CAAvB;EACA,MAAMG,gBAAgB1D,aAAauD,gBAAb,EAA+B,YAA/B,CAAtB;EACA,MAAMI,gBAAgB3D,aAAauD,gBAAb,EAA+B,YAA/B,CAAtB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,MAAI,OAAOR,mBAAP,KAA+B,UAAnC,EAA+C;EAC7C,QAAMa,WAAWjC,SAASkC,aAAT,CAAuB,UAAvB,CAAjB;EACA,QAAID,SAASE,OAAT,IAAoBF,SAASE,OAAT,CAAiBC,aAAzC,EAAwD;EACtDpC,iBAAWiC,SAASE,OAAT,CAAiBC,aAA5B;EACD;EACF;;EAED,MAAMC,qBAAqBvC,0BACzBC,YADyB,EAEzBmB,gBAFyB,CAA3B;EAIA,MAAMoB,YACJD,sBAAsBE,mBAAtB,GACIF,mBAAmB7B,UAAnB,CAA8B,EAA9B,CADJ,GAEI,EAHN;;EA9D6C,kBAwEzCR,QAxEyC;EAAA,MAoE3CwC,cApE2C,aAoE3CA,cApE2C;EAAA,MAqE3CC,kBArE2C,aAqE3CA,kBArE2C;EAAA,MAsE3CC,sBAtE2C,aAsE3CA,sBAtE2C;EAAA,MAuE3CC,oBAvE2C,aAuE3CA,oBAvE2C;EAAA,MAyErCC,UAzEqC,GAyEtB1B,gBAzEsB,CAyErC0B,UAzEqC;;;EA2E7C,MAAIC,eAAe,EAAnB;EACA,MAAI;EACFA,mBAAe5E,MAAM+B,QAAN,EAAgB6C,YAAhB,GAA+B7C,SAAS6C,YAAxC,GAAuD,EAAtE;EACD,GAFD,CAEE,OAAOpC,CAAP,EAAU;;EAEZ,MAAIqC,QAAQ,EAAZ;;EAEA;;;EAGAnC,YAAUM,WAAV,GACE,OAAOe,aAAP,KAAyB,UAAzB,IACAQ,cADA,IAEA,OAAOA,eAAeO,kBAAtB,KAA6C,WAF7C,IAGAF,iBAAiB,CAJnB;;EArF6C,MA4F3CxD,gBA5F2C,GAkGzC2D,aAlGyC;EAAA,MA6F3C1D,WA7F2C,GAkGzC0D,QAlGyC;EAAA,MA8F3CzD,YA9F2C,GAkGzCyD,SAlGyC;EAAA,MA+F3CxD,YA/F2C,GAkGzCwD,SAlGyC;EAAA,MAgG3CtD,oBAhG2C,GAkGzCsD,iBAlGyC;EAAA,MAiG3CrD,kBAjG2C,GAkGzCqD,eAlGyC;EAAA,MAoGvCvD,iBApGuC,GAoGpBuD,cApGoB;;EAsG7C;;;;;EAKA;;EACA,MAAIC,eAAe,IAAnB;EACA,MAAMC,uBAAuBxF,SAAS,EAAT,iCACxByF,IADwB,wBAExBA,GAFwB,wBAGxBA,UAHwB,wBAIxBA,MAJwB,wBAKxBA,IALwB,GAA7B;;EAQA;EACA,MAAIC,eAAe,IAAnB;EACA,MAAMC,uBAAuB3F,SAAS,EAAT,iCACxB4F,MADwB,wBAExBA,KAFwB,wBAGxBA,QAHwB,wBAIxBA,GAJwB,GAA7B;;EAOA;;;;;;EAMA,MAAMC,0BAA0BzI,OAAOM,IAAP,CAC9BN,OAAOO,MAAP,CAAc,IAAd,EAAoB;EAClBmI,kBAAc;EACZC,gBAAU,IADE;EAEZC,oBAAc,KAFF;EAGZC,kBAAY,IAHA;EAIZlF,aAAO;EAJK,KADI;EAOlBmF,wBAAoB;EAClBH,gBAAU,IADQ;EAElBC,oBAAc,KAFI;EAGlBC,kBAAY,IAHM;EAIlBlF,aAAO;EAJW,KAPF;EAalBoF,oCAAgC;EAC9BJ,gBAAU,IADoB;EAE9BC,oBAAc,KAFgB;EAG9BC,kBAAY,IAHkB;EAI9BlF,aAAO;EAJuB;EAbd,GAApB,CAD8B,CAAhC;;EAuBA;EACA,MAAIqF,cAAc,IAAlB;;EAEA;EACA,MAAIC,cAAc,IAAlB;;EAEA;EACA,MAAIC,kBAAkB,IAAtB;;EAEA;EACA,MAAIC,kBAAkB,IAAtB;;EAEA;EACA,MAAIC,0BAA0B,KAA9B;;EAEA;;;EAGA,MAAIC,qBAAqB,KAAzB;;EAEA;EACA,MAAIC,iBAAiB,KAArB;;EAEA;EACA,MAAIC,aAAa,KAAjB;;EAEA;;EAEA,MAAIC,aAAa,KAAjB;;EAEA;;;;EAIA,MAAIC,aAAa,KAAjB;;EAEA;;EAEA,MAAIC,sBAAsB,KAA1B;;EAEA;;EAEA,MAAIjC,sBAAsB,KAA1B;;EAEA;EACA,MAAIkC,eAAe,IAAnB;;EAEA;EACA,MAAIC,eAAe,IAAnB;;EAEA;;EAEA,MAAIC,WAAW,KAAf;;EAEA;EACA,MAAIC,eAAe,EAAnB;;EAEA;EACA,MAAIC,kBAAkB,IAAtB;EACA,MAAMC,0BAA0BpH,SAAS,EAAT,EAAa,CAC3C,gBAD2C,EAE3C,OAF2C,EAG3C,UAH2C,EAI3C,MAJ2C,EAK3C,eAL2C,EAM3C,MAN2C,EAO3C,QAP2C,EAQ3C,MAR2C,EAS3C,IAT2C,EAU3C,IAV2C,EAW3C,IAX2C,EAY3C,IAZ2C,EAa3C,OAb2C,EAc3C,SAd2C,EAe3C,UAf2C,EAgB3C,UAhB2C,EAiB3C,WAjB2C,EAkB3C,QAlB2C,EAmB3C,OAnB2C,EAoB3C,KApB2C,EAqB3C,UArB2C,EAsB3C,OAtB2C,EAuB3C,OAvB2C,EAwB3C,OAxB2C,EAyB3C,KAzB2C,CAAb,CAAhC;;EA4BA;EACA,MAAIqH,gBAAgB,IAApB;EACA,MAAMC,wBAAwBtH,SAAS,EAAT,EAAa,CACzC,OADyC,EAEzC,OAFyC,EAGzC,KAHyC,EAIzC,QAJyC,EAKzC,OALyC,EAMzC,OANyC,CAAb,CAA9B;;EASA;EACA,MAAIuH,sBAAsB,IAA1B;EACA,MAAMC,8BAA8BxH,SAAS,EAAT,EAAa,CAC/C,KAD+C,EAE/C,OAF+C,EAG/C,KAH+C,EAI/C,IAJ+C,EAK/C,OAL+C,EAM/C,MAN+C,EAO/C,SAP+C,EAQ/C,aAR+C,EAS/C,MAT+C,EAU/C,SAV+C,EAW/C,OAX+C,EAY/C,OAZ+C,EAa/C,OAb+C,EAc/C,OAd+C,CAAb,CAApC;;EAiBA,MAAMyH,mBAAmB,oCAAzB;EACA,MAAMC,gBAAgB,4BAAtB;EACA,MAAMC,iBAAiB,8BAAvB;EACA;EACA,MAAIC,YAAYD,cAAhB;EACA,MAAIE,iBAAiB,KAArB;;EAEA;EACA,MAAIC,0BAAJ;EACA,MAAMC,+BAA+B,CAAC,uBAAD,EAA0B,WAA1B,CAArC;EACA,MAAMC,4BAA4B,WAAlC;EACA,MAAIC,0BAAJ;;EAEA;EACA,MAAIC,SAAS,IAAb;;EAEA;EACA;;EAEA,MAAMC,cAAc7F,SAASkC,aAAT,CAAuB,MAAvB,CAApB;;EAEA,MAAM4D,oBAAoB,SAApBA,iBAAoB,CAAUC,SAAV,EAAqB;EAC7C,WAAOA,qBAAqB5I,MAArB,IAA+B4I,qBAAqBC,QAA3D;EACD,GAFD;;EAIA;;;;;EAKA;EACA,MAAMC,eAAe,SAAfA,YAAe,CAAUC,GAAV,EAAe;EAClC,QAAIN,UAAUA,WAAWM,GAAzB,EAA8B;EAC5B;EACD;;EAED;EACA,QAAI,CAACA,GAAD,IAAQ,QAAOA,GAAP,yCAAOA,GAAP,OAAe,QAA3B,EAAqC;EACnCA,YAAM,EAAN;EACD;;EAED;EACAA,UAAMjI,MAAMiI,GAAN,CAAN;;EAEA;EACAjD,mBACE,kBAAkBiD,GAAlB,GACIxI,SAAS,EAAT,EAAawI,IAAIjD,YAAjB,CADJ,GAEIC,oBAHN;EAIAE,mBACE,kBAAkB8C,GAAlB,GACIxI,SAAS,EAAT,EAAawI,IAAI9C,YAAjB,CADJ,GAEIC,oBAHN;EAIA4B,0BACE,uBAAuBiB,GAAvB,GACIxI,SAASO,MAAMiH,2BAAN,CAAT,EAA6CgB,IAAIC,iBAAjD,CADJ,GAEIjB,2BAHN;EAIAH,oBACE,uBAAuBmB,GAAvB,GACIxI,SAASO,MAAM+G,qBAAN,CAAT,EAAuCkB,IAAIE,iBAA3C,CADJ,GAEIpB,qBAHN;EAIAH,sBACE,qBAAqBqB,GAArB,GACIxI,SAAS,EAAT,EAAawI,IAAIrB,eAAjB,CADJ,GAEIC,uBAHN;EAIAhB,kBAAc,iBAAiBoC,GAAjB,GAAuBxI,SAAS,EAAT,EAAawI,IAAIpC,WAAjB,CAAvB,GAAuD,EAArE;EACAC,kBAAc,iBAAiBmC,GAAjB,GAAuBxI,SAAS,EAAT,EAAawI,IAAInC,WAAjB,CAAvB,GAAuD,EAArE;EACAa,mBAAe,kBAAkBsB,GAAlB,GAAwBA,IAAItB,YAA5B,GAA2C,KAA1D;EACAZ,sBAAkBkC,IAAIlC,eAAJ,KAAwB,KAA1C,CArCkC;EAsClCC,sBAAkBiC,IAAIjC,eAAJ,KAAwB,KAA1C,CAtCkC;EAuClCC,8BAA0BgC,IAAIhC,uBAAJ,IAA+B,KAAzD,CAvCkC;EAwClCC,yBAAqB+B,IAAI/B,kBAAJ,IAA0B,KAA/C,CAxCkC;EAyClCC,qBAAiB8B,IAAI9B,cAAJ,IAAsB,KAAvC,CAzCkC;EA0ClCG,iBAAa2B,IAAI3B,UAAJ,IAAkB,KAA/B,CA1CkC;EA2ClCC,0BAAsB0B,IAAI1B,mBAAJ,IAA2B,KAAjD,CA3CkC;EA4ClCjC,0BAAsB2D,IAAI3D,mBAAJ,IAA2B,KAAjD,CA5CkC;EA6ClC+B,iBAAa4B,IAAI5B,UAAJ,IAAkB,KAA/B,CA7CkC;EA8ClCG,mBAAeyB,IAAIzB,YAAJ,KAAqB,KAApC,CA9CkC;EA+ClCC,mBAAewB,IAAIxB,YAAJ,KAAqB,KAApC,CA/CkC;EAgDlCC,eAAWuB,IAAIvB,QAAJ,IAAgB,KAA3B,CAhDkC;EAiDlClF,wBAAiByG,IAAIG,kBAAJ,IAA0B5G,iBAA3C;EACA6F,gBAAYY,IAAIZ,SAAJ,IAAiBD,cAA7B;EACA,QACEa,IAAI3C,uBAAJ,IACAuC,kBAAkBI,IAAI3C,uBAAJ,CAA4BC,YAA9C,CAFF,EAGE;EACAD,8BAAwBC,YAAxB,GACE0C,IAAI3C,uBAAJ,CAA4BC,YAD9B;EAED;;EAED,QACE0C,IAAI3C,uBAAJ,IACAuC,kBAAkBI,IAAI3C,uBAAJ,CAA4BK,kBAA9C,CAFF,EAGE;EACAL,8BAAwBK,kBAAxB,GACEsC,IAAI3C,uBAAJ,CAA4BK,kBAD9B;EAED;;EAED,QACEsC,IAAI3C,uBAAJ,IACA,OAAO2C,IAAI3C,uBAAJ,CAA4BM,8BAAnC,KACE,SAHJ,EAIE;EACAN,8BAAwBM,8BAAxB,GACEqC,IAAI3C,uBAAJ,CAA4BM,8BAD9B;EAED;;EAED2B;EACE;EACAC,iCAA6B1I,OAA7B,CAAqCmJ,IAAIV,iBAAzC,MAAgE,CAAC,CAAjE,GACKA,oBAAoBE,yBADzB,GAEKF,oBAAoBU,IAAIV,iBAJ/B;;EAMA;EACAG,wBACEH,sBAAsB,uBAAtB,GACI,UAAC5J,CAAD;EAAA,aAAOA,CAAP;EAAA,KADJ,GAEIW,iBAHN;;EAKA,QAAI4H,kBAAJ,EAAwB;EACtBF,wBAAkB,KAAlB;EACD;;EAED,QAAIO,mBAAJ,EAAyB;EACvBD,mBAAa,IAAb;EACD;;EAED;EACA,QAAIK,YAAJ,EAAkB;EAChB3B,qBAAevF,SAAS,EAAT,iCAAiByF,IAAjB,GAAf;EACAC,qBAAe,EAAf;EACA,UAAIwB,aAAa/F,IAAb,KAAsB,IAA1B,EAAgC;EAC9BnB,iBAASuF,YAAT,EAAuBE,IAAvB;EACAzF,iBAAS0F,YAAT,EAAuBE,MAAvB;EACD;;EAED,UAAIsB,aAAa9F,GAAb,KAAqB,IAAzB,EAA+B;EAC7BpB,iBAASuF,YAAT,EAAuBE,GAAvB;EACAzF,iBAAS0F,YAAT,EAAuBE,KAAvB;EACA5F,iBAAS0F,YAAT,EAAuBE,GAAvB;EACD;;EAED,UAAIsB,aAAa7F,UAAb,KAA4B,IAAhC,EAAsC;EACpCrB,iBAASuF,YAAT,EAAuBE,UAAvB;EACAzF,iBAAS0F,YAAT,EAAuBE,KAAvB;EACA5F,iBAAS0F,YAAT,EAAuBE,GAAvB;EACD;;EAED,UAAIsB,aAAa3F,MAAb,KAAwB,IAA5B,EAAkC;EAChCvB,iBAASuF,YAAT,EAAuBE,MAAvB;EACAzF,iBAAS0F,YAAT,EAAuBE,QAAvB;EACA5F,iBAAS0F,YAAT,EAAuBE,GAAvB;EACD;EACF;;EAED;EACA,QAAI4C,IAAII,QAAR,EAAkB;EAChB,UAAIrD,iBAAiBC,oBAArB,EAA2C;EACzCD,uBAAehF,MAAMgF,YAAN,CAAf;EACD;;EAEDvF,eAASuF,YAAT,EAAuBiD,IAAII,QAA3B;EACD;;EAED,QAAIJ,IAAIK,QAAR,EAAkB;EAChB,UAAInD,iBAAiBC,oBAArB,EAA2C;EACzCD,uBAAenF,MAAMmF,YAAN,CAAf;EACD;;EAED1F,eAAS0F,YAAT,EAAuB8C,IAAIK,QAA3B;EACD;;EAED,QAAIL,IAAIC,iBAAR,EAA2B;EACzBzI,eAASuH,mBAAT,EAA8BiB,IAAIC,iBAAlC;EACD;;EAED,QAAID,IAAIrB,eAAR,EAAyB;EACvB,UAAIA,oBAAoBC,uBAAxB,EAAiD;EAC/CD,0BAAkB5G,MAAM4G,eAAN,CAAlB;EACD;;EAEDnH,eAASmH,eAAT,EAA0BqB,IAAIrB,eAA9B;EACD;;EAED;EACA,QAAIH,YAAJ,EAAkB;EAChBzB,mBAAa,OAAb,IAAwB,IAAxB;EACD;;EAED;EACA,QAAImB,cAAJ,EAAoB;EAClB1G,eAASuF,YAAT,EAAuB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAAvB;EACD;;EAED;EACA,QAAIA,aAAauD,KAAjB,EAAwB;EACtB9I,eAASuF,YAAT,EAAuB,CAAC,OAAD,CAAvB;EACA,aAAOa,YAAY2C,KAAnB;EACD;;EAED;EACA;EACA,QAAItL,MAAJ,EAAY;EACVA,aAAO+K,GAAP;EACD;;EAEDN,aAASM,GAAT;EACD,GAhLD;;EAkLA,MAAMQ,iCAAiChJ,SAAS,EAAT,EAAa,CAClD,IADkD,EAElD,IAFkD,EAGlD,IAHkD,EAIlD,IAJkD,EAKlD,OALkD,CAAb,CAAvC;;EAQA,MAAMiJ,0BAA0BjJ,SAAS,EAAT,EAAa,CAC3C,eAD2C,EAE3C,MAF2C,EAG3C,OAH2C,EAI3C,gBAJ2C,CAAb,CAAhC;;EAOA;;;EAGA,MAAMkJ,eAAelJ,SAAS,EAAT,EAAayF,GAAb,CAArB;EACAzF,WAASkJ,YAAT,EAAuBzD,UAAvB;EACAzF,WAASkJ,YAAT,EAAuBzD,aAAvB;;EAEA,MAAM0D,kBAAkBnJ,SAAS,EAAT,EAAayF,MAAb,CAAxB;EACAzF,WAASmJ,eAAT,EAA0B1D,gBAA1B;;EAEA;;;;;;;;EAQA,MAAM2D,uBAAuB,SAAvBA,oBAAuB,CAAU/I,OAAV,EAAmB;EAC9C,QAAIgJ,SAAS/E,cAAcjE,OAAd,CAAb;;EAEA;EACA;EACA,QAAI,CAACgJ,MAAD,IAAW,CAACA,OAAOC,OAAvB,EAAgC;EAC9BD,eAAS;EACPE,sBAAc5B,cADP;EAEP2B,iBAAS;EAFF,OAAT;EAID;;EAED,QAAMA,UAAUzK,kBAAkBwB,QAAQiJ,OAA1B,CAAhB;EACA,QAAME,gBAAgB3K,kBAAkBwK,OAAOC,OAAzB,CAAtB;;EAEA,QAAIjJ,QAAQkJ,YAAR,KAAyB7B,aAA7B,EAA4C;EAC1C;EACA;EACA;EACA,UAAI2B,OAAOE,YAAP,KAAwB5B,cAA5B,EAA4C;EAC1C,eAAO2B,YAAY,KAAnB;EACD;;EAED;EACA;EACA;EACA,UAAID,OAAOE,YAAP,KAAwB9B,gBAA5B,EAA8C;EAC5C,eACE6B,YAAY,KAAZ,KACCE,kBAAkB,gBAAlB,IACCR,+BAA+BQ,aAA/B,CAFF,CADF;EAKD;;EAED;EACA;EACA,aAAOC,QAAQP,aAAaI,OAAb,CAAR,CAAP;EACD;;EAED,QAAIjJ,QAAQkJ,YAAR,KAAyB9B,gBAA7B,EAA+C;EAC7C;EACA;EACA;EACA,UAAI4B,OAAOE,YAAP,KAAwB5B,cAA5B,EAA4C;EAC1C,eAAO2B,YAAY,MAAnB;EACD;;EAED;EACA;EACA,UAAID,OAAOE,YAAP,KAAwB7B,aAA5B,EAA2C;EACzC,eAAO4B,YAAY,MAAZ,IAAsBL,wBAAwBO,aAAxB,CAA7B;EACD;;EAED;EACA;EACA,aAAOC,QAAQN,gBAAgBG,OAAhB,CAAR,CAAP;EACD;;EAED,QAAIjJ,QAAQkJ,YAAR,KAAyB5B,cAA7B,EAA6C;EAC3C;EACA;EACA;EACA,UACE0B,OAAOE,YAAP,KAAwB7B,aAAxB,IACA,CAACuB,wBAAwBO,aAAxB,CAFH,EAGE;EACA,eAAO,KAAP;EACD;;EAED,UACEH,OAAOE,YAAP,KAAwB9B,gBAAxB,IACA,CAACuB,+BAA+BQ,aAA/B,CAFH,EAGE;EACA,eAAO,KAAP;EACD;;EAED;EACA;EACA;EACA;EACA,UAAME,2BAA2B1J,SAAS,EAAT,EAAa,CAC5C,OAD4C,EAE5C,OAF4C,EAG5C,MAH4C,EAI5C,GAJ4C,EAK5C,QAL4C,CAAb,CAAjC;;EAQA;EACA;EACA,aACE,CAACmJ,gBAAgBG,OAAhB,CAAD,KACCI,yBAAyBJ,OAAzB,KAAqC,CAACJ,aAAaI,OAAb,CADvC,CADF;EAID;;EAED;EACA;EACA;EACA,WAAO,KAAP;EACD,GApGD;;EAsGA;;;;;EAKA,MAAMK,eAAe,SAAfA,YAAe,CAAUC,IAAV,EAAgB;EACnCjL,cAAUsE,UAAUI,OAApB,EAA6B,EAAEhD,SAASuJ,IAAX,EAA7B;EACA,QAAI;EACF;EACAA,WAAKC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B;EACD,KAHD,CAGE,OAAO7G,CAAP,EAAU;EACV,UAAI;EACF6G,aAAKG,SAAL,GAAiBnF,SAAjB;EACD,OAFD,CAEE,OAAO7B,CAAP,EAAU;EACV6G,aAAKI,MAAL;EACD;EACF;EACF,GAZD;;EAcA;;;;;;EAMA,MAAMC,mBAAmB,SAAnBA,gBAAmB,CAAUC,IAAV,EAAgBN,IAAhB,EAAsB;EAC7C,QAAI;EACFjL,gBAAUsE,UAAUI,OAApB,EAA6B;EAC3B8G,mBAAWP,KAAKQ,gBAAL,CAAsBF,IAAtB,CADgB;EAE3BG,cAAMT;EAFqB,OAA7B;EAID,KALD,CAKE,OAAO7G,CAAP,EAAU;EACVpE,gBAAUsE,UAAUI,OAApB,EAA6B;EAC3B8G,mBAAW,IADgB;EAE3BE,cAAMT;EAFqB,OAA7B;EAID;;EAEDA,SAAKU,eAAL,CAAqBJ,IAArB;;EAEA;EACA,QAAIA,SAAS,IAAT,IAAiB,CAACxE,aAAawE,IAAb,CAAtB,EAA0C;EACxC,UAAIrD,cAAcC,mBAAlB,EAAuC;EACrC,YAAI;EACF6C,uBAAaC,IAAb;EACD,SAFD,CAEE,OAAO7G,CAAP,EAAU;EACb,OAJD,MAIO;EACL,YAAI;EACF6G,eAAKW,YAAL,CAAkBL,IAAlB,EAAwB,EAAxB;EACD,SAFD,CAEE,OAAOnH,CAAP,EAAU;EACb;EACF;EACF,GA3BD;;EA6BA;;;;;;EAMA,MAAMyH,gBAAgB,SAAhBA,aAAgB,CAAUC,KAAV,EAAiB;EACrC;EACA,QAAIC,YAAJ;EACA,QAAIC,0BAAJ;;EAEA,QAAI/D,UAAJ,EAAgB;EACd6D,cAAQ,sBAAsBA,KAA9B;EACD,KAFD,MAEO;EACL;EACA,UAAMG,UAAU5L,YAAYyL,KAAZ,EAAmB,aAAnB,CAAhB;EACAE,0BAAoBC,WAAWA,QAAQ,CAAR,CAA/B;EACD;;EAED,QAAI9C,sBAAsB,uBAA1B,EAAmD;EACjD;EACA2C,cACE,mEACAA,KADA,GAEA,gBAHF;EAID;;EAED,QAAMI,eAAelG,qBACjBA,mBAAmB7B,UAAnB,CAA8B2H,KAA9B,CADiB,GAEjBA,KAFJ;EAGA;;;;EAIA,QAAI7C,cAAcD,cAAlB,EAAkC;EAChC,UAAI;EACF+C,cAAM,IAAIzG,SAAJ,GAAgB6G,eAAhB,CAAgCD,YAAhC,EAA8C/C,iBAA9C,CAAN;EACD,OAFD,CAEE,OAAO/E,CAAP,EAAU;EACb;;EAED;EACA,QAAI,CAAC2H,GAAD,IAAQ,CAACA,IAAIK,eAAjB,EAAkC;EAChCL,YAAM5F,eAAekG,cAAf,CAA8BpD,SAA9B,EAAyC,UAAzC,EAAqD,IAArD,CAAN;EACA,UAAI;EACF8C,YAAIK,eAAJ,CAAoBE,SAApB,GAAgCpD,iBAAiB,EAAjB,GAAsBgD,YAAtD;EACD,OAFD,CAEE,OAAO9H,CAAP,EAAU;EACV;EACD;EACF;;EAED,QAAMmI,OAAOR,IAAIQ,IAAJ,IAAYR,IAAIK,eAA7B;;EAEA,QAAIN,SAASE,iBAAb,EAAgC;EAC9BO,WAAKC,YAAL,CACE7I,SAAS8I,cAAT,CAAwBT,iBAAxB,CADF,EAEEO,KAAKG,UAAL,CAAgB,CAAhB,KAAsB,IAFxB;EAID;;EAED;EACA,QAAIzD,cAAcD,cAAlB,EAAkC;EAChC,aAAO1C,qBAAqBqG,IAArB,CACLZ,GADK,EAELhE,iBAAiB,MAAjB,GAA0B,MAFrB,EAGL,CAHK,CAAP;EAID;;EAED,WAAOA,iBAAiBgE,IAAIK,eAArB,GAAuCG,IAA9C;EACD,GA9DD;;EAgEA;;;;;;EAMA,MAAMK,kBAAkB,SAAlBA,eAAkB,CAAUrI,IAAV,EAAgB;EACtC,WAAO6B,mBAAmBuG,IAAnB,CACLpI,KAAKwB,aAAL,IAAsBxB,IADjB,EAELA,IAFK,EAGLW,WAAW2H,YAAX,GAA0B3H,WAAW4H,YAArC,GAAoD5H,WAAW6H,SAH1D,EAIL,IAJK,EAKL,KALK,CAAP;EAOD,GARD;;EAUA;;;;;;EAMA,MAAMC,eAAe,SAAfA,YAAe,CAAUC,GAAV,EAAe;EAClC,WACEA,eAAe5H,eAAf,KACC,OAAO4H,IAAIC,QAAX,KAAwB,QAAxB,IACC,OAAOD,IAAIE,WAAX,KAA2B,QAD5B,IAEC,OAAOF,IAAI9B,WAAX,KAA2B,UAF5B,IAGC,EAAE8B,IAAIG,UAAJ,YAA0BjI,YAA5B,CAHD,IAIC,OAAO8H,IAAItB,eAAX,KAA+B,UAJhC,IAKC,OAAOsB,IAAIrB,YAAX,KAA4B,UAL7B,IAMC,OAAOqB,IAAIrC,YAAX,KAA4B,QAN7B,IAOC,OAAOqC,IAAIT,YAAX,KAA4B,UAR9B,CADF;EAWD,GAZD;;EAcA;;;;;;EAMA,MAAMa,UAAU,SAAVA,OAAU,CAAUxL,MAAV,EAAkB;EAChC,WAAO,QAAOmD,IAAP,yCAAOA,IAAP,OAAgB,QAAhB,GACHnD,kBAAkBmD,IADf,GAEHnD,UACE,QAAOA,MAAP,yCAAOA,MAAP,OAAkB,QADpB,IAEE,OAAOA,OAAO8C,QAAd,KAA2B,QAF7B,IAGE,OAAO9C,OAAOqL,QAAd,KAA2B,QALjC;EAMD,GAPD;;EASA;;;;;;;;EAQA,MAAMI,eAAe,SAAfA,YAAe,CAAUC,UAAV,EAAsBC,WAAtB,EAAmCC,IAAnC,EAAyC;EAC5D,QAAI,CAAChH,MAAM8G,UAAN,CAAL,EAAwB;EACtB;EACD;;EAED9N,iBAAagH,MAAM8G,UAAN,CAAb,EAAgC,UAACG,IAAD,EAAU;EACxCA,WAAKf,IAAL,CAAUrI,SAAV,EAAqBkJ,WAArB,EAAkCC,IAAlC,EAAwClE,MAAxC;EACD,KAFD;EAGD,GARD;;EAUA;;;;;;;;;;EAUA,MAAMoE,oBAAoB,SAApBA,iBAAoB,CAAUH,WAAV,EAAuB;EAC/C,QAAI1H,gBAAJ;;EAEA;EACAwH,iBAAa,wBAAb,EAAuCE,WAAvC,EAAoD,IAApD;;EAEA;EACA,QAAIR,aAAaQ,WAAb,CAAJ,EAA+B;EAC7BxC,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAInN,YAAYmN,YAAYN,QAAxB,EAAkC,iBAAlC,CAAJ,EAA0D;EACxDlC,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAM7C,UAAUrB,kBAAkBkE,YAAYN,QAA9B,CAAhB;;EAEA;EACAI,iBAAa,qBAAb,EAAoCE,WAApC,EAAiD;EAC/C7C,sBAD+C;EAE/CiD,mBAAahH;EAFkC,KAAjD;;EAKA;EACA,QACE,CAACyG,QAAQG,YAAYK,iBAApB,CAAD,KACC,CAACR,QAAQG,YAAY1H,OAApB,CAAD,IACC,CAACuH,QAAQG,YAAY1H,OAAZ,CAAoB+H,iBAA5B,CAFH,KAGAhN,WAAW,SAAX,EAAsB2M,YAAYlB,SAAlC,CAHA,IAIAzL,WAAW,SAAX,EAAsB2M,YAAYL,WAAlC,CALF,EAME;EACAnC,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QACE7C,YAAY,QAAZ,IACA9J,WAAW,YAAX,EAAyB2M,YAAYlB,SAArC,CAFF,EAGE;EACAtB,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAI,CAAC5G,aAAa+D,OAAb,CAAD,IAA0BlD,YAAYkD,OAAZ,CAA9B,EAAoD;EAClD;EACA,UAAItC,gBAAgB,CAACG,gBAAgBmC,OAAhB,CAArB,EAA+C;EAC7C,YAAMO,aAAavF,cAAc6H,WAAd,KAA8BA,YAAYtC,UAA7D;EACA,YAAMwB,aAAahH,cAAc8H,WAAd,KAA8BA,YAAYd,UAA7D;;EAEA,YAAIA,cAAcxB,UAAlB,EAA8B;EAC5B,cAAM4C,aAAapB,WAAWjL,MAA9B;;EAEA,eAAK,IAAIsM,IAAID,aAAa,CAA1B,EAA6BC,KAAK,CAAlC,EAAqC,EAAEA,CAAvC,EAA0C;EACxC7C,uBAAWsB,YAAX,CACEhH,UAAUkH,WAAWqB,CAAX,CAAV,EAAyB,IAAzB,CADF,EAEEtI,eAAe+H,WAAf,CAFF;EAID;EACF;EACF;;EAED,UAAI,CAAC/F,YAAYkD,OAAZ,CAAD,IAAyBqD,wBAAwBrD,OAAxB,CAA7B,EAA+D;EAC7D,YACEzD,wBAAwBC,YAAxB,YAAgDrG,MAAhD,IACAD,WAAWqG,wBAAwBC,YAAnC,EAAiDwD,OAAjD,CAFF,EAIE,OAAO,KAAP;EACF,YACEzD,wBAAwBC,YAAxB,YAAgDwC,QAAhD,IACAzC,wBAAwBC,YAAxB,CAAqCwD,OAArC,CAFF,EAIE,OAAO,KAAP;EACH;;EAEDK,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAIA,uBAAuBvI,OAAvB,IAAkC,CAACwF,qBAAqB+C,WAArB,CAAvC,EAA0E;EACxExC,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED,QACE,CAAC7C,YAAY,UAAZ,IAA0BA,YAAY,SAAvC,KACA9J,WAAW,sBAAX,EAAmC2M,YAAYlB,SAA/C,CAFF,EAGE;EACAtB,mBAAawC,WAAb;EACA,aAAO,IAAP;EACD;;EAED;EACA,QAAI1F,sBAAsB0F,YAAY7I,QAAZ,KAAyB,CAAnD,EAAsD;EACpD;EACAmB,gBAAU0H,YAAYL,WAAtB;EACArH,gBAAUvF,cAAcuF,OAAd,EAAuB9C,gBAAvB,EAAsC,GAAtC,CAAV;EACA8C,gBAAUvF,cAAcuF,OAAd,EAAuB7C,WAAvB,EAAiC,GAAjC,CAAV;EACA,UAAIuK,YAAYL,WAAZ,KAA4BrH,OAAhC,EAAyC;EACvC9F,kBAAUsE,UAAUI,OAApB,EAA6B,EAAEhD,SAAS8L,YAAYhI,SAAZ,EAAX,EAA7B;EACAgI,oBAAYL,WAAZ,GAA0BrH,OAA1B;EACD;EACF;;EAED;EACAwH,iBAAa,uBAAb,EAAsCE,WAAtC,EAAmD,IAAnD;;EAEA,WAAO,KAAP;EACD,GAlHD;;EAoHA;;;;;;;;EAQA;EACA,MAAMS,oBAAoB,SAApBA,iBAAoB,CAAUC,KAAV,EAAiBC,MAAjB,EAAyB/L,KAAzB,EAAgC;EACxD;EACA,QACEgG,iBACC+F,WAAW,IAAX,IAAmBA,WAAW,MAD/B,MAEC/L,SAASuB,QAAT,IAAqBvB,SAASoH,WAF/B,CADF,EAIE;EACA,aAAO,KAAP;EACD;;EAED;;;;EAIA,QACE5B,mBACA,CAACF,YAAYyG,MAAZ,CADD,IAEAtN,WAAWqC,YAAX,EAAsBiL,MAAtB,CAHF,EAIE,CAJF,MAMO,IAAIxG,mBAAmB9G,WAAWsC,YAAX,EAAsBgL,MAAtB,CAAvB,EAAsD,CAAtD,MAGA,IAAI,CAACpH,aAAaoH,MAAb,CAAD,IAAyBzG,YAAYyG,MAAZ,CAA7B,EAAkD;EACvD;EACE;EACA;EACA;EACCH,8BAAwBE,KAAxB,MACGhH,wBAAwBC,YAAxB,YAAgDrG,MAAhD,IACAD,WAAWqG,wBAAwBC,YAAnC,EAAiD+G,KAAjD,CADD,IAEEhH,wBAAwBC,YAAxB,YAAgDwC,QAAhD,IACCzC,wBAAwBC,YAAxB,CAAqC+G,KAArC,CAJL,MAKGhH,wBAAwBK,kBAAxB,YAAsDzG,MAAtD,IACAD,WAAWqG,wBAAwBK,kBAAnC,EAAuD4G,MAAvD,CADD,IAEEjH,wBAAwBK,kBAAxB,YAAsDoC,QAAtD,IACCzC,wBAAwBK,kBAAxB,CAA2C4G,MAA3C,CARL,CAAD;EASA;EACA;EACCA,iBAAW,IAAX,IACCjH,wBAAwBM,8BADzB,KAEGN,wBAAwBC,YAAxB,YAAgDrG,MAAhD,IACAD,WAAWqG,wBAAwBC,YAAnC,EAAiD/E,KAAjD,CADD,IAEE8E,wBAAwBC,YAAxB,YAAgDwC,QAAhD,IACCzC,wBAAwBC,YAAxB,CAAqC/E,KAArC,CALL,CAfH,EAqBE,CArBF,MAwBO;EACL,eAAO,KAAP;EACD;EACD;EACD,KA7BM,MA6BA,IAAIwG,oBAAoBuF,MAApB,CAAJ,EAAiC,CAAjC,MAIA,IACLtN,WAAWuC,iBAAX,EAA2B7C,cAAc6B,KAAd,EAAqBkB,kBAArB,EAAsC,EAAtC,CAA3B,CADK,EAEL,CAFK,MAMA,IACL,CAAC6K,WAAW,KAAX,IAAoBA,WAAW,YAA/B,IAA+CA,WAAW,MAA3D,KACAD,UAAU,QADV,IAEAzN,cAAc2B,KAAd,EAAqB,OAArB,MAAkC,CAFlC,IAGAsG,cAAcwF,KAAd,CAJK,EAKL,CALK,MAUA,IACLrG,2BACA,CAAChH,WAAWwC,oBAAX,EAA8B9C,cAAc6B,KAAd,EAAqBkB,kBAArB,EAAsC,EAAtC,CAA9B,CAFI,EAGL,CAHK,MAOA,IAAI,CAAClB,KAAL,EAAY,CAAZ,MAGA;EACL,aAAO,KAAP;EACD;;EAED,WAAO,IAAP;EACD,GAvFD;;EAyFA;;;;;;EAMA,MAAM4L,0BAA0B,SAA1BA,uBAA0B,CAAUrD,OAAV,EAAmB;EACjD,WAAOA,QAAQjK,OAAR,CAAgB,GAAhB,IAAuB,CAA9B;EACD,GAFD;;EAIA;;;;;;;;;;EAUA,MAAM0N,sBAAsB,SAAtBA,mBAAsB,CAAUZ,WAAV,EAAuB;EACjD,QAAIa,aAAJ;EACA,QAAIjM,cAAJ;EACA,QAAI+L,eAAJ;EACA,QAAI3M,UAAJ;EACA;EACA8L,iBAAa,0BAAb,EAAyCE,WAAzC,EAAsD,IAAtD;;EANiD,QAQzCJ,UARyC,GAQ1BI,WAR0B,CAQzCJ,UARyC;;EAUjD;;EACA,QAAI,CAACA,UAAL,EAAiB;EACf;EACD;;EAED,QAAMkB,YAAY;EAChBC,gBAAU,EADM;EAEhBC,iBAAW,EAFK;EAGhBC,gBAAU,IAHM;EAIhBC,yBAAmB3H;EAJH,KAAlB;EAMAvF,QAAI4L,WAAW3L,MAAf;;EAEA;EACA,WAAOD,GAAP,EAAY;EACV6M,aAAOjB,WAAW5L,CAAX,CAAP;EADU,kBAEqB6M,IAFrB;EAAA,UAEF9C,IAFE,SAEFA,IAFE;EAAA,UAEIX,YAFJ,SAEIA,YAFJ;;EAGVxI,cAAQzB,WAAW0N,KAAKjM,KAAhB,CAAR;EACA+L,eAAS7E,kBAAkBiC,IAAlB,CAAT;;EAEA;EACA+C,gBAAUC,QAAV,GAAqBJ,MAArB;EACAG,gBAAUE,SAAV,GAAsBpM,KAAtB;EACAkM,gBAAUG,QAAV,GAAqB,IAArB;EACAH,gBAAUK,aAAV,GAA0BC,SAA1B,CAVU;EAWVtB,mBAAa,uBAAb,EAAsCE,WAAtC,EAAmDc,SAAnD;EACAlM,cAAQkM,UAAUE,SAAlB;EACA;EACA,UAAIF,UAAUK,aAAd,EAA6B;EAC3B;EACD;;EAED;EACArD,uBAAiBC,IAAjB,EAAuBiC,WAAvB;;EAEA;EACA,UAAI,CAACc,UAAUG,QAAf,EAAyB;EACvB;EACD;;EAED;EACA,UAAI5N,WAAW,MAAX,EAAmBuB,KAAnB,CAAJ,EAA+B;EAC7BkJ,yBAAiBC,IAAjB,EAAuBiC,WAAvB;EACA;EACD;;EAED;EACA,UAAI1F,kBAAJ,EAAwB;EACtB1F,gBAAQ7B,cAAc6B,KAAd,EAAqBY,gBAArB,EAAoC,GAApC,CAAR;EACAZ,gBAAQ7B,cAAc6B,KAAd,EAAqBa,WAArB,EAA+B,GAA/B,CAAR;EACD;;EAED;EACA,UAAMiL,QAAQ5E,kBAAkBkE,YAAYN,QAA9B,CAAd;EACA,UAAI,CAACe,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiC/L,KAAjC,CAAL,EAA8C;EAC5C;EACD;;EAED;EACA,UAAI;EACF,YAAIwI,YAAJ,EAAkB;EAChB4C,sBAAYqB,cAAZ,CAA2BjE,YAA3B,EAAyCW,IAAzC,EAA+CnJ,KAA/C;EACD,SAFD,MAEO;EACL;EACAoL,sBAAY5B,YAAZ,CAAyBL,IAAzB,EAA+BnJ,KAA/B;EACD;;EAEDtC,iBAASwE,UAAUI,OAAnB;EACD,OATD,CASE,OAAON,CAAP,EAAU;EACb;;EAED;EACAkJ,iBAAa,yBAAb,EAAwCE,WAAxC,EAAqD,IAArD;EACD,GAnFD;;EAqFA;;;;;EAKA,MAAMsB,qBAAqB,SAArBA,kBAAqB,CAAUC,QAAV,EAAoB;EAC7C,QAAIC,mBAAJ;EACA,QAAMC,iBAAiBrC,gBAAgBmC,QAAhB,CAAvB;;EAEA;EACAzB,iBAAa,yBAAb,EAAwCyB,QAAxC,EAAkD,IAAlD;;EAEA,WAAQC,aAAaC,eAAeC,QAAf,EAArB,EAAiD;EAC/C;EACA5B,mBAAa,wBAAb,EAAuC0B,UAAvC,EAAmD,IAAnD;;EAEA;EACA,UAAIrB,kBAAkBqB,UAAlB,CAAJ,EAAmC;EACjC;EACD;;EAED;EACA,UAAIA,WAAWlJ,OAAX,YAA8BhB,gBAAlC,EAAoD;EAClDgK,2BAAmBE,WAAWlJ,OAA9B;EACD;;EAED;EACAsI,0BAAoBY,UAApB;EACD;;EAED;EACA1B,iBAAa,wBAAb,EAAuCyB,QAAvC,EAAiD,IAAjD;EACD,GA3BD;;EA6BA;;;;;;;EAOA;EACAzK,YAAU6K,QAAV,GAAqB,UAAUrD,KAAV,EAAiBjC,GAAjB,EAAsB;EACzC,QAAI0C,aAAJ;EACA,QAAI6C,qBAAJ;EACA,QAAI5B,oBAAJ;EACA,QAAI6B,gBAAJ;EACA,QAAIC,mBAAJ;EACA;;;EAGApG,qBAAiB,CAAC4C,KAAlB;EACA,QAAI5C,cAAJ,EAAoB;EAClB4C,cAAQ,OAAR;EACD;;EAED;EACA,QAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACuB,QAAQvB,KAAR,CAAlC,EAAkD;EAChD;EACA,UAAI,OAAOA,MAAMyD,QAAb,KAA0B,UAA9B,EAA0C;EACxC,cAAMvO,gBAAgB,4BAAhB,CAAN;EACD,OAFD,MAEO;EACL8K,gBAAQA,MAAMyD,QAAN,EAAR;EACA,YAAI,OAAOzD,KAAP,KAAiB,QAArB,EAA+B;EAC7B,gBAAM9K,gBAAgB,iCAAhB,CAAN;EACD;EACF;EACF;;EAED;EACA,QAAI,CAACsD,UAAUM,WAAf,EAA4B;EAC1B,UACE,QAAOpB,OAAOgM,YAAd,MAA+B,QAA/B,IACA,OAAOhM,OAAOgM,YAAd,KAA+B,UAFjC,EAGE;EACA,YAAI,OAAO1D,KAAP,KAAiB,QAArB,EAA+B;EAC7B,iBAAOtI,OAAOgM,YAAP,CAAoB1D,KAApB,CAAP;EACD;;EAED,YAAIuB,QAAQvB,KAAR,CAAJ,EAAoB;EAClB,iBAAOtI,OAAOgM,YAAP,CAAoB1D,MAAMV,SAA1B,CAAP;EACD;EACF;;EAED,aAAOU,KAAP;EACD;;EAED;EACA,QAAI,CAAC9D,UAAL,EAAiB;EACf4B,mBAAaC,GAAb;EACD;;EAED;EACAvF,cAAUI,OAAV,GAAoB,EAApB;;EAEA;EACA,QAAI,OAAOoH,KAAP,KAAiB,QAArB,EAA+B;EAC7BxD,iBAAW,KAAX;EACD;;EAED,QAAIA,QAAJ,EAAc,CAAd,MAEO,IAAIwD,iBAAiB9G,IAArB,EAA2B;EAChC;;EAEAuH,aAAOV,cAAc,SAAd,CAAP;EACAuD,qBAAe7C,KAAKxG,aAAL,CAAmBQ,UAAnB,CAA8BuF,KAA9B,EAAqC,IAArC,CAAf;EACA,UAAIsD,aAAazK,QAAb,KAA0B,CAA1B,IAA+ByK,aAAalC,QAAb,KAA0B,MAA7D,EAAqE;EACnE;EACAX,eAAO6C,YAAP;EACD,OAHD,MAGO,IAAIA,aAAalC,QAAb,KAA0B,MAA9B,EAAsC;EAC3CX,eAAO6C,YAAP;EACD,OAFM,MAEA;EACL;EACA7C,aAAKkD,WAAL,CAAiBL,YAAjB;EACD;EACF,KAdM,MAcA;EACL;EACA,UACE,CAAClH,UAAD,IACA,CAACJ,kBADD,IAEA,CAACC,cAFD;EAGA;EACA+D,YAAMpL,OAAN,CAAc,GAAd,MAAuB,CAAC,CAL1B,EAME;EACA,eAAOsF,sBAAsBE,mBAAtB,GACHF,mBAAmB7B,UAAnB,CAA8B2H,KAA9B,CADG,GAEHA,KAFJ;EAGD;;EAED;EACAS,aAAOV,cAAcC,KAAd,CAAP;;EAEA;EACA,UAAI,CAACS,IAAL,EAAW;EACT,eAAOrE,aAAa,IAAb,GAAoBjC,SAA3B;EACD;EACF;;EAED;EACA,QAAIsG,QAAQtE,UAAZ,EAAwB;EACtB+C,mBAAauB,KAAKmD,UAAlB;EACD;;EAED;EACA,QAAMC,eAAe/C,gBAAgBtE,WAAWwD,KAAX,GAAmBS,IAAnC,CAArB;;EAEA;EACA,WAAQiB,cAAcmC,aAAaT,QAAb,EAAtB,EAAgD;EAC9C;EACA,UAAI1B,YAAY7I,QAAZ,KAAyB,CAAzB,IAA8B6I,gBAAgB6B,OAAlD,EAA2D;EACzD;EACD;;EAED;EACA,UAAI1B,kBAAkBH,WAAlB,CAAJ,EAAoC;EAClC;EACD;;EAED;EACA,UAAIA,YAAY1H,OAAZ,YAA+BhB,gBAAnC,EAAqD;EACnDgK,2BAAmBtB,YAAY1H,OAA/B;EACD;;EAED;EACAsI,0BAAoBZ,WAApB;;EAEA6B,gBAAU7B,WAAV;EACD;;EAED6B,cAAU,IAAV;;EAEA;EACA,QAAI/G,QAAJ,EAAc;EACZ,aAAOwD,KAAP;EACD;;EAED;EACA,QAAI5D,UAAJ,EAAgB;EACd,UAAIC,mBAAJ,EAAyB;EACvBmH,qBAAajJ,uBAAuBsG,IAAvB,CAA4BJ,KAAKxG,aAAjC,CAAb;;EAEA,eAAOwG,KAAKmD,UAAZ,EAAwB;EACtB;EACAJ,qBAAWG,WAAX,CAAuBlD,KAAKmD,UAA5B;EACD;EACF,OAPD,MAOO;EACLJ,qBAAa/C,IAAb;EACD;;EAED,UAAIxF,aAAa6I,UAAjB,EAA6B;EAC3B;;;;;;;EAOAN,qBAAa/I,WAAWoG,IAAX,CAAgB9H,gBAAhB,EAAkCyK,UAAlC,EAA8C,IAA9C,CAAb;EACD;;EAED,aAAOA,UAAP;EACD;;EAED,QAAIO,iBAAiB9H,iBAAiBwE,KAAKnB,SAAtB,GAAkCmB,KAAKD,SAA5D;;EAEA;EACA,QAAIxE,kBAAJ,EAAwB;EACtB+H,uBAAiBtP,cAAcsP,cAAd,EAA8B7M,gBAA9B,EAA6C,GAA7C,CAAjB;EACA6M,uBAAiBtP,cAAcsP,cAAd,EAA8B5M,WAA9B,EAAwC,GAAxC,CAAjB;EACD;;EAED,WAAO+C,sBAAsBE,mBAAtB,GACHF,mBAAmB7B,UAAnB,CAA8B0L,cAA9B,CADG,GAEHA,cAFJ;EAGD,GA7KD;;EA+KA;;;;;;EAMAvL,YAAUwL,SAAV,GAAsB,UAAUjG,GAAV,EAAe;EACnCD,iBAAaC,GAAb;EACA7B,iBAAa,IAAb;EACD,GAHD;;EAKA;;;;;EAKA1D,YAAUyL,WAAV,GAAwB,YAAY;EAClCxG,aAAS,IAAT;EACAvB,iBAAa,KAAb;EACD,GAHD;;EAKA;;;;;;;;;;EAUA1D,YAAU0L,gBAAV,GAA6B,UAAUC,GAAV,EAAe5B,IAAf,EAAqBjM,KAArB,EAA4B;EACvD;EACA,QAAI,CAACmH,MAAL,EAAa;EACXK,mBAAa,EAAb;EACD;;EAED,QAAMsE,QAAQ5E,kBAAkB2G,GAAlB,CAAd;EACA,QAAM9B,SAAS7E,kBAAkB+E,IAAlB,CAAf;EACA,WAAOJ,kBAAkBC,KAAlB,EAAyBC,MAAzB,EAAiC/L,KAAjC,CAAP;EACD,GATD;;EAWA;;;;;;;EAOAkC,YAAU4L,OAAV,GAAoB,UAAU3C,UAAV,EAAsB4C,YAAtB,EAAoC;EACtD,QAAI,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;EACtC;EACD;;EAED1J,UAAM8G,UAAN,IAAoB9G,MAAM8G,UAAN,KAAqB,EAAzC;EACAvN,cAAUyG,MAAM8G,UAAN,CAAV,EAA6B4C,YAA7B;EACD,GAPD;;EASA;;;;;;;EAOA7L,YAAU8L,UAAV,GAAuB,UAAU7C,UAAV,EAAsB;EAC3C,QAAI9G,MAAM8G,UAAN,CAAJ,EAAuB;EACrBzN,eAAS2G,MAAM8G,UAAN,CAAT;EACD;EACF,GAJD;;EAMA;;;;;;EAMAjJ,YAAU+L,WAAV,GAAwB,UAAU9C,UAAV,EAAsB;EAC5C,QAAI9G,MAAM8G,UAAN,CAAJ,EAAuB;EACrB9G,YAAM8G,UAAN,IAAoB,EAApB;EACD;EACF,GAJD;;EAMA;;;;;EAKAjJ,YAAUgM,cAAV,GAA2B,YAAY;EACrC7J,YAAQ,EAAR;EACD,GAFD;;EAIA,SAAOnC,SAAP;EACD;;AAED,eAAeD,iBAAf;;;;;;;;"}
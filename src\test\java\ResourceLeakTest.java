import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Resource leak test class
 * Used to verify the fix for file stream resource management
 *
 * Creator: wanghq
 * Created: 2025-07-04
 */
public class ResourceLeakTest {

    private static final AtomicInteger openFileCount = new AtomicInteger(0);

    public static void main(String[] args) {
        System.out.println("Starting resource leak test...");

        // Test 1: File stream resource management test
        testFileStreamResourceManagement();

        // Test 2: Large file memory usage test
        testLargeFileMemoryUsage();

        // Test 3: Temporary file cleanup test
        testTempFileCleanup();

        System.out.println("Resource leak test completed");
    }
    
    /**
     * Test file stream resource management
     */
    private static void testFileStreamResourceManagement() {
        System.out.println("\n=== Testing file stream resource management ===");

        try {
            // Create test file
            Path testFile = Files.createTempFile("test", ".txt");
            Files.write(testFile, "test data".getBytes());

            // Simulate multiple file operations
            for (int i = 0; i < 10; i++) {
                try (FileInputStream fis = new FileInputStream(testFile.toFile());
                     FileOutputStream fos = new FileOutputStream(testFile.toString() + "_copy_" + i)) {

                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                    fos.flush();
                }
            }

            // Clean up test files
            Files.deleteIfExists(testFile);
            for (int i = 0; i < 10; i++) {
                Files.deleteIfExists(Paths.get(testFile.toString() + "_copy_" + i));
            }

            System.out.println("File stream resource management test PASSED - All streams properly closed");

        } catch (Exception e) {
            System.err.println("File stream resource management test FAILED: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test large file memory usage
     */
    private static void testLargeFileMemoryUsage() {
        System.out.println("\n=== Testing large file memory usage ===");

        try {
            // Record initial memory usage
            Runtime runtime = Runtime.getRuntime();
            long initialMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Initial memory usage: " + (initialMemory / 1024 / 1024) + " MB");

            // Create large test file (10MB)
            Path largeFile = Files.createTempFile("large_test", ".dat");
            try (FileOutputStream fos = new FileOutputStream(largeFile.toFile())) {
                byte[] data = new byte[1024 * 1024]; // 1MB block
                for (int i = 0; i < 10; i++) {
                    fos.write(data);
                }
                fos.flush();
            }

            // Use streaming copy to handle large file
            Path copyFile = Files.createTempFile("large_copy", ".dat");
            try (FileInputStream fis = new FileInputStream(largeFile.toFile());
                 FileOutputStream fos = new FileOutputStream(copyFile.toFile())) {

                byte[] buffer = new byte[8192]; // 8KB buffer
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
                fos.flush();
            }

            // Check memory usage
            System.gc(); // Trigger garbage collection
            Thread.sleep(100); // Wait for GC to complete
            long finalMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryIncrease = finalMemory - initialMemory;

            System.out.println("Final memory usage: " + (finalMemory / 1024 / 1024) + " MB");
            System.out.println("Memory increase: " + (memoryIncrease / 1024 / 1024) + " MB");

            // Clean up test files
            Files.deleteIfExists(largeFile);
            Files.deleteIfExists(copyFile);

            if (memoryIncrease < 50 * 1024 * 1024) { // Memory increase less than 50MB is considered normal
                System.out.println("Large file memory test PASSED - Memory usage well controlled");
            } else {
                System.out.println("WARNING: Large file processing may have memory issues");
            }

        } catch (Exception e) {
            System.err.println("Large file memory test FAILED: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test temporary file cleanup
     */
    private static void testTempFileCleanup() {
        System.out.println("\n=== Testing temporary file cleanup ===");

        try {
            // Create temporary directory and files
            Path tempDir = Files.createTempDirectory("test_cleanup");
            Path tempFile1 = Files.createTempFile(tempDir, "temp1", ".tmp");
            Path tempFile2 = Files.createTempFile(tempDir, "temp2", ".tmp");

            Files.write(tempFile1, "temp data 1".getBytes());
            Files.write(tempFile2, "temp data 2".getBytes());

            System.out.println("Created temp directory: " + tempDir);
            System.out.println("Created temp files: " + tempFile1 + ", " + tempFile2);

            // Verify files exist
            if (Files.exists(tempDir) && Files.exists(tempFile1) && Files.exists(tempFile2)) {
                System.out.println("Temporary files created successfully");
            }

            // Simulate cleanup process
            try {
                Files.deleteIfExists(tempFile1);
                Files.deleteIfExists(tempFile2);
                Files.deleteIfExists(tempDir);

                System.out.println("Temporary file cleanup completed");

                // Verify cleanup results
                if (!Files.exists(tempDir) && !Files.exists(tempFile1) && !Files.exists(tempFile2)) {
                    System.out.println("Temporary file cleanup test PASSED - All temp files cleaned");
                } else {
                    System.out.println("WARNING: Some temporary files were not cleaned");
                }

            } catch (Exception e) {
                System.err.println("Exception occurred while cleaning temp files: " + e.getMessage());
            }

        } catch (Exception e) {
            System.err.println("Temporary file cleanup test FAILED: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

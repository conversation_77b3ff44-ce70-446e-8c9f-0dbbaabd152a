{"version": 3, "file": "purify.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function numberIsNaN(x) {\n  // eslint-disable-next-line unicorn/prefer-number-properties\n  return typeof x === 'number' && isNaN(x);\n}\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  numberIsNaN,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Specify the maximum element nesting depth to prevent mXSS */\n  const MAX_NESTING_DEPTH = 255;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, [\n    'foreignobject',\n    'annotation-xml',\n  ]);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      ((typeof elm.__depth !== 'undefined' &&\n        typeof elm.__depth !== 'number') ||\n        (typeof elm.__removalCount !== 'undefined' &&\n          typeof elm.__removalCount !== 'number') ||\n        typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === 8 &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document ||\n        value in formElement ||\n        value === '__depth' ||\n        value === '__removalCount')\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      const parentNode = getParentNode(shadowNode);\n\n      /* Set the nesting depth of an element */\n      if (shadowNode.nodeType === 1) {\n        if (parentNode && parentNode.__depth) {\n          /*\n            We want the depth of the node in the original tree, which can\n            change when it's removed from its parent.\n          */\n          shadowNode.__depth =\n            (shadowNode.__removalCount || 0) + parentNode.__depth + 1;\n        } else {\n          shadowNode.__depth = 1;\n        }\n      }\n\n      /*\n       * Remove an element if nested too deeply to avoid mXSS\n       * or if the __depth might have been tampered with\n       */\n      if (\n        shadowNode.__depth >= MAX_NESTING_DEPTH ||\n        numberIsNaN(shadowNode.__depth)\n      ) {\n        _forceRemove(shadowNode);\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        shadowNode.content.__depth = shadowNode.__depth;\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      const parentNode = getParentNode(currentNode);\n\n      /* Set the nesting depth of an element */\n      if (currentNode.nodeType === 1) {\n        if (parentNode && parentNode.__depth) {\n          /*\n            We want the depth of the node in the original tree, which can\n            change when it's removed from its parent.\n          */\n          currentNode.__depth =\n            (currentNode.__removalCount || 0) + parentNode.__depth + 1;\n        } else {\n          currentNode.__depth = 1;\n        }\n      }\n\n      /*\n       * Remove an element if nested too deeply to avoid mXSS\n       * or if the __depth might have been tampered with\n       */\n      if (\n        currentNode.__depth >= MAX_NESTING_DEPTH ||\n        numberIsNaN(currentNode.__depth)\n      ) {\n        _forceRemove(currentNode);\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        currentNode.content.__depth = currentNode.__depth;\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "_construct", "_toConsumableArray", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "numberIsNaN", "isNaN", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "_typeof", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "MAX_NESTING_DEPTH", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "__depth", "__removalCount", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "i", "child<PERSON>lone", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IACEA,cAAc,GAKZC,MAAM,CALRD,cAAc;IACdE,cAAc,GAIZD,MAAM,CAJRC,cAAc;IACdC,QAAQ,GAGNF,MAAM,CAHRE,QAAQ;IACRC,cAAc,GAEZH,MAAM,CAFRG,cAAc;IACdC,wBAAwB,GACtBJ,MAAM,CADRI,wBAAwB,CAAA;EAG1B,IAAMC,MAAM,GAAmBL,MAAM,CAA/BK,MAAM;IAAEC,IAAI,GAAaN,MAAM,CAAvBM,IAAI;EAAEC,EAAAA,MAAM,GAAKP,MAAM,CAAjBO,MAAM,CAAY;EACtC,IAAAC,IAAA,GAA2B,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO;IAA9DC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS,CAAA;EAEtB,IAAI,CAACD,KAAK,EAAE;IACVA,KAAK,GAAG,SAAAA,KAAUE,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;EACtC,IAAA,OAAOF,GAAG,CAACF,KAAK,CAACG,SAAS,EAAEC,IAAI,CAAC,CAAA;KAClC,CAAA;EACH,CAAA;EAEA,IAAI,CAACT,MAAM,EAAE;EACXA,EAAAA,MAAM,GAAG,SAAAA,MAAUU,CAAAA,CAAC,EAAE;EACpB,IAAA,OAAOA,CAAC,CAAA;KACT,CAAA;EACH,CAAA;EAEA,IAAI,CAACT,IAAI,EAAE;EACTA,EAAAA,IAAI,GAAG,SAAAA,IAAUS,CAAAA,CAAC,EAAE;EAClB,IAAA,OAAOA,CAAC,CAAA;KACT,CAAA;EACH,CAAA;EAEA,IAAI,CAACJ,SAAS,EAAE;EACdA,EAAAA,SAAS,GAAG,SAAAA,SAAAA,CAAUK,IAAI,EAAEF,IAAI,EAAE;EAChC,IAAA,OAAAG,UAAA,CAAWD,IAAI,EAAAE,kBAAA,CAAIJ,IAAI,CAAA,CAAA,CAAA;KACxB,CAAA;EACH,CAAA;EAEA,IAAMK,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;EAErD,IAAMC,QAAQ,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,GAAG,CAAC,CAAA;EAC7C,IAAMC,SAAS,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,IAAI,CAAC,CAAA;EAG/C,IAAMC,iBAAiB,GAAGR,OAAO,CAACS,MAAM,CAACP,SAAS,CAACQ,WAAW,CAAC,CAAA;EAC/D,IAAMC,cAAc,GAAGX,OAAO,CAACS,MAAM,CAACP,SAAS,CAACU,QAAQ,CAAC,CAAA;EACzD,IAAMC,WAAW,GAAGb,OAAO,CAACS,MAAM,CAACP,SAAS,CAACY,KAAK,CAAC,CAAA;EACnD,IAAMC,aAAa,GAAGf,OAAO,CAACS,MAAM,CAACP,SAAS,CAACc,OAAO,CAAC,CAAA;EACvD,IAAMC,aAAa,GAAGjB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACgB,OAAO,CAAC,CAAA;EACvD,IAAMC,UAAU,GAAGnB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACkB,IAAI,CAAC,CAAA;EAEjD,IAAMC,UAAU,GAAGrB,OAAO,CAACsB,MAAM,CAACpB,SAAS,CAACqB,IAAI,CAAC,CAAA;EAEjD,IAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;EAEvC,SAASC,WAAWA,CAAChC,CAAC,EAAE;EAC7B;IACA,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIiC,KAAK,CAACjC,CAAC,CAAC,CAAA;EAC1C,CAAA;EAEO,SAASK,OAAOA,CAAC6B,IAAI,EAAE;EAC5B,EAAA,OAAO,UAACC,OAAO,EAAA;MAAA,KAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAKvC,IAAI,OAAAO,KAAA,CAAA8B,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;EAAJxC,MAAAA,IAAI,CAAAwC,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;EAAA,KAAA;EAAA,IAAA,OAAK5C,KAAK,CAACuC,IAAI,EAAEC,OAAO,EAAEpC,IAAI,CAAC,CAAA;EAAA,GAAA,CAAA;EACzD,CAAA;EAEO,SAAS+B,WAAWA,CAACI,IAAI,EAAE;IAChC,OAAO,YAAA;EAAA,IAAA,KAAA,IAAAM,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIvC,IAAI,GAAAO,IAAAA,KAAA,CAAAkC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJ1C,MAAAA,IAAI,CAAA0C,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;EAAA,KAAA;EAAA,IAAA,OAAK7C,SAAS,CAACsC,IAAI,EAAEnC,IAAI,CAAC,CAAA;EAAA,GAAA,CAAA;EAC3C,CAAA;;EAEA;EACO,SAAS2C,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;EAAA,EAAA,IAAAC,kBAAA,CAAA;IACtDD,iBAAiB,GAAA,CAAAC,kBAAA,GAAGD,iBAAiB,cAAAC,kBAAA,KAAA,KAAA,CAAA,GAAAA,kBAAA,GAAIjC,iBAAiB,CAAA;EAC1D,EAAA,IAAI3B,cAAc,EAAE;EAClB;EACA;EACA;EACAA,IAAAA,cAAc,CAACyD,GAAG,EAAE,IAAI,CAAC,CAAA;EAC3B,GAAA;EAEA,EAAA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM,CAAA;IACpB,OAAOS,CAAC,EAAE,EAAE;EACV,IAAA,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC,CAAA;EACtB,IAAA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;EAC/B,MAAA,IAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC,CAAA;QAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;EACzB;EACA,QAAA,IAAI,CAAC7D,QAAQ,CAACyD,KAAK,CAAC,EAAE;EACpBA,UAAAA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS,CAAA;EACtB,SAAA;EAEAD,QAAAA,OAAO,GAAGC,SAAS,CAAA;EACrB,OAAA;EACF,KAAA;EAEAN,IAAAA,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI,CAAA;EACrB,GAAA;EAEA,EAAA,OAAOL,GAAG,CAAA;EACZ,CAAA;;EAEA;EACO,SAASO,KAAKA,CAACC,MAAM,EAAE;EAC5B,EAAA,IAAMC,SAAS,GAAG5D,MAAM,CAAC,IAAI,CAAC,CAAA;EAE9B,EAAA,IAAI6D,QAAQ,CAAA;IACZ,KAAKA,QAAQ,IAAIF,MAAM,EAAE;EACvB,IAAA,IAAIxD,KAAK,CAACX,cAAc,EAAEmE,MAAM,EAAE,CAACE,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;EACtDD,MAAAA,SAAS,CAACC,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC,CAAA;EACxC,KAAA;EACF,GAAA;EAEA,EAAA,OAAOD,SAAS,CAAA;EAClB,CAAA;;EAEA;EACA;EACA;EACA;EACA,SAASE,YAAYA,CAACH,MAAM,EAAEI,IAAI,EAAE;IAClC,OAAOJ,MAAM,KAAK,IAAI,EAAE;EACtB,IAAA,IAAMK,IAAI,GAAGnE,wBAAwB,CAAC8D,MAAM,EAAEI,IAAI,CAAC,CAAA;EACnD,IAAA,IAAIC,IAAI,EAAE;QACR,IAAIA,IAAI,CAACC,GAAG,EAAE;EACZ,QAAA,OAAOpD,OAAO,CAACmD,IAAI,CAACC,GAAG,CAAC,CAAA;EAC1B,OAAA;EAEA,MAAA,IAAI,OAAOD,IAAI,CAACE,KAAK,KAAK,UAAU,EAAE;EACpC,QAAA,OAAOrD,OAAO,CAACmD,IAAI,CAACE,KAAK,CAAC,CAAA;EAC5B,OAAA;EACF,KAAA;EAEAP,IAAAA,MAAM,GAAG/D,cAAc,CAAC+D,MAAM,CAAC,CAAA;EACjC,GAAA;IAEA,SAASQ,aAAaA,CAACX,OAAO,EAAE;EAC9BY,IAAAA,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEb,OAAO,CAAC,CAAA;EAC3C,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,OAAOW,aAAa,CAAA;EACtB;;ECtIO,IAAMG,MAAI,GAAGxE,MAAM,CAAC,CACzB,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,KAAK,EACL,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,GAAG,EACH,SAAS,EACT,KAAK,EACL,UAAU,EACV,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,GAAG,EACH,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;EAEF;EACO,IAAMyE,KAAG,GAAGzE,MAAM,CAAC,CACxB,KAAK,EACL,GAAG,EACH,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,EACH,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR,CAAC,CAAA;EAEK,IAAM0E,UAAU,GAAG1E,MAAM,CAAC,CAC/B,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC,CAAA;;EAEF;EACA;EACA;EACA;EACO,IAAM2E,aAAa,GAAG3E,MAAM,CAAC,CAClC,SAAS,EACT,eAAe,EACf,QAAQ,EACR,SAAS,EACT,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,OAAO,EACP,WAAW,EACX,MAAM,EACN,cAAc,EACd,WAAW,EACX,SAAS,EACT,eAAe,EACf,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,SAAS,EACT,KAAK,CACN,CAAC,CAAA;EAEK,IAAM4E,QAAM,GAAG5E,MAAM,CAAC,CAC3B,MAAM,EACN,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,YAAY,CACb,CAAC,CAAA;;EAEF;EACA;EACO,IAAM6E,gBAAgB,GAAG7E,MAAM,CAAC,CACrC,SAAS,EACT,aAAa,EACb,YAAY,EACZ,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,MAAM,CACP,CAAC,CAAA;EAEK,IAAM8E,IAAI,GAAG9E,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;;ECpR9B,IAAMwE,IAAI,GAAGxE,MAAM,CAAC,CACzB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,sBAAsB,EACtB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACb,UAAU,EACV,UAAU,EACV,SAAS,EACT,KAAK,EACL,UAAU,EACV,yBAAyB,EACzB,uBAAuB,EACvB,UAAU,EACV,WAAW,EACX,SAAS,EACT,cAAc,EACd,MAAM,EACN,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,EACX,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa,EACb,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EACZ,UAAU,EACV,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,YAAY,EACZ,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACX,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,CACP,CAAC,CAAA;EAEK,IAAMyE,GAAG,GAAGzE,MAAM,CAAC,CACxB,eAAe,EACf,YAAY,EACZ,UAAU,EACV,oBAAoB,EACpB,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,eAAe,EACf,WAAW,EACX,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,6BAA6B,EAC7B,eAAe,EACf,iBAAiB,EACjB,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,WAAW,EACX,SAAS,EACT,SAAS,EACT,KAAK,EACL,UAAU,EACV,WAAW,EACX,KAAK,EACL,MAAM,EACN,cAAc,EACd,WAAW,EACX,QAAQ,EACR,aAAa,EACb,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,iBAAiB,EACjB,IAAI,EACJ,KAAK,EACL,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACV,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,kBAAkB,EAClB,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,UAAU,EACV,aAAa,EACb,MAAM,EACN,YAAY,EACZ,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,gBAAgB,EAChB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACd,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,eAAe,EACf,OAAO,EACP,cAAc,EACd,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,YAAY,CACb,CAAC,CAAA;EAEK,IAAM4E,MAAM,GAAG5E,MAAM,CAAC,CAC3B,QAAQ,EACR,aAAa,EACb,OAAO,EACP,UAAU,EACV,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,KAAK,EACL,SAAS,EACT,cAAc,EACd,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,SAAS,EACT,eAAe,EACf,UAAU,EACV,UAAU,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,eAAe,EACf,sBAAsB,EACtB,WAAW,EACX,WAAW,EACX,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,CACR,CAAC,CAAA;EAEK,IAAM+E,GAAG,GAAG/E,MAAM,CAAC,CACxB,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,WAAW,EACX,aAAa,CACd,CAAC;;ECvWF;EACO,IAAMgF,aAAa,GAAG/E,IAAI,CAAC,2BAA2B,CAAC,CAAC;EACxD,IAAMgF,QAAQ,GAAGhF,IAAI,CAAC,uBAAuB,CAAC,CAAA;EAC9C,IAAMiF,WAAW,GAAGjF,IAAI,CAAC,eAAe,CAAC,CAAA;EACzC,IAAMkF,SAAS,GAAGlF,IAAI,CAAC,4BAA4B,CAAC,CAAC;EACrD,IAAMmF,SAAS,GAAGnF,IAAI,CAAC,gBAAgB,CAAC,CAAC;EACzC,IAAMoF,cAAc,GAAGpF,IAAI,CAChC,uFAAuF;EACzF,CAAC,CAAA;EACM,IAAMqF,iBAAiB,GAAGrF,IAAI,CAAC,uBAAuB,CAAC,CAAA;EACvD,IAAMsF,eAAe,GAAGtF,IAAI,CACjC,6DAA6D;EAC/D,CAAC,CAAA;EACM,IAAMuF,YAAY,GAAGvF,IAAI,CAAC,SAAS,CAAC,CAAA;EACpC,IAAMwF,cAAc,GAAGxF,IAAI,CAAC,0BAA0B,CAAC;;ECM9D,IAAMyF,SAAS,GAAG,SAAZA,SAASA,GAAA;EAAA,EAAA,OAAU,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;EAAA,CAAC,CAAA;;EAEvE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,YAAY,EAAEC,QAAQ,EAAE;EAClE,EAAA,IACEC,OAAA,CAAOF,YAAY,CAAA,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACG,YAAY,KAAK,UAAU,EAC/C;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA;EACA;IACA,IAAIC,MAAM,GAAG,IAAI,CAAA;IACjB,IAAMC,SAAS,GAAG,uBAAuB,CAAA;EACzC,EAAA,IACEJ,QAAQ,CAACK,aAAa,IACtBL,QAAQ,CAACK,aAAa,CAACC,YAAY,CAACF,SAAS,CAAC,EAC9C;MACAD,MAAM,GAAGH,QAAQ,CAACK,aAAa,CAACE,YAAY,CAACH,SAAS,CAAC,CAAA;EACzD,GAAA;IAEA,IAAMI,UAAU,GAAG,WAAW,IAAIL,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;IAE7D,IAAI;EACF,IAAA,OAAOJ,YAAY,CAACG,YAAY,CAACM,UAAU,EAAE;QAC3CC,UAAU,EAAA,SAAAA,UAAC/B,CAAAA,IAAI,EAAE;EACf,QAAA,OAAOA,IAAI,CAAA;SACZ;QACDgC,eAAe,EAAA,SAAAA,eAACC,CAAAA,SAAS,EAAE;EACzB,QAAA,OAAOA,SAAS,CAAA;EAClB,OAAA;EACF,KAAC,CAAC,CAAA;KACH,CAAC,OAAOC,CAAC,EAAE;EACV;EACA;EACA;MACApC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAG+B,UAAU,GAAG,wBACxC,CAAC,CAAA;EACD,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EACF,CAAC,CAAA;EAED,SAASK,eAAeA,GAAuB;EAAA,EAAA,IAAtBhB,MAAM,GAAA5C,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAA6D,CAAAA,CAAAA,KAAAA,SAAA,GAAA7D,SAAA,CAAG2C,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;EAC3C,EAAA,IAAMmB,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAA;MAAA,OAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;EAAA,GAAA,CAAA;;EAEjD;EACF;EACA;EACA;IACED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;;EAE3B;EACF;EACA;EACA;IACEH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;EAEtB,EAAA,IAAI,CAACtB,MAAM,IAAI,CAACA,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACoB,QAAQ,KAAK,CAAC,EAAE;EACjE;EACA;MACAL,SAAS,CAACM,WAAW,GAAG,KAAK,CAAA;EAE7B,IAAA,OAAON,SAAS,CAAA;EAClB,GAAA;EAEA,EAAA,IAAMO,gBAAgB,GAAGzB,MAAM,CAACG,QAAQ,CAAA;EAExC,EAAA,IAAMA,QAAQ,GAAKH,MAAM,CAAnBG,QAAQ,CAAA;EACd,EAAA,IACEuB,gBAAgB,GASd1B,MAAM,CATR0B,gBAAgB;MAChBC,mBAAmB,GAQjB3B,MAAM,CARR2B,mBAAmB;MACnBC,IAAI,GAOF5B,MAAM,CAPR4B,IAAI;MACJC,OAAO,GAML7B,MAAM,CANR6B,OAAO;MACPC,UAAU,GAKR9B,MAAM,CALR8B,UAAU;MAAAC,oBAAA,GAKR/B,MAAM,CAJRgC,YAAY;MAAZA,YAAY,GAAAD,oBAAA,KAAA,KAAA,CAAA,GAAG/B,MAAM,CAACgC,YAAY,IAAIhC,MAAM,CAACiC,eAAe,GAAAF,oBAAA;MAC5DG,eAAe,GAGblC,MAAM,CAHRkC,eAAe;MACfC,SAAS,GAEPnC,MAAM,CAFRmC,SAAS;MACTjC,YAAY,GACVF,MAAM,CADRE,YAAY,CAAA;EAGd,EAAA,IAAMkC,gBAAgB,GAAGP,OAAO,CAACvG,SAAS,CAAA;EAE1C,EAAA,IAAM+G,SAAS,GAAGhE,YAAY,CAAC+D,gBAAgB,EAAE,WAAW,CAAC,CAAA;EAC7D,EAAA,IAAME,cAAc,GAAGjE,YAAY,CAAC+D,gBAAgB,EAAE,aAAa,CAAC,CAAA;EACpE,EAAA,IAAMG,aAAa,GAAGlE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;EAClE,EAAA,IAAMI,aAAa,GAAGnE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;;EAElE;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,IAAI,OAAOT,mBAAmB,KAAK,UAAU,EAAE;EAC7C,IAAA,IAAMc,QAAQ,GAAGtC,QAAQ,CAACuC,aAAa,CAAC,UAAU,CAAC,CAAA;MACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;EACtDzC,MAAAA,QAAQ,GAAGsC,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;EAC3C,KAAA;EACF,GAAA;EAEA,EAAA,IAAMC,kBAAkB,GAAG5C,yBAAyB,CAClDC,YAAY,EACZuB,gBACF,CAAC,CAAA;IACD,IAAMqB,SAAS,GAAGD,kBAAkB,GAAGA,kBAAkB,CAACjC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;IAE7E,IAAAmC,SAAA,GAKI5C,QAAQ;MAJV6C,cAAc,GAAAD,SAAA,CAAdC,cAAc;MACdC,kBAAkB,GAAAF,SAAA,CAAlBE,kBAAkB;MAClBC,sBAAsB,GAAAH,SAAA,CAAtBG,sBAAsB;MACtBC,oBAAoB,GAAAJ,SAAA,CAApBI,oBAAoB,CAAA;EAEtB,EAAA,IAAQC,UAAU,GAAK3B,gBAAgB,CAA/B2B,UAAU,CAAA;IAElB,IAAIC,YAAY,GAAG,EAAE,CAAA;IACrB,IAAI;EACFA,IAAAA,YAAY,GAAGpF,KAAK,CAACkC,QAAQ,CAAC,CAACkD,YAAY,GAAGlD,QAAQ,CAACkD,YAAY,GAAG,EAAE,CAAA;EAC1E,GAAC,CAAC,OAAOtC,CAAC,EAAE,EAAC;IAEb,IAAIuC,KAAK,GAAG,EAAE,CAAA;;EAEd;EACF;EACA;EACEpC,EAAAA,SAAS,CAACM,WAAW,GACnB,OAAOgB,aAAa,KAAK,UAAU,IACnCQ,cAAc,IACdA,cAAc,CAACO,kBAAkB,KAAKtC,SAAS,IAC/CoC,YAAY,KAAK,CAAC,CAAA;EAEpB,EAAA,IACEhE,eAAa,GAQXmE,aARW;MACblE,UAAQ,GAONkE,QAPM;MACRjE,aAAW,GAMTiE,WANS;MACXhE,WAAS,GAKPgE,SALO;MACT/D,WAAS,GAIP+D,SAJO;MACT7D,mBAAiB,GAGf6D,iBAHe;MACjB5D,iBAAe,GAEb4D,eAFa;MACf1D,gBAAc,GACZ0D,cADY,CAAA;EAGhB,EAAA,IAAM9D,gBAAc,GAAK8D,cAAL,CAAA;;EAEpB;EACF;EACA;EACA;;EAEE;IACA,IAAIC,YAAY,GAAG,IAAI,CAAA;EACvB,EAAA,IAAMC,oBAAoB,GAAGjG,QAAQ,CAAC,EAAE,EAAAkG,EAAAA,CAAAA,MAAA,CAAAzI,kBAAA,CACnC0I,MAAS,CAAA1I,EAAAA,kBAAA,CACT0I,KAAQ,CAAA,EAAA1I,kBAAA,CACR0I,UAAe,CAAA,EAAA1I,kBAAA,CACf0I,QAAW,GAAA1I,kBAAA,CACX0I,IAAS,EACb,CAAC,CAAA;;EAEF;IACA,IAAIC,YAAY,GAAG,IAAI,CAAA;EACvB,EAAA,IAAMC,oBAAoB,GAAGrG,QAAQ,CAAC,EAAE,EAAA,EAAA,CAAAkG,MAAA,CAAAzI,kBAAA,CACnC6I,IAAU,CAAA7I,EAAAA,kBAAA,CACV6I,GAAS,CAAA7I,EAAAA,kBAAA,CACT6I,MAAY,CAAA,EAAA7I,kBAAA,CACZ6I,GAAS,EACb,CAAC,CAAA;;EAEF;EACF;EACA;EACA;EACA;EACA;IACE,IAAIC,uBAAuB,GAAGhK,MAAM,CAACM,IAAI,CACvCN,MAAM,CAACO,MAAM,CAAC,IAAI,EAAE;EAClB0J,IAAAA,YAAY,EAAE;EACZC,MAAAA,QAAQ,EAAE,IAAI;EACdC,MAAAA,YAAY,EAAE,KAAK;EACnBC,MAAAA,UAAU,EAAE,IAAI;EAChB3F,MAAAA,KAAK,EAAE,IAAA;OACR;EACD4F,IAAAA,kBAAkB,EAAE;EAClBH,MAAAA,QAAQ,EAAE,IAAI;EACdC,MAAAA,YAAY,EAAE,KAAK;EACnBC,MAAAA,UAAU,EAAE,IAAI;EAChB3F,MAAAA,KAAK,EAAE,IAAA;OACR;EACD6F,IAAAA,8BAA8B,EAAE;EAC9BJ,MAAAA,QAAQ,EAAE,IAAI;EACdC,MAAAA,YAAY,EAAE,KAAK;EACnBC,MAAAA,UAAU,EAAE,IAAI;EAChB3F,MAAAA,KAAK,EAAE,KAAA;EACT,KAAA;EACF,GAAC,CACH,CAAC,CAAA;;EAED;IACA,IAAI8F,WAAW,GAAG,IAAI,CAAA;;EAEtB;IACA,IAAIC,WAAW,GAAG,IAAI,CAAA;;EAEtB;IACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;EAE1B;IACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;EAE1B;IACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;;EAEnC;EACF;IACE,IAAIC,wBAAwB,GAAG,IAAI,CAAA;;EAEnC;EACF;EACA;IACE,IAAIC,kBAAkB,GAAG,KAAK,CAAA;;EAE9B;EACF;EACA;IACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;EAEvB;IACA,IAAIC,cAAc,GAAG,KAAK,CAAA;;EAE1B;IACA,IAAIC,UAAU,GAAG,KAAK,CAAA;;EAEtB;EACF;IACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;EAEtB;EACF;EACA;EACA;IACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;EAEtB;EACF;IACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;EAE/B;EACF;IACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;EAE/B;EACF;EACA;IACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;EAEvB;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACE,IAAIC,oBAAoB,GAAG,KAAK,CAAA;IAChC,IAAMC,2BAA2B,GAAG,eAAe,CAAA;;EAEnD;IACA,IAAIC,YAAY,GAAG,IAAI,CAAA;;EAEvB;EACF;IACE,IAAIC,QAAQ,GAAG,KAAK,CAAA;;EAEpB;IACA,IAAIC,YAAY,GAAG,EAAE,CAAA;;EAErB;IACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAC1B,IAAMC,uBAAuB,GAAGnI,QAAQ,CAAC,EAAE,EAAE,CAC3C,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;EAEF;IACA,IAAIoI,aAAa,GAAG,IAAI,CAAA;IACxB,IAAMC,qBAAqB,GAAGrI,QAAQ,CAAC,EAAE,EAAE,CACzC,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC,CAAA;;EAEF;IACA,IAAIsI,mBAAmB,GAAG,IAAI,CAAA;EAC9B,EAAA,IAAMC,2BAA2B,GAAGvI,QAAQ,CAAC,EAAE,EAAE,CAC/C,KAAK,EACL,OAAO,EACP,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR,CAAC,CAAA;IAEF,IAAMwI,gBAAgB,GAAG,oCAAoC,CAAA;IAC7D,IAAMC,aAAa,GAAG,4BAA4B,CAAA;IAClD,IAAMC,cAAc,GAAG,8BAA8B,CAAA;EACrD;IACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;IAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;;EAE1B;IACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;EAC7B,EAAA,IAAMC,0BAA0B,GAAG9I,QAAQ,CACzC,EAAE,EACF,CAACwI,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EACjDpK,cACF,CAAC,CAAA;;EAED;EACA,EAAA,IAAIyK,iBAAiB,CAAA;EACrB,EAAA,IAAMC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAA;IAC3E,IAAMC,yBAAyB,GAAG,WAAW,CAAA;EAC7C,EAAA,IAAI9I,iBAAiB,CAAA;;EAErB;IACA,IAAI+I,MAAM,GAAG,IAAI,CAAA;;EAEjB;IACA,IAAMC,iBAAiB,GAAG,GAAG,CAAA;;EAE7B;EACA;;EAEA,EAAA,IAAMC,WAAW,GAAG1G,QAAQ,CAACuC,aAAa,CAAC,MAAM,CAAC,CAAA;EAElD,EAAA,IAAMoE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,SAAS,EAAE;EAC7C,IAAA,OAAOA,SAAS,YAAYrK,MAAM,IAAIqK,SAAS,YAAYC,QAAQ,CAAA;KACpE,CAAA;;EAED;EACF;EACA;EACA;EACA;EACE;EACA,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;EAClC,IAAA,IAAIP,MAAM,IAAIA,MAAM,KAAKO,GAAG,EAAE;EAC5B,MAAA,OAAA;EACF,KAAA;;EAEA;MACA,IAAI,CAACA,GAAG,IAAI9G,OAAA,CAAO8G,GAAG,CAAA,KAAK,QAAQ,EAAE;QACnCA,GAAG,GAAG,EAAE,CAAA;EACV,KAAA;;EAEA;EACAA,IAAAA,GAAG,GAAGjJ,KAAK,CAACiJ,GAAG,CAAC,CAAA;MAEhBV,iBAAiB;EACf;EACAC,IAAAA,4BAA4B,CAACnK,OAAO,CAAC4K,GAAG,CAACV,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC7DA,iBAAiB,GAAGE,yBAAyB,GAC7CF,iBAAiB,GAAGU,GAAG,CAACV,iBAAkB,CAAA;;EAEjD;EACA5I,IAAAA,iBAAiB,GACf4I,iBAAiB,KAAK,uBAAuB,GACzCzK,cAAc,GACdH,iBAAiB,CAAA;;EAEvB;EACA6H,IAAAA,YAAY,GACV,cAAc,IAAIyD,GAAG,GACjBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAACzD,YAAY,EAAE7F,iBAAiB,CAAC,GACjD8F,oBAAoB,CAAA;EAC1BG,IAAAA,YAAY,GACV,cAAc,IAAIqD,GAAG,GACjBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAACrD,YAAY,EAAEjG,iBAAiB,CAAC,GACjDkG,oBAAoB,CAAA;EAC1BwC,IAAAA,kBAAkB,GAChB,oBAAoB,IAAIY,GAAG,GACvBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAACZ,kBAAkB,EAAEvK,cAAc,CAAC,GACpDwK,0BAA0B,CAAA;MAChCR,mBAAmB,GACjB,mBAAmB,IAAImB,GAAG,GACtBzJ,QAAQ,CACNQ,KAAK,CAAC+H,2BAA2B,CAAC;EAAE;EACpCkB,IAAAA,GAAG,CAACC,iBAAiB;EAAE;EACvBvJ,IAAAA,iBAAiB;EACnB,KAAC;EAAC,MACFoI,2BAA2B,CAAA;MACjCH,aAAa,GACX,mBAAmB,IAAIqB,GAAG,GACtBzJ,QAAQ,CACNQ,KAAK,CAAC6H,qBAAqB,CAAC;EAAE;EAC9BoB,IAAAA,GAAG,CAACE,iBAAiB;EAAE;EACvBxJ,IAAAA,iBAAiB;EACnB,KAAC;EAAC,MACFkI,qBAAqB,CAAA;EAC3BH,IAAAA,eAAe,GACb,iBAAiB,IAAIuB,GAAG,GACpBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAACvB,eAAe,EAAE/H,iBAAiB,CAAC,GACpDgI,uBAAuB,CAAA;EAC7BrB,IAAAA,WAAW,GACT,aAAa,IAAI2C,GAAG,GAChBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAAC3C,WAAW,EAAE3G,iBAAiB,CAAC,GAChD,EAAE,CAAA;EACR4G,IAAAA,WAAW,GACT,aAAa,IAAI0C,GAAG,GAChBzJ,QAAQ,CAAC,EAAE,EAAEyJ,GAAG,CAAC1C,WAAW,EAAE5G,iBAAiB,CAAC,GAChD,EAAE,CAAA;MACR8H,YAAY,GAAG,cAAc,IAAIwB,GAAG,GAAGA,GAAG,CAACxB,YAAY,GAAG,KAAK,CAAA;EAC/DjB,IAAAA,eAAe,GAAGyC,GAAG,CAACzC,eAAe,KAAK,KAAK,CAAC;EAChDC,IAAAA,eAAe,GAAGwC,GAAG,CAACxC,eAAe,KAAK,KAAK,CAAC;EAChDC,IAAAA,uBAAuB,GAAGuC,GAAG,CAACvC,uBAAuB,IAAI,KAAK,CAAC;EAC/DC,IAAAA,wBAAwB,GAAGsC,GAAG,CAACtC,wBAAwB,KAAK,KAAK,CAAC;EAClEC,IAAAA,kBAAkB,GAAGqC,GAAG,CAACrC,kBAAkB,IAAI,KAAK,CAAC;EACrDC,IAAAA,YAAY,GAAGoC,GAAG,CAACpC,YAAY,KAAK,KAAK,CAAC;EAC1CC,IAAAA,cAAc,GAAGmC,GAAG,CAACnC,cAAc,IAAI,KAAK,CAAC;EAC7CG,IAAAA,UAAU,GAAGgC,GAAG,CAAChC,UAAU,IAAI,KAAK,CAAC;EACrCC,IAAAA,mBAAmB,GAAG+B,GAAG,CAAC/B,mBAAmB,IAAI,KAAK,CAAC;EACvDC,IAAAA,mBAAmB,GAAG8B,GAAG,CAAC9B,mBAAmB,IAAI,KAAK,CAAC;EACvDH,IAAAA,UAAU,GAAGiC,GAAG,CAACjC,UAAU,IAAI,KAAK,CAAC;EACrCI,IAAAA,YAAY,GAAG6B,GAAG,CAAC7B,YAAY,KAAK,KAAK,CAAC;EAC1CC,IAAAA,oBAAoB,GAAG4B,GAAG,CAAC5B,oBAAoB,IAAI,KAAK,CAAC;EACzDE,IAAAA,YAAY,GAAG0B,GAAG,CAAC1B,YAAY,KAAK,KAAK,CAAC;EAC1CC,IAAAA,QAAQ,GAAGyB,GAAG,CAACzB,QAAQ,IAAI,KAAK,CAAC;EACjC/F,IAAAA,gBAAc,GAAGwH,GAAG,CAACG,kBAAkB,IAAI3H,gBAAc,CAAA;EACzD0G,IAAAA,SAAS,GAAGc,GAAG,CAACd,SAAS,IAAID,cAAc,CAAA;EAC3CnC,IAAAA,uBAAuB,GAAGkD,GAAG,CAAClD,uBAAuB,IAAI,EAAE,CAAA;EAC3D,IAAA,IACEkD,GAAG,CAAClD,uBAAuB,IAC3B8C,iBAAiB,CAACI,GAAG,CAAClD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;EACAD,MAAAA,uBAAuB,CAACC,YAAY,GAClCiD,GAAG,CAAClD,uBAAuB,CAACC,YAAY,CAAA;EAC5C,KAAA;EAEA,IAAA,IACEiD,GAAG,CAAClD,uBAAuB,IAC3B8C,iBAAiB,CAACI,GAAG,CAAClD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;EACAL,MAAAA,uBAAuB,CAACK,kBAAkB,GACxC6C,GAAG,CAAClD,uBAAuB,CAACK,kBAAkB,CAAA;EAClD,KAAA;EAEA,IAAA,IACE6C,GAAG,CAAClD,uBAAuB,IAC3B,OAAOkD,GAAG,CAAClD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;EACAN,MAAAA,uBAAuB,CAACM,8BAA8B,GACpD4C,GAAG,CAAClD,uBAAuB,CAACM,8BAA8B,CAAA;EAC9D,KAAA;EAEA,IAAA,IAAIO,kBAAkB,EAAE;EACtBH,MAAAA,eAAe,GAAG,KAAK,CAAA;EACzB,KAAA;EAEA,IAAA,IAAIS,mBAAmB,EAAE;EACvBD,MAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,KAAA;;EAEA;EACA,IAAA,IAAIQ,YAAY,EAAE;EAChBjC,MAAAA,YAAY,GAAGhG,QAAQ,CAAC,EAAE,EAAAvC,kBAAA,CAAM0I,IAAS,CAAC,CAAC,CAAA;EAC3CC,MAAAA,YAAY,GAAG,EAAE,CAAA;EACjB,MAAA,IAAI6B,YAAY,CAAC7G,IAAI,KAAK,IAAI,EAAE;EAC9BpB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,MAAS,CAAC,CAAA;EACjCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,IAAU,CAAC,CAAA;EACpC,OAAA;EAEA,MAAA,IAAI2B,YAAY,CAAC5G,GAAG,KAAK,IAAI,EAAE;EAC7BrB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,KAAQ,CAAC,CAAA;EAChCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;EACjCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;EACnC,OAAA;EAEA,MAAA,IAAI2B,YAAY,CAAC3G,UAAU,KAAK,IAAI,EAAE;EACpCtB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,UAAe,CAAC,CAAA;EACvCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;EACjCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;EACnC,OAAA;EAEA,MAAA,IAAI2B,YAAY,CAACzG,MAAM,KAAK,IAAI,EAAE;EAChCxB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,QAAW,CAAC,CAAA;EACnCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,MAAY,CAAC,CAAA;EACpCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;EACnC,OAAA;EACF,KAAA;;EAEA;MACA,IAAImD,GAAG,CAACI,QAAQ,EAAE;QAChB,IAAI7D,YAAY,KAAKC,oBAAoB,EAAE;EACzCD,QAAAA,YAAY,GAAGxF,KAAK,CAACwF,YAAY,CAAC,CAAA;EACpC,OAAA;QAEAhG,QAAQ,CAACgG,YAAY,EAAEyD,GAAG,CAACI,QAAQ,EAAE1J,iBAAiB,CAAC,CAAA;EACzD,KAAA;MAEA,IAAIsJ,GAAG,CAACK,QAAQ,EAAE;QAChB,IAAI1D,YAAY,KAAKC,oBAAoB,EAAE;EACzCD,QAAAA,YAAY,GAAG5F,KAAK,CAAC4F,YAAY,CAAC,CAAA;EACpC,OAAA;QAEApG,QAAQ,CAACoG,YAAY,EAAEqD,GAAG,CAACK,QAAQ,EAAE3J,iBAAiB,CAAC,CAAA;EACzD,KAAA;MAEA,IAAIsJ,GAAG,CAACC,iBAAiB,EAAE;QACzB1J,QAAQ,CAACsI,mBAAmB,EAAEmB,GAAG,CAACC,iBAAiB,EAAEvJ,iBAAiB,CAAC,CAAA;EACzE,KAAA;MAEA,IAAIsJ,GAAG,CAACvB,eAAe,EAAE;QACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;EAC/CD,QAAAA,eAAe,GAAG1H,KAAK,CAAC0H,eAAe,CAAC,CAAA;EAC1C,OAAA;QAEAlI,QAAQ,CAACkI,eAAe,EAAEuB,GAAG,CAACvB,eAAe,EAAE/H,iBAAiB,CAAC,CAAA;EACnE,KAAA;;EAEA;EACA,IAAA,IAAI4H,YAAY,EAAE;EAChB/B,MAAAA,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;EAC9B,KAAA;;EAEA;EACA,IAAA,IAAIsB,cAAc,EAAE;QAClBtH,QAAQ,CAACgG,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;EAClD,KAAA;;EAEA;MACA,IAAIA,YAAY,CAAC+D,KAAK,EAAE;EACtB/J,MAAAA,QAAQ,CAACgG,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,CAAA;QACjC,OAAOc,WAAW,CAACkD,KAAK,CAAA;EAC1B,KAAA;;EAEA;EACA;EACA,IAAA,IAAIpN,MAAM,EAAE;QACVA,MAAM,CAAC6M,GAAG,CAAC,CAAA;EACb,KAAA;EAEAP,IAAAA,MAAM,GAAGO,GAAG,CAAA;KACb,CAAA;EAED,EAAA,IAAMQ,8BAA8B,GAAGjK,QAAQ,CAAC,EAAE,EAAE,CAClD,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAAC,CAAA;EAEF,EAAA,IAAMkK,uBAAuB,GAAGlK,QAAQ,CAAC,EAAE,EAAE,CAC3C,eAAe,EACf,gBAAgB,CACjB,CAAC,CAAA;;EAEF;EACA;EACA;EACA;EACA,EAAA,IAAMmK,4BAA4B,GAAGnK,QAAQ,CAAC,EAAE,EAAE,CAChD,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,CACT,CAAC,CAAA;;EAEF;EACF;EACA;IACE,IAAMoK,YAAY,GAAGpK,QAAQ,CAAC,EAAE,EAAEmG,KAAQ,CAAC,CAAA;EAC3CnG,EAAAA,QAAQ,CAACoK,YAAY,EAAEjE,UAAe,CAAC,CAAA;EACvCnG,EAAAA,QAAQ,CAACoK,YAAY,EAAEjE,aAAkB,CAAC,CAAA;IAE1C,IAAMkE,eAAe,GAAGrK,QAAQ,CAAC,EAAE,EAAEmG,QAAW,CAAC,CAAA;EACjDnG,EAAAA,QAAQ,CAACqK,eAAe,EAAElE,gBAAqB,CAAC,CAAA;;EAEhD;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMmE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAahK,OAAO,EAAE;EAC9C,IAAA,IAAIiK,MAAM,GAAGxF,aAAa,CAACzE,OAAO,CAAC,CAAA;;EAEnC;EACA;EACA,IAAA,IAAI,CAACiK,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;EAC9BD,MAAAA,MAAM,GAAG;EACPE,QAAAA,YAAY,EAAE9B,SAAS;EACvB6B,QAAAA,OAAO,EAAE,UAAA;SACV,CAAA;EACH,KAAA;EAEA,IAAA,IAAMA,OAAO,GAAGrM,iBAAiB,CAACmC,OAAO,CAACkK,OAAO,CAAC,CAAA;EAClD,IAAA,IAAME,aAAa,GAAGvM,iBAAiB,CAACoM,MAAM,CAACC,OAAO,CAAC,CAAA;EAEvD,IAAA,IAAI,CAAC3B,kBAAkB,CAACvI,OAAO,CAACmK,YAAY,CAAC,EAAE;EAC7C,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EAEA,IAAA,IAAInK,OAAO,CAACmK,YAAY,KAAKhC,aAAa,EAAE;EAC1C;EACA;EACA;EACA,MAAA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;UAC1C,OAAO8B,OAAO,KAAK,KAAK,CAAA;EAC1B,OAAA;;EAEA;EACA;EACA;EACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;EAC5C,QAAA,OACEgC,OAAO,KAAK,KAAK,KAChBE,aAAa,KAAK,gBAAgB,IACjCT,8BAA8B,CAACS,aAAa,CAAC,CAAC,CAAA;EAEpD,OAAA;;EAEA;EACA;EACA,MAAA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;EACvC,KAAA;EAEA,IAAA,IAAIlK,OAAO,CAACmK,YAAY,KAAKjC,gBAAgB,EAAE;EAC7C;EACA;EACA;EACA,MAAA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;UAC1C,OAAO8B,OAAO,KAAK,MAAM,CAAA;EAC3B,OAAA;;EAEA;EACA;EACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;EACzC,QAAA,OAAO+B,OAAO,KAAK,MAAM,IAAIN,uBAAuB,CAACQ,aAAa,CAAC,CAAA;EACrE,OAAA;;EAEA;EACA;EACA,MAAA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;EAC1C,KAAA;EAEA,IAAA,IAAIlK,OAAO,CAACmK,YAAY,KAAK/B,cAAc,EAAE;EAC3C;EACA;EACA;QACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACyB,uBAAuB,CAACQ,aAAa,CAAC,EACvC;EACA,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;QAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACyB,8BAA8B,CAACS,aAAa,CAAC,EAC9C;EACA,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;;EAEA;EACA;EACA,MAAA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,KACxBL,4BAA4B,CAACK,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;EAErE,KAAA;;EAEA;MACA,IACEzB,iBAAiB,KAAK,uBAAuB,IAC7CF,kBAAkB,CAACvI,OAAO,CAACmK,YAAY,CAAC,EACxC;EACA,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA;EACA;EACA;EACA,IAAA,OAAO,KAAK,CAAA;KACb,CAAA;;EAED;EACF;EACA;EACA;EACA;EACE,EAAA,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAI,EAAE;EACnC5M,IAAAA,SAAS,CAACwF,SAAS,CAACI,OAAO,EAAE;EAAEvD,MAAAA,OAAO,EAAEuK,IAAAA;EAAK,KAAC,CAAC,CAAA;MAC/C,IAAI;EACF;EACAA,MAAAA,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC,CAAA;OAClC,CAAC,OAAOvH,CAAC,EAAE;QACV,IAAI;UACFuH,IAAI,CAACG,SAAS,GAAG3F,SAAS,CAAA;SAC3B,CAAC,OAAO/B,CAAC,EAAE;UACVuH,IAAI,CAACI,MAAM,EAAE,CAAA;EACf,OAAA;EACF,KAAA;KACD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;IACE,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAI,EAAEN,IAAI,EAAE;MAC7C,IAAI;EACF5M,MAAAA,SAAS,CAACwF,SAAS,CAACI,OAAO,EAAE;EAC3BuH,QAAAA,SAAS,EAAEP,IAAI,CAACQ,gBAAgB,CAACF,IAAI,CAAC;EACtCG,QAAAA,IAAI,EAAET,IAAAA;EACR,OAAC,CAAC,CAAA;OACH,CAAC,OAAOvH,CAAC,EAAE;EACVrF,MAAAA,SAAS,CAACwF,SAAS,CAACI,OAAO,EAAE;EAC3BuH,QAAAA,SAAS,EAAE,IAAI;EACfE,QAAAA,IAAI,EAAET,IAAAA;EACR,OAAC,CAAC,CAAA;EACJ,KAAA;EAEAA,IAAAA,IAAI,CAACU,eAAe,CAACJ,IAAI,CAAC,CAAA;;EAE1B;MACA,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC/E,YAAY,CAAC+E,IAAI,CAAC,EAAE;QACxC,IAAI1D,UAAU,IAAIC,mBAAmB,EAAE;UACrC,IAAI;YACFkD,YAAY,CAACC,IAAI,CAAC,CAAA;EACpB,SAAC,CAAC,OAAOvH,CAAC,EAAE,EAAC;EACf,OAAC,MAAM;UACL,IAAI;EACFuH,UAAAA,IAAI,CAACW,YAAY,CAACL,IAAI,EAAE,EAAE,CAAC,CAAA;EAC7B,SAAC,CAAC,OAAO7H,CAAC,EAAE,EAAC;EACf,OAAA;EACF,KAAA;KACD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMmI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAK,EAAE;EACrC;EACA,IAAA,IAAIC,GAAG,CAAA;EACP,IAAA,IAAIC,iBAAiB,CAAA;EAErB,IAAA,IAAIpE,UAAU,EAAE;QACdkE,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;EACrC,KAAC,MAAM;EACL;EACA,MAAA,IAAMG,OAAO,GAAGrN,WAAW,CAACkN,KAAK,EAAE,aAAa,CAAC,CAAA;EACjDE,MAAAA,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;EAC3C,KAAA;EAEA,IAAA,IACE9C,iBAAiB,KAAK,uBAAuB,IAC7CJ,SAAS,KAAKD,cAAc,EAC5B;EACA;EACAgD,MAAAA,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;EACpB,KAAA;MAEA,IAAMI,YAAY,GAAG1G,kBAAkB,GACnCA,kBAAkB,CAACjC,UAAU,CAACuI,KAAK,CAAC,GACpCA,KAAK,CAAA;EACT;EACJ;EACA;EACA;MACI,IAAI/C,SAAS,KAAKD,cAAc,EAAE;QAChC,IAAI;UACFiD,GAAG,GAAG,IAAIjH,SAAS,EAAE,CAACqH,eAAe,CAACD,YAAY,EAAE/C,iBAAiB,CAAC,CAAA;EACxE,OAAC,CAAC,OAAOzF,CAAC,EAAE,EAAC;EACf,KAAA;;EAEA;EACA,IAAA,IAAI,CAACqI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;QAChCL,GAAG,GAAGpG,cAAc,CAAC0G,cAAc,CAACtD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAChE,IAAI;UACFgD,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGtD,cAAc,GAC1CvD,SAAS,GACTyG,YAAY,CAAA;SACjB,CAAC,OAAOxI,CAAC,EAAE;EACV;EAAA,OAAA;EAEJ,KAAA;MAEA,IAAM6I,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;MAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;EAC9BO,MAAAA,IAAI,CAACC,YAAY,CACf1J,QAAQ,CAAC2J,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IACxB,CAAC,CAAA;EACH,KAAA;;EAEA;MACA,IAAI3D,SAAS,KAAKD,cAAc,EAAE;EAChC,MAAA,OAAOhD,oBAAoB,CAAC6G,IAAI,CAC9BZ,GAAG,EACHrE,cAAc,GAAG,MAAM,GAAG,MAC5B,CAAC,CAAC,CAAC,CAAC,CAAA;EACN,KAAA;EAEA,IAAA,OAAOA,cAAc,GAAGqE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;KACnD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,CAAa9I,IAAI,EAAE;MACtC,OAAO8B,kBAAkB,CAAC+G,IAAI,CAC5B7I,IAAI,CAACyB,aAAa,IAAIzB,IAAI,EAC1BA,IAAI;EACJ;MACAW,UAAU,CAACoI,YAAY,GACrBpI,UAAU,CAACqI,YAAY,GACvBrI,UAAU,CAACsI,SAAS,GACpBtI,UAAU,CAACuI,2BAA2B,GACtCvI,UAAU,CAACwI,kBAAkB,EAC/B,IAAI,EACJ,KACF,CAAC,CAAA;KACF,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;MAClC,OACEA,GAAG,YAAYtI,eAAe,KAC5B,OAAOsI,GAAG,CAACC,OAAO,KAAK,WAAW,IAClC,OAAOD,GAAG,CAACC,OAAO,KAAK,QAAQ,IAC9B,OAAOD,GAAG,CAACE,cAAc,KAAK,WAAW,IACxC,OAAOF,GAAG,CAACE,cAAc,KAAK,QAAS,IACzC,OAAOF,GAAG,CAACG,QAAQ,KAAK,QAAQ,IAChC,OAAOH,GAAG,CAACI,WAAW,KAAK,QAAQ,IACnC,OAAOJ,GAAG,CAAChC,WAAW,KAAK,UAAU,IACrC,EAAEgC,GAAG,CAACK,UAAU,YAAY7I,YAAY,CAAC,IACzC,OAAOwI,GAAG,CAACxB,eAAe,KAAK,UAAU,IACzC,OAAOwB,GAAG,CAACvB,YAAY,KAAK,UAAU,IACtC,OAAOuB,GAAG,CAACtC,YAAY,KAAK,QAAQ,IACpC,OAAOsC,GAAG,CAACX,YAAY,KAAK,UAAU,IACtC,OAAOW,GAAG,CAACM,aAAa,KAAK,UAAU,CAAC,CAAA;KAE7C,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAa7M,MAAM,EAAE;EAChC,IAAA,OAAOkC,OAAA,CAAOwB,IAAI,CAAA,KAAK,QAAQ,GAC3B1D,MAAM,YAAY0D,IAAI,GACtB1D,MAAM,IACJkC,OAAA,CAAOlC,MAAM,CAAK,KAAA,QAAQ,IAC1B,OAAOA,MAAM,CAACqD,QAAQ,KAAK,QAAQ,IACnC,OAAOrD,MAAM,CAACyM,QAAQ,KAAK,QAAQ,CAAA;KAC1C,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;IACE,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAaC,UAAU,EAAEC,WAAW,EAAEC,IAAI,EAAE;EAC5D,IAAA,IAAI,CAAC7H,KAAK,CAAC2H,UAAU,CAAC,EAAE;EACtB,MAAA,OAAA;EACF,KAAA;MAEA9P,YAAY,CAACmI,KAAK,CAAC2H,UAAU,CAAC,EAAE,UAACG,IAAI,EAAK;QACxCA,IAAI,CAACpB,IAAI,CAAC9I,SAAS,EAAEgK,WAAW,EAAEC,IAAI,EAAExE,MAAM,CAAC,CAAA;EACjD,KAAC,CAAC,CAAA;KACH,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAM0E,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAW,EAAE;EAC/C,IAAA,IAAIvI,OAAO,CAAA;;EAEX;EACAqI,IAAAA,YAAY,CAAC,wBAAwB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;;EAEzD;EACA,IAAA,IAAIX,YAAY,CAACW,WAAW,CAAC,EAAE;QAC7B7C,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;MACA,IAAIzO,UAAU,CAAC,iBAAiB,EAAEyO,WAAW,CAACP,QAAQ,CAAC,EAAE;QACvDtC,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IAAMjD,OAAO,GAAGrK,iBAAiB,CAACsN,WAAW,CAACP,QAAQ,CAAC,CAAA;;EAEvD;EACAK,IAAAA,YAAY,CAAC,qBAAqB,EAAEE,WAAW,EAAE;EAC/CjD,MAAAA,OAAO,EAAPA,OAAO;EACPqD,MAAAA,WAAW,EAAE7H,YAAAA;EACf,KAAC,CAAC,CAAA;;EAEF;MACA,IACEyH,WAAW,CAACJ,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAiB,CAAC,KACtC,CAACR,OAAO,CAACG,WAAW,CAACvI,OAAO,CAAC,IAC5B,CAACoI,OAAO,CAACG,WAAW,CAACvI,OAAO,CAAC4I,iBAAiB,CAAC,CAAC,IAClD9O,UAAU,CAAC,SAAS,EAAEyO,WAAW,CAACvB,SAAS,CAAC,IAC5ClN,UAAU,CAAC,SAAS,EAAEyO,WAAW,CAACN,WAAW,CAAC,EAC9C;QACAvC,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IACEjD,OAAO,KAAK,QAAQ,IACpBxL,UAAU,CAAC,YAAY,EAAEyO,WAAW,CAACvB,SAAS,CAAC,EAC/C;QACAtB,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IAAIA,WAAW,CAAC3J,QAAQ,KAAK,CAAC,EAAE;QAC9B8G,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IACEpG,YAAY,IACZoG,WAAW,CAAC3J,QAAQ,KAAK,CAAC,IAC1B9E,UAAU,CAAC,SAAS,EAAEyO,WAAW,CAACC,IAAI,CAAC,EACvC;QACA9C,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;MACA,IAAI,CAACzH,YAAY,CAACwE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;EAClD;QACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAIuD,uBAAuB,CAACvD,OAAO,CAAC,EAAE;EAC7D,QAAA,IACEjE,uBAAuB,CAACC,YAAY,YAAYvH,MAAM,IACtDD,UAAU,CAACuH,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EAEzD,OAAO,KAAK,CAAA;EACd,QAAA,IACEjE,uBAAuB,CAACC,YAAY,YAAY+C,QAAQ,IACxDhD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAE7C,OAAO,KAAK,CAAA;EAChB,OAAA;;EAEA;EACA,MAAA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;UAC7C,IAAMM,UAAU,GAAG/F,aAAa,CAAC0I,WAAW,CAAC,IAAIA,WAAW,CAAC3C,UAAU,CAAA;UACvE,IAAMwB,UAAU,GAAGxH,aAAa,CAAC2I,WAAW,CAAC,IAAIA,WAAW,CAACnB,UAAU,CAAA;UAEvE,IAAIA,UAAU,IAAIxB,UAAU,EAAE;EAC5B,UAAA,IAAMkD,UAAU,GAAG1B,UAAU,CAAC1M,MAAM,CAAA;EAEpC,UAAA,KAAK,IAAIqO,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;cACxC,IAAMC,UAAU,GAAGtJ,SAAS,CAAC0H,UAAU,CAAC2B,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;cACjDC,UAAU,CAACjB,cAAc,GAAG,CAACQ,WAAW,CAACR,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;cACjEnC,UAAU,CAACsB,YAAY,CAAC8B,UAAU,EAAErJ,cAAc,CAAC4I,WAAW,CAAC,CAAC,CAAA;EAClE,WAAA;EACF,SAAA;EACF,OAAA;QAEA7C,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;MACA,IAAIA,WAAW,YAAYrJ,OAAO,IAAI,CAACkG,oBAAoB,CAACmD,WAAW,CAAC,EAAE;QACxE7C,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;MACA,IACE,CAACjD,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxBxL,UAAU,CAAC,6BAA6B,EAAEyO,WAAW,CAACvB,SAAS,CAAC,EAChE;QACAtB,YAAY,CAAC6C,WAAW,CAAC,CAAA;EACzB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IAAIrG,kBAAkB,IAAIqG,WAAW,CAAC3J,QAAQ,KAAK,CAAC,EAAE;EACpD;QACAoB,OAAO,GAAGuI,WAAW,CAACN,WAAW,CAAA;QACjCjI,OAAO,GAAGxG,aAAa,CAACwG,OAAO,EAAEtD,eAAa,EAAE,GAAG,CAAC,CAAA;QACpDsD,OAAO,GAAGxG,aAAa,CAACwG,OAAO,EAAErD,UAAQ,EAAE,GAAG,CAAC,CAAA;QAC/CqD,OAAO,GAAGxG,aAAa,CAACwG,OAAO,EAAEpD,aAAW,EAAE,GAAG,CAAC,CAAA;EAClD,MAAA,IAAI2L,WAAW,CAACN,WAAW,KAAKjI,OAAO,EAAE;EACvCjH,QAAAA,SAAS,CAACwF,SAAS,CAACI,OAAO,EAAE;EAAEvD,UAAAA,OAAO,EAAEmN,WAAW,CAAC7I,SAAS,EAAC;EAAE,SAAC,CAAC,CAAA;UAClE6I,WAAW,CAACN,WAAW,GAAGjI,OAAO,CAAA;EACnC,OAAA;EACF,KAAA;;EAEA;EACAqI,IAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;EAExD,IAAA,OAAO,KAAK,CAAA;KACb,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACE;IACA,IAAMU,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,KAAK,EAAEC,MAAM,EAAErN,KAAK,EAAE;EACxD;MACA,IACE4G,YAAY,KACXyG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KACrCrN,KAAK,IAAI0B,QAAQ,IAChB1B,KAAK,IAAIoI,WAAW,IACpBpI,KAAK,KAAK,SAAS,IACnBA,KAAK,KAAK,gBAAgB,CAAC,EAC7B;EACA,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;;EAEA;EACJ;EACA;EACA;EACI,IAAA,IACEiG,eAAe,IACf,CAACF,WAAW,CAACsH,MAAM,CAAC,IACpBrP,UAAU,CAAC+C,WAAS,EAAEsM,MAAM,CAAC,EAC7B,CAED,MAAM,IAAIrH,eAAe,IAAIhI,UAAU,CAACgD,WAAS,EAAEqM,MAAM,CAAC,EAAE,CAG5D,MAAM,IAAI,CAACjI,YAAY,CAACiI,MAAM,CAAC,IAAItH,WAAW,CAACsH,MAAM,CAAC,EAAE;EACvD,MAAA;EACE;EACA;EACA;EACCN,MAAAA,uBAAuB,CAACK,KAAK,CAAC,KAC3B7H,uBAAuB,CAACC,YAAY,YAAYvH,MAAM,IACtDD,UAAU,CAACuH,uBAAuB,CAACC,YAAY,EAAE4H,KAAK,CAAC,IACtD7H,uBAAuB,CAACC,YAAY,YAAY+C,QAAQ,IACvDhD,uBAAuB,CAACC,YAAY,CAAC4H,KAAK,CAAE,CAAC,KAC/C7H,uBAAuB,CAACK,kBAAkB,YAAY3H,MAAM,IAC5DD,UAAU,CAACuH,uBAAuB,CAACK,kBAAkB,EAAEyH,MAAM,CAAC,IAC7D9H,uBAAuB,CAACK,kBAAkB,YAAY2C,QAAQ,IAC7DhD,uBAAuB,CAACK,kBAAkB,CAACyH,MAAM,CAAE,CAAC;EAC1D;EACA;EACCA,MAAAA,MAAM,KAAK,IAAI,IACd9H,uBAAuB,CAACM,8BAA8B,KACpDN,uBAAuB,CAACC,YAAY,YAAYvH,MAAM,IACtDD,UAAU,CAACuH,uBAAuB,CAACC,YAAY,EAAExF,KAAK,CAAC,IACtDuF,uBAAuB,CAACC,YAAY,YAAY+C,QAAQ,IACvDhD,uBAAuB,CAACC,YAAY,CAACxF,KAAK,CAAE,CAAE,EACpD,CAGD,MAAM;EACL,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EACA;EACF,KAAC,MAAM,IAAIsH,mBAAmB,CAAC+F,MAAM,CAAC,EAAE,CAIvC,MAAM,IACLrP,UAAU,CAACiD,gBAAc,EAAEvD,aAAa,CAACsC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID,MAAM,IACL,CAACkM,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBxP,aAAa,CAACoC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnCoH,aAAa,CAACgG,KAAK,CAAC,EACpB,CAKD,MAAM,IACLlH,uBAAuB,IACvB,CAAClI,UAAU,CAACkD,mBAAiB,EAAExD,aAAa,CAACsC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD,MAAM,IAAInB,KAAK,EAAE;EAChB,MAAA,OAAO,KAAK,CAAA;EACd,KAAC,MAAM,CAEL;EAGF,IAAA,OAAO,IAAI,CAAA;KACZ,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE,EAAA,IAAM+M,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAavD,OAAO,EAAE;MACjD,OAAOA,OAAO,KAAK,gBAAgB,IAAIhM,WAAW,CAACgM,OAAO,EAAEnI,gBAAc,CAAC,CAAA;KAC5E,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMiM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAab,WAAW,EAAE;EACjD,IAAA,IAAIc,IAAI,CAAA;EACR,IAAA,IAAIvN,KAAK,CAAA;EACT,IAAA,IAAIqN,MAAM,CAAA;EACV,IAAA,IAAIhO,CAAC,CAAA;EACL;EACAkN,IAAAA,YAAY,CAAC,0BAA0B,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;EAE3D,IAAA,IAAQL,UAAU,GAAKK,WAAW,CAA1BL,UAAU,CAAA;;EAElB;MACA,IAAI,CAACA,UAAU,EAAE;EACf,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAMoB,SAAS,GAAG;EAChBC,MAAAA,QAAQ,EAAE,EAAE;EACZC,MAAAA,SAAS,EAAE,EAAE;EACbC,MAAAA,QAAQ,EAAE,IAAI;EACdC,MAAAA,iBAAiB,EAAExI,YAAAA;OACpB,CAAA;MACD/F,CAAC,GAAG+M,UAAU,CAACxN,MAAM,CAAA;;EAErB;MACA,OAAOS,CAAC,EAAE,EAAE;EACVkO,MAAAA,IAAI,GAAGnB,UAAU,CAAC/M,CAAC,CAAC,CAAA;QACpB,IAAAwO,KAAA,GAA+BN,IAAI;UAA3BpD,IAAI,GAAA0D,KAAA,CAAJ1D,IAAI;UAAEV,YAAY,GAAAoE,KAAA,CAAZpE,YAAY,CAAA;EAC1BzJ,MAAAA,KAAK,GAAGmK,IAAI,KAAK,OAAO,GAAGoD,IAAI,CAACvN,KAAK,GAAGlC,UAAU,CAACyP,IAAI,CAACvN,KAAK,CAAC,CAAA;EAC9DqN,MAAAA,MAAM,GAAGlO,iBAAiB,CAACgL,IAAI,CAAC,CAAA;;EAEhC;QACAqD,SAAS,CAACC,QAAQ,GAAGJ,MAAM,CAAA;QAC3BG,SAAS,CAACE,SAAS,GAAG1N,KAAK,CAAA;QAC3BwN,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;EACzBH,MAAAA,SAAS,CAACM,aAAa,GAAGtL,SAAS,CAAC;EACpC+J,MAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAEe,SAAS,CAAC,CAAA;QAC7DxN,KAAK,GAAGwN,SAAS,CAACE,SAAS,CAAA;EAC3B;QACA,IAAIF,SAAS,CAACM,aAAa,EAAE;EAC3B,QAAA,SAAA;EACF,OAAA;;EAEA;EACA5D,MAAAA,gBAAgB,CAACC,IAAI,EAAEsC,WAAW,CAAC,CAAA;;EAEnC;EACA,MAAA,IAAI,CAACe,SAAS,CAACG,QAAQ,EAAE;EACvB,QAAA,SAAA;EACF,OAAA;;EAEA;QACA,IAAI,CAACxH,wBAAwB,IAAInI,UAAU,CAAC,MAAM,EAAEgC,KAAK,CAAC,EAAE;EAC1DkK,QAAAA,gBAAgB,CAACC,IAAI,EAAEsC,WAAW,CAAC,CAAA;EACnC,QAAA,SAAA;EACF,OAAA;;EAEA;QACA,IAAIpG,YAAY,IAAIrI,UAAU,CAAC,+BAA+B,EAAEgC,KAAK,CAAC,EAAE;EACtEkK,QAAAA,gBAAgB,CAACC,IAAI,EAAEsC,WAAW,CAAC,CAAA;EACnC,QAAA,SAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAIrG,kBAAkB,EAAE;UACtBpG,KAAK,GAAGtC,aAAa,CAACsC,KAAK,EAAEY,eAAa,EAAE,GAAG,CAAC,CAAA;UAChDZ,KAAK,GAAGtC,aAAa,CAACsC,KAAK,EAAEa,UAAQ,EAAE,GAAG,CAAC,CAAA;UAC3Cb,KAAK,GAAGtC,aAAa,CAACsC,KAAK,EAAEc,aAAW,EAAE,GAAG,CAAC,CAAA;EAChD,OAAA;;EAEA;EACA,MAAA,IAAMsM,KAAK,GAAGjO,iBAAiB,CAACsN,WAAW,CAACP,QAAQ,CAAC,CAAA;QACrD,IAAI,CAACiB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAErN,KAAK,CAAC,EAAE;EAC5C,QAAA,SAAA;EACF,OAAA;;EAEA;EACN;EACA;QACM,IAAI6G,oBAAoB,KAAKwG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;EAClE;EACAnD,QAAAA,gBAAgB,CAACC,IAAI,EAAEsC,WAAW,CAAC,CAAA;;EAEnC;UACAzM,KAAK,GAAG8G,2BAA2B,GAAG9G,KAAK,CAAA;EAC7C,OAAA;;EAEA;EACA,MAAA,IACEoE,kBAAkB,IAClBzC,OAAA,CAAOF,YAAY,CAAK,KAAA,QAAQ,IAChC,OAAOA,YAAY,CAACsM,gBAAgB,KAAK,UAAU,EACnD;EACA,QAAA,IAAItE,YAAY,EAAE,CAEjB,MAAM;EACL,UAAA,QAAQhI,YAAY,CAACsM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;EAClD,YAAA,KAAK,aAAa;EAAE,cAAA;EAClBrN,gBAAAA,KAAK,GAAGoE,kBAAkB,CAACjC,UAAU,CAACnC,KAAK,CAAC,CAAA;EAC5C,gBAAA,MAAA;EACF,eAAA;EAEA,YAAA,KAAK,kBAAkB;EAAE,cAAA;EACvBA,gBAAAA,KAAK,GAAGoE,kBAAkB,CAAChC,eAAe,CAACpC,KAAK,CAAC,CAAA;EACjD,gBAAA,MAAA;EACF,eAAA;EAKF,WAAA;EACF,SAAA;EACF,OAAA;;EAEA;QACA,IAAI;EACF,QAAA,IAAIyJ,YAAY,EAAE;YAChBgD,WAAW,CAACuB,cAAc,CAACvE,YAAY,EAAEU,IAAI,EAAEnK,KAAK,CAAC,CAAA;EACvD,SAAC,MAAM;EACL;EACAyM,UAAAA,WAAW,CAACjC,YAAY,CAACL,IAAI,EAAEnK,KAAK,CAAC,CAAA;EACvC,SAAA;EAEA,QAAA,IAAI8L,YAAY,CAACW,WAAW,CAAC,EAAE;YAC7B7C,YAAY,CAAC6C,WAAW,CAAC,CAAA;EAC3B,SAAC,MAAM;EACL1P,UAAAA,QAAQ,CAAC0F,SAAS,CAACI,OAAO,CAAC,CAAA;EAC7B,SAAA;EACF,OAAC,CAAC,OAAOP,CAAC,EAAE,EAAC;EACf,KAAA;;EAEA;EACAiK,IAAAA,YAAY,CAAC,yBAAyB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;KAC3D,CAAA;;EAED;EACF;EACA;EACA;EACA;EACE,EAAA,IAAMwB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAAQ,EAAE;EAC7C,IAAA,IAAIC,UAAU,CAAA;EACd,IAAA,IAAMC,cAAc,GAAG5C,eAAe,CAAC0C,QAAQ,CAAC,CAAA;;EAEhD;EACA3B,IAAAA,YAAY,CAAC,yBAAyB,EAAE2B,QAAQ,EAAE,IAAI,CAAC,CAAA;EAEvD,IAAA,OAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,EAAG;EAC/C;EACA9B,MAAAA,YAAY,CAAC,wBAAwB,EAAE4B,UAAU,EAAE,IAAI,CAAC,CAAA;;EAExD;EACA,MAAA,IAAIvB,iBAAiB,CAACuB,UAAU,CAAC,EAAE;EACjC,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,IAAMrE,UAAU,GAAG/F,aAAa,CAACoK,UAAU,CAAC,CAAA;;EAE5C;EACA,MAAA,IAAIA,UAAU,CAACrL,QAAQ,KAAK,CAAC,EAAE;EAC7B,QAAA,IAAIgH,UAAU,IAAIA,UAAU,CAACkC,OAAO,EAAE;EACpC;EACV;EACA;EACA;EACUmC,UAAAA,UAAU,CAACnC,OAAO,GAChB,CAACmC,UAAU,CAAClC,cAAc,IAAI,CAAC,IAAInC,UAAU,CAACkC,OAAO,GAAG,CAAC,CAAA;EAC7D,SAAC,MAAM;YACLmC,UAAU,CAACnC,OAAO,GAAG,CAAC,CAAA;EACxB,SAAA;EACF,OAAA;;EAEA;EACN;EACA;EACA;EACM,MAAA,IACEmC,UAAU,CAACnC,OAAO,IAAI7D,iBAAiB,IACvC7J,WAAW,CAAC6P,UAAU,CAACnC,OAAO,CAAC,EAC/B;UACApC,YAAY,CAACuE,UAAU,CAAC,CAAA;EAC1B,OAAA;;EAEA;EACA,MAAA,IAAIA,UAAU,CAACjK,OAAO,YAAYjB,gBAAgB,EAAE;EAClDkL,QAAAA,UAAU,CAACjK,OAAO,CAAC8H,OAAO,GAAGmC,UAAU,CAACnC,OAAO,CAAA;EAC/CiC,QAAAA,kBAAkB,CAACE,UAAU,CAACjK,OAAO,CAAC,CAAA;EACxC,OAAA;;EAEA;QACAoJ,mBAAmB,CAACa,UAAU,CAAC,CAAA;EACjC,KAAA;;EAEA;EACA5B,IAAAA,YAAY,CAAC,wBAAwB,EAAE2B,QAAQ,EAAE,IAAI,CAAC,CAAA;KACvD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACE;EACAzL,EAAAA,SAAS,CAAC6L,QAAQ,GAAG,UAAU5D,KAAK,EAAY;EAAA,IAAA,IAAVjC,GAAG,GAAA9J,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAA6D,SAAA,GAAA7D,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EAC5C,IAAA,IAAIwM,IAAI,CAAA;EACR,IAAA,IAAIoD,YAAY,CAAA;EAChB,IAAA,IAAI9B,WAAW,CAAA;EACf,IAAA,IAAI+B,OAAO,CAAA;EACX,IAAA,IAAIC,UAAU,CAAA;EACd;EACJ;EACA;MACI7G,cAAc,GAAG,CAAC8C,KAAK,CAAA;EACvB,IAAA,IAAI9C,cAAc,EAAE;EAClB8C,MAAAA,KAAK,GAAG,OAAO,CAAA;EACjB,KAAA;;EAEA;MACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC4B,OAAO,CAAC5B,KAAK,CAAC,EAAE;EAChD,MAAA,IAAI,OAAOA,KAAK,CAACnN,QAAQ,KAAK,UAAU,EAAE;EACxCmN,QAAAA,KAAK,GAAGA,KAAK,CAACnN,QAAQ,EAAE,CAAA;EACxB,QAAA,IAAI,OAAOmN,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAMvM,eAAe,CAAC,iCAAiC,CAAC,CAAA;EAC1D,SAAA;EACF,OAAC,MAAM;UACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;EACrD,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAI,CAACsE,SAAS,CAACM,WAAW,EAAE;EAC1B,MAAA,IACEpB,OAAA,CAAOJ,MAAM,CAACmN,YAAY,CAAK,KAAA,QAAQ,IACvC,OAAOnN,MAAM,CAACmN,YAAY,KAAK,UAAU,EACzC;EACA,QAAA,IAAI,OAAOhE,KAAK,KAAK,QAAQ,EAAE;EAC7B,UAAA,OAAOnJ,MAAM,CAACmN,YAAY,CAAChE,KAAK,CAAC,CAAA;EACnC,SAAA;EAEA,QAAA,IAAI4B,OAAO,CAAC5B,KAAK,CAAC,EAAE;EAClB,UAAA,OAAOnJ,MAAM,CAACmN,YAAY,CAAChE,KAAK,CAACV,SAAS,CAAC,CAAA;EAC7C,SAAA;EACF,OAAA;EAEA,MAAA,OAAOU,KAAK,CAAA;EACd,KAAA;;EAEA;MACA,IAAI,CAACnE,UAAU,EAAE;QACfiC,YAAY,CAACC,GAAG,CAAC,CAAA;EACnB,KAAA;;EAEA;MACAhG,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;;EAEtB;EACA,IAAA,IAAI,OAAO6H,KAAK,KAAK,QAAQ,EAAE;EAC7B1D,MAAAA,QAAQ,GAAG,KAAK,CAAA;EAClB,KAAA;EAEA,IAAA,IAAIA,QAAQ,EAAE;EACZ;QACA,IAAI0D,KAAK,CAACwB,QAAQ,EAAE;EAClB,QAAA,IAAM1C,OAAO,GAAGrK,iBAAiB,CAACuL,KAAK,CAACwB,QAAQ,CAAC,CAAA;UACjD,IAAI,CAAClH,YAAY,CAACwE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;YAClD,MAAMrL,eAAe,CACnB,yDACF,CAAC,CAAA;EACH,SAAA;EACF,OAAA;EACF,KAAC,MAAM,IAAIuM,KAAK,YAAYvH,IAAI,EAAE;EAChC;EACN;EACMgI,MAAAA,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;QAC/B8D,YAAY,GAAGpD,IAAI,CAAChH,aAAa,CAACQ,UAAU,CAAC+F,KAAK,EAAE,IAAI,CAAC,CAAA;QACzD,IAAI6D,YAAY,CAACzL,QAAQ,KAAK,CAAC,IAAIyL,YAAY,CAACrC,QAAQ,KAAK,MAAM,EAAE;EACnE;EACAf,QAAAA,IAAI,GAAGoD,YAAY,CAAA;EACrB,OAAC,MAAM,IAAIA,YAAY,CAACrC,QAAQ,KAAK,MAAM,EAAE;EAC3Cf,QAAAA,IAAI,GAAGoD,YAAY,CAAA;EACrB,OAAC,MAAM;EACL;EACApD,QAAAA,IAAI,CAACwD,WAAW,CAACJ,YAAY,CAAC,CAAA;EAChC,OAAA;EACF,KAAC,MAAM;EACL;EACA,MAAA,IACE,CAAC9H,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc;EACf;QACAoE,KAAK,CAAC7M,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;UACA,OAAOuG,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAACuI,KAAK,CAAC,GACpCA,KAAK,CAAA;EACX,OAAA;;EAEA;EACAS,MAAAA,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;;EAE3B;QACA,IAAI,CAACS,IAAI,EAAE;UACT,OAAO1E,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGtC,SAAS,GAAG,EAAE,CAAA;EACjE,OAAA;EACF,KAAA;;EAEA;MACA,IAAI8G,IAAI,IAAI3E,UAAU,EAAE;EACtBoD,MAAAA,YAAY,CAACuB,IAAI,CAACyD,UAAU,CAAC,CAAA;EAC/B,KAAA;;EAEA;MACA,IAAMC,YAAY,GAAGrD,eAAe,CAACxE,QAAQ,GAAG0D,KAAK,GAAGS,IAAI,CAAC,CAAA;;EAE7D;EACA,IAAA,OAAQsB,WAAW,GAAGoC,YAAY,CAACR,QAAQ,EAAE,EAAG;EAC9C;QACA,IAAI5B,WAAW,CAAC3J,QAAQ,KAAK,CAAC,IAAI2J,WAAW,KAAK+B,OAAO,EAAE;EACzD,QAAA,SAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAI5B,iBAAiB,CAACH,WAAW,CAAC,EAAE;EAClC,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,IAAM3C,UAAU,GAAG/F,aAAa,CAAC0I,WAAW,CAAC,CAAA;;EAE7C;EACA,MAAA,IAAIA,WAAW,CAAC3J,QAAQ,KAAK,CAAC,EAAE;EAC9B,QAAA,IAAIgH,UAAU,IAAIA,UAAU,CAACkC,OAAO,EAAE;EACpC;EACV;EACA;EACA;EACUS,UAAAA,WAAW,CAACT,OAAO,GACjB,CAACS,WAAW,CAACR,cAAc,IAAI,CAAC,IAAInC,UAAU,CAACkC,OAAO,GAAG,CAAC,CAAA;EAC9D,SAAC,MAAM;YACLS,WAAW,CAACT,OAAO,GAAG,CAAC,CAAA;EACzB,SAAA;EACF,OAAA;;EAEA;EACN;EACA;EACA;EACM,MAAA,IACES,WAAW,CAACT,OAAO,IAAI7D,iBAAiB,IACxC7J,WAAW,CAACmO,WAAW,CAACT,OAAO,CAAC,EAChC;UACApC,YAAY,CAAC6C,WAAW,CAAC,CAAA;EAC3B,OAAA;;EAEA;EACA,MAAA,IAAIA,WAAW,CAACvI,OAAO,YAAYjB,gBAAgB,EAAE;EACnDwJ,QAAAA,WAAW,CAACvI,OAAO,CAAC8H,OAAO,GAAGS,WAAW,CAACT,OAAO,CAAA;EACjDiC,QAAAA,kBAAkB,CAACxB,WAAW,CAACvI,OAAO,CAAC,CAAA;EACzC,OAAA;;EAEA;QACAoJ,mBAAmB,CAACb,WAAW,CAAC,CAAA;EAEhC+B,MAAAA,OAAO,GAAG/B,WAAW,CAAA;EACvB,KAAA;EAEA+B,IAAAA,OAAO,GAAG,IAAI,CAAA;;EAEd;EACA,IAAA,IAAIxH,QAAQ,EAAE;EACZ,MAAA,OAAO0D,KAAK,CAAA;EACd,KAAA;;EAEA;EACA,IAAA,IAAIjE,UAAU,EAAE;EACd,MAAA,IAAIC,mBAAmB,EAAE;UACvB+H,UAAU,GAAGhK,sBAAsB,CAAC8G,IAAI,CAACJ,IAAI,CAAChH,aAAa,CAAC,CAAA;UAE5D,OAAOgH,IAAI,CAACyD,UAAU,EAAE;EACtB;EACAH,UAAAA,UAAU,CAACE,WAAW,CAACxD,IAAI,CAACyD,UAAU,CAAC,CAAA;EACzC,SAAA;EACF,OAAC,MAAM;EACLH,QAAAA,UAAU,GAAGtD,IAAI,CAAA;EACnB,OAAA;EAEA,MAAA,IAAI/F,YAAY,CAAC0J,UAAU,IAAI1J,YAAY,CAAC2J,aAAa,EAAE;EACzD;EACR;EACA;EACA;EACA;EACA;EACA;UACQN,UAAU,GAAG9J,UAAU,CAAC4G,IAAI,CAACvI,gBAAgB,EAAEyL,UAAU,EAAE,IAAI,CAAC,CAAA;EAClE,OAAA;EAEA,MAAA,OAAOA,UAAU,CAAA;EACnB,KAAA;MAEA,IAAIO,cAAc,GAAG1I,cAAc,GAAG6E,IAAI,CAACnB,SAAS,GAAGmB,IAAI,CAACD,SAAS,CAAA;;EAErE;EACA,IAAA,IACE5E,cAAc,IACdtB,YAAY,CAAC,UAAU,CAAC,IACxBmG,IAAI,CAAChH,aAAa,IAClBgH,IAAI,CAAChH,aAAa,CAAC8K,OAAO,IAC1B9D,IAAI,CAAChH,aAAa,CAAC8K,OAAO,CAAC9E,IAAI,IAC/BnM,UAAU,CAAC+G,YAAwB,EAAEoG,IAAI,CAAChH,aAAa,CAAC8K,OAAO,CAAC9E,IAAI,CAAC,EACrE;EACA6E,MAAAA,cAAc,GACZ,YAAY,GAAG7D,IAAI,CAAChH,aAAa,CAAC8K,OAAO,CAAC9E,IAAI,GAAG,KAAK,GAAG6E,cAAc,CAAA;EAC3E,KAAA;;EAEA;EACA,IAAA,IAAI5I,kBAAkB,EAAE;QACtB4I,cAAc,GAAGtR,aAAa,CAACsR,cAAc,EAAEpO,eAAa,EAAE,GAAG,CAAC,CAAA;QAClEoO,cAAc,GAAGtR,aAAa,CAACsR,cAAc,EAAEnO,UAAQ,EAAE,GAAG,CAAC,CAAA;QAC7DmO,cAAc,GAAGtR,aAAa,CAACsR,cAAc,EAAElO,aAAW,EAAE,GAAG,CAAC,CAAA;EAClE,KAAA;MAEA,OAAOsD,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAAC6M,cAAc,CAAC,GAC7CA,cAAc,CAAA;KACnB,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACEvM,EAAAA,SAAS,CAACyM,SAAS,GAAG,UAAUzG,GAAG,EAAE;MACnCD,YAAY,CAACC,GAAG,CAAC,CAAA;EACjBlC,IAAAA,UAAU,GAAG,IAAI,CAAA;KAClB,CAAA;;EAED;EACF;EACA;EACA;EACA;IACE9D,SAAS,CAAC0M,WAAW,GAAG,YAAY;EAClCjH,IAAAA,MAAM,GAAG,IAAI,CAAA;EACb3B,IAAAA,UAAU,GAAG,KAAK,CAAA;KACnB,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACE9D,SAAS,CAAC2M,gBAAgB,GAAG,UAAUC,GAAG,EAAE9B,IAAI,EAAEvN,KAAK,EAAE;EACvD;MACA,IAAI,CAACkI,MAAM,EAAE;QACXM,YAAY,CAAC,EAAE,CAAC,CAAA;EAClB,KAAA;EAEA,IAAA,IAAM4E,KAAK,GAAGjO,iBAAiB,CAACkQ,GAAG,CAAC,CAAA;EACpC,IAAA,IAAMhC,MAAM,GAAGlO,iBAAiB,CAACoO,IAAI,CAAC,CAAA;EACtC,IAAA,OAAOJ,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAErN,KAAK,CAAC,CAAA;KAC/C,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACEyC,EAAAA,SAAS,CAAC6M,OAAO,GAAG,UAAU9C,UAAU,EAAE+C,YAAY,EAAE;EACtD,IAAA,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;EACtC,MAAA,OAAA;EACF,KAAA;MAEA1K,KAAK,CAAC2H,UAAU,CAAC,GAAG3H,KAAK,CAAC2H,UAAU,CAAC,IAAI,EAAE,CAAA;EAC3CvP,IAAAA,SAAS,CAAC4H,KAAK,CAAC2H,UAAU,CAAC,EAAE+C,YAAY,CAAC,CAAA;KAC3C,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACE9M,EAAAA,SAAS,CAAC+M,UAAU,GAAG,UAAUhD,UAAU,EAAE;EAC3C,IAAA,IAAI3H,KAAK,CAAC2H,UAAU,CAAC,EAAE;EACrB,MAAA,OAAOzP,QAAQ,CAAC8H,KAAK,CAAC2H,UAAU,CAAC,CAAC,CAAA;EACpC,KAAA;KACD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACE/J,EAAAA,SAAS,CAACgN,WAAW,GAAG,UAAUjD,UAAU,EAAE;EAC5C,IAAA,IAAI3H,KAAK,CAAC2H,UAAU,CAAC,EAAE;EACrB3H,MAAAA,KAAK,CAAC2H,UAAU,CAAC,GAAG,EAAE,CAAA;EACxB,KAAA;KACD,CAAA;;EAED;EACF;EACA;EACA;EACA;IACE/J,SAAS,CAACiN,cAAc,GAAG,YAAY;MACrC7K,KAAK,GAAG,EAAE,CAAA;KACX,CAAA;EAED,EAAA,OAAOpC,SAAS,CAAA;EAClB,CAAA;AAEA,eAAeF,eAAe,EAAE;;;;;;;;"}
package common;

import java.io.IOException;
import java.lang.reflect.Method;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class BaseServlet extends HttpServlet {


	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		service(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		service(req, resp);
	}

	@Override
	@SuppressWarnings({"rawtypes", "unchecked"})
	protected void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {

		req.setCharacterEncoding("UTF-8");

		resp.setCharacterEncoding("UTF-8");

		resp.setHeader("Content-Type", "text/html; charset=UTF-8");

		//跨域请求，*代表允许全部类型
		resp.setHeader("Access-Control-Allow-Origin", "*");
		//允许请求方式
		resp.setHeader("Access-Control-Allow-Methods", "POST, GET, DELETE");
		//用来指定本次预检请求的有效期，单位为秒，在此期间不用发出另一条预检请求
		resp.setHeader("Access-Control-Max-Age", "3600");
		//请求包含的字段内容，如有多个可用哪个逗号分隔如下
		resp.setHeader("Access-Control-Allow-Headers", "content-type,x-requested-with,Authorization, x-ui-request,lang");
		//访问控制允许凭据，true为允许
		resp.setHeader("Access-Control-Allow-Credentials", "true");

		String name = req.getParameter("act");
		if (name == null || name.isEmpty()) {
			throw new RuntimeException("act方法参数不存在");
		}
		Class c = this.getClass();
		Method method = null;
		try {
			method = c.getMethod(name, HttpServletRequest.class, HttpServletResponse.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			method.invoke(this, req, resp);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
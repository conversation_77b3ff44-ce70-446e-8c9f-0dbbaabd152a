!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).el=e()}}(function(){return function t(f,u,l){function d(n,e){if(!u[n]){if(!f[n]){var o="function"==typeof require&&require;if(!e&&o)return o(n,!0);if(s)return s(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var i=u[n]={exports:{}};f[n][0].call(i.exports,function(e){return d(f[n][1][e]||e)},i,i.exports,t,f,u,l)}return u[n].exports}for(var s="function"==typeof require&&require,e=0;e<l.length;e++)d(l[e]);return d}({1:[function(e,n,o){"use strict";n.exports={languageTag:"el",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"χ",million:"ε",billion:"δ",trillion:"τ"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=el.min.js.map

var treeId = 'dpTree';
var ztreeObj;
var curDragNodes, autoExpandNode;
var treeSetting = {
    view: {
        dblClickExpand: false,
        showLine: true,
        fontCss: {
            'color': 'black'
        },
        selectedMulti: false,
        txtSelectedEnable: true,
        showTitle: true
    },
    async: {
        enable: true,
        url: getReqUrl("tree", "QueryTreeNodeByPid"),
        type: "post",
        autoParam: ["ID"],
        contentType: "application/json;charset=utf-8",
        dataType: 'json',
        dataFilter: function (treeId, parentNode, responseData) {
            if (responseData.success) {
                var datas = responseData.data;
                if (datas.length > 0) {
                    datas = dealData(datas);
                }
                return datas;
            } else {
                layer.alert(responseData.msg, {
                    icon: 2
                });
            }
        }
    },
    check: {
        enable: false
    },
    edit: {
        enable: true,
        editNameSelectAll: false,
        showRemoveBtn: false,
        showRenameBtn: false,
        drag: {
            autoExpandTrigger: true,
            prev: dropPrev,
            inner: dropInner,
            next: dropPrev
        }
    },
    data: {
        simpleData: {
            enable: true,
            idKey: "ID",
            pIdKey: "PID",
            rootPId: -1
        },
        key: {
            name: 'TEXT',
            title: 'TEXT',
            isParent: "ISPARENT"
        }
    },
    callback: {
        beforeDrag: beforeDrag,
        beforeDrop: beforeDrop,
        beforeDragOpen: beforeDragOpen,
        onDrag: onDrag,
        onDrop: onDrop
    }
};

function dropPrev(treeId, nodes, targetNode) {
    var pNode = targetNode.getParentNode();
    if (pNode && pNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            var curPNode = curDragNodes[i].getParentNode();
            if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function dropInner(treeId, nodes, targetNode) {
    if (targetNode && targetNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            if (!targetNode && curDragNodes[i].dropRoot === false) {
                return false;
            } else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
                .childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function beforeDrag(treeId, treeNodes) {
    for (var i = 0, l = treeNodes.length; i < l; i++) {
        if (treeNodes[i].drag === false) {
            curDragNodes = null;
            return false;
        } else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
            curDragNodes = null;
            return false;
        }
    }
    curDragNodes = treeNodes;
    return true;
}

function beforeDragOpen(treeId, treeNode) {
    autoExpandNode = treeNode;
    return true;
}

function beforeDrop() {
    return true;
}

function onDrag(event, treeId, treeNodes) {
}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
    if (targetNode != null) {
        var sourceNodeSort = treeNodes[0]['SORT'];
        var sourceNodePId = treeNodes[0]['PID'];
        var sourceNodeId = treeNodes[0]['ID'];
        var sourceNodeName = treeNodes[0]['NAME'];

        var targetNodeSort = targetNode['SORT'];
        var targetNodeId = targetNode['ID'];
        var targetNodeName = targetNode['NAME'];
        var type = "上面";
        if (sourceNodeSort < targetNodeSort) {
            type = '下面';
        }
        var parentNode = treeNodes[0].getParentNode();
        var allNode = parentNode.children;
        var arr = [];
        for (var i = 1; i <= allNode.length; i++) {
            arr.push(allNode[i - 1]['ID'] + ":" + i);
        }
        var str = arr.join(",");
        postAjax('tree', 'UpdateNodeSort', {
            str: str
        }, true, function () {
            reloadTree(sourceNodePId, sourceNodeId);
        });
    }
}

/**
 * 对移动节点的树的数据进行处理
 * @param {} datas
 * @param parentNode 需要转移的节点的父节点
 */
function dealMoveTreeData(datas, parentNode) {
    var datas = dealData(datas);
    var parentType = parentNode['TYPE'];
    var parentLevel = parentNode['LEVEL_NUM'];
    var parentId = parentNode['ID'];
    for (var i = 0; i < datas.length; i++) {
        var dLevel = datas[i]['LEVEL_NUM'];
        var dType = datas[i]['TYPE'];
        if (parentLevel <= dLevel) {
            datas[i]['ISPARENT'] = false;
        }
        if (dType == parentType) {
            datas[i]['nocheck'] = false;
            if (datas[i]['ID'] == parentId) {
                datas[i]['checked'] = true;
            } else {
                datas[i]['checked'] = false;
            }
        } else {
            datas[i]['nocheck'] = true;
        }
    }
    return datas;
}

/**
 * 对数据进行处理
 * @param {} datas
 */
function dealData(datas) {
    for (var i = 0; i < datas.length; i++) {
        var dType = datas[i]['TYPE'];
        var tableNum = datas[i]['TABLE_NUM'] || "";
        datas[i]['TEXT'] = datas[i]['NAME'];
        if (dType === 'root') {
            datas[i].icon = imgPath + "root.png";
            datas[i].drag = false;
            datas[i].childOuter = false;
        } else if (dType === 'folder') {
            datas[i]['TEXT'] = dealPrefix(datas[i]['SORT']) + "-" + datas[i]['NAME'];
            datas[i].icon = imgPath + "folder.png";
            datas[i].dropInner = false;
            datas[i].childOuter = false;
        } else if (dType === 'model') {
            datas[i].icon = imgPath + "卫星.png";
            datas[i].drag = false;
            datas[i].childOuter = false;
        } else if (dType === 'project') {
            datas[i]['TEXT'] = dealPrefix(datas[i]['SORT']) + "-" + datas[i]['NAME'];
            datas[i].icon = imgPath + "dir.png";
            datas[i].childOuter = false;
        } else if (dType === 'a') {
            datas[i]['TEXT'] = tableNum + "：" + datas[i]['NAME'];
            datas[i].icon = imgPath + "A.png";
            datas[i].childOuter = false;
        } else if (dType === 'b') {
            datas[i]['TEXT'] = tableNum + "：" + datas[i]['NAME'];
            datas[i].icon = imgPath + "B.png";
            datas[i].dropInner = false;
        }
    }
    return datas;
}

function dealPrefix(sort, num) {
    if (!num) {
        num = 2;
    }
    var sortStr = sort + "";
    if (sortStr.length < num) {
        var temp = "";
        for (var i = 0; i < (num - sortStr.length); i++) {
            temp += "0";
        }
        sortStr = temp + sort;
    }
    return sortStr;
}

/**
 * 操作完节点之后重新加载节点
 * @param refreshId 需要刷新的节点
 * @param selId 刷新之后需要选中的节点
 */
function reloadTree(refreshId, selId) {
    if (selId) {

    } else {
        selId = refreshId;
    }
    var refreshTreeNode = ztreeObj.getNodeByParam("ID", refreshId, null);
    if (!refreshTreeNode.ISPARENT) {
        refreshTreeNode.ISPARENT = true;
        ztreeObj.updateNode(refreshTreeNode);
    }
    ztreeObj.reAsyncChildNodes(refreshTreeNode, 'refresh', false, function () {
        ztreeObj.expandNode(refreshTreeNode, true, false, true);
        var newSelNode = ztreeObj.getNodeByParam("ID", selId, null);
        ztreeObj.selectNode(newSelNode, false, true);
        loadTreeMenu();
        reloadTable(newSelNode);
    });
}

/**
 * js加载树
 */
function loadTree() {
    var cb_success = function (datas) {
        datas = dealData(datas);
        treeSetting.callback.onExpand = function (event, treeId, treeNode) {
            loadTreeMenu();
        };

        treeSetting.callback.onClick = function (event, treeId, treeNode) {
            reloadTable(treeNode);
        };
        ztreeObj = $.fn.zTree.init($("#" + treeId), treeSetting, datas);
        var node = ztreeObj.getNodeByParam("LEVEL_NUM", 0, null);
        ztreeObj.expandNode(node, true, false, true);
        loadTreeMenu();
    };
    //使用ajax进行异步加载Tree
    postAjax("tree", 'QueryTreeRoot', {}, false, cb_success);
}

/**
 * 加载树节点右键菜单
 */
function loadTreeMenu() {
    $("#dpTree a").each(function (i, n) {
        var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
        var menus = getNodeMenu(node);
        if (menus.length != 0) {
            $(n).contextMenu({
                width: 140,
                menu: menus,
                target: function (ele) {
                    var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
                    ztreeObj.selectNode(node, false, true);
                    if (HotUtil.currentTreeNodeId !== node['ID']) {
                        reloadTable(node);
                    }
                }
            });
        }
    });
}
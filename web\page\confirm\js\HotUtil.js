var HotUtil = {
        /**
         * 存储td节点的图片信息
         */
        tdImgs: {},
        /**
         * 当前节点的工作类型
         */
        workType: '',
        workTypes: [],
        postTypes: [],
        currentTreeNodeId: '',
        /**
         * 判断当前节点是否是pdf节点
         */
        isPdf: function (treeNode) {
            var isPdf = false;
            if ((treeNode['TYPE'] == 'b' || treeNode['TYPE'].indexOf('table') > -1) && (treeNode['FILE_FORMAT'] == 'pdf' || treeNode['FILE_FORMAT'] == 'PDF')) {
                isPdf = true;
            }
            return isPdf;
        },
        /** 使表格进入编辑状态
         * @param {Object} treeNode
         */
        openEdit: function (treeNode, isUpdate, successFn) {
            postAjax("table", 'OpenEdit', {
                id: treeNode['ID'],
                isUpdate: isUpdate,
                user: sessionStorage.getItem("fullname") + '[' + sessionStorage.getItem('username') + ']',
            }, false, function () {
                successFn();
            });
        },
        /** 使表格关闭编辑状态
         * @param {Object} treeNode
         */
        closeEdit: function (treeNode, successFn) {
            postAjax('table', 'CloseEdit', {
                id: treeNode['ID']
            }, false, function () {
                successFn();
            });
        },
        /**
         * 遍历选中的每一个单元格
         * @param {Object} thisHot
         * @param {Object} eachFn
         */
        eachSelectedRange: function (thisHot, eachFn) {
            var srs = HotUtil.getSelectedRange();
            HotUtil.eachArrays(srs, thisHot, eachFn);
        },
        getSelectedRange: function () {
            var selectedRange = window.hot.getSelectedRange();
            if (selectedRange) {
                for (var i = 0; i < selectedRange.length; i++) {
                    var selectedRangeElement = selectedRange[i];
                    var from = selectedRangeElement.from;
                    if (from.row == -1) {
                        from.row = 0;
                    }
                    if (from.col == -1) {
                        from.col = 0;
                    }

                    var to = selectedRangeElement.to;
                    if (to.row == -1) {
                        to.row = 0;
                    }
                    if (to.col == -1) {
                        to.col = 0;
                    }
                }
                return selectedRange;
            } else {
                return [];
            }
        },
        eachArrays: function (arrays, thisHot, eachFn) {
            for (var i = 0; i < arrays.length; i++) {
                var sr = arrays[i];
                for (var r = sr.from.row; r <= sr.to.row; r++) {
                    for (var c = sr.from.col; c <= sr.to.col; c++) {
                        if (r > -1 && c > -1) {
                            if (thisHot.getCellMeta(r, c).hidden) {
                                continue;
                            }
                            var isBreak = eachFn(r, c);
                            if (isBreak) {
                                break;
                            }
                        }
                    }
                }
            }
        },
        /**
         * 检查选中的单元格中全是锁定状态
         * @param {Object} thisHot
         */
        allReadOnly: function (thisHot) {
            var allReadOnly = true;
            HotUtil.eachSelectedRange(thisHot, function (r, c) {
                if (!thisHot.getCellMeta(r, c).readOnly) {
                    allReadOnly = false;
                    return true;
                } else {
                    return false;
                }
            });
            return allReadOnly;
        },
        /**
         * 检查选中的单元格中是至少有一个单元格是锁定状态
         * @param {Object} thisHot
         */
        atLeastOneReadOnly: function (thisHot) {
            var atLeastOneReadOnly = false;
            if (HotUtil.getSelectedRange().length === 0) {
                atLeastOneReadOnly = true;
            } else {
                HotUtil.eachSelectedRange(thisHot, function (r, c) {
                    if (thisHot.getCellMeta(r, c).readOnly) {
                        atLeastOneReadOnly = true;
                        return true;
                    } else {
                        return false;
                    }
                });
            }

            return atLeastOneReadOnly;
        },
        /**
         * 禁用列相关的右键操作
         * @param {Object} thisHot
         */
        disableColContextMenu: function (thisHot) {
            //选中的单元格是否存在锁定的
            var hasLock = HotUtil.atLeastOneReadOnly(thisHot);
            if (!hasLock) {
                var selectedRanges = HotUtil.getSelectedRange();
                if (selectedRanges.length > 0) {
                    //检测选中单元格同列是否存在锁定的
                    var selectedRange = HotUtil.getSelectedRange()[0];
                    for (var row = 0; row < hot.countRows(); row++) {
                        for (var col = selectedRange.from.col; col <= selectedRange.to.col; col++) {
                            if (col > -1 && row > -1) {
                                if (thisHot.getCellMeta(row, col).readOnly) {
                                    hasLock = true;
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    hasLock = true;
                }
            }
            return hasLock;
        },
        getSelecteds:

            function () {
                var selected = hot.getSelected() || [];
                var arr = [];
                for (var index = 0; index < selected.length; index += 1) {
                    var [row1, column1, row2, column2] = selected[index];
                    var startRow = Math.max(Math.min(row1, row2), 0);
                    var endRow = Math.max(row1, row2);
                    var startCol = Math.max(Math.min(column1, column2), 0);
                    var endCol = Math.max(column1, column2);

                    for (var rowIndex = startRow; rowIndex <= endRow; rowIndex += 1) {
                        for (var columnIndex = startCol; columnIndex <=
                        endCol; columnIndex += 1) {
                            var obj = {
                                row: rowIndex,
                                col: columnIndex
                            };
                            arr.push(obj);
                        }
                    }
                }
                return arr;
            }

        ,
        /**
         * 设置更为类型
         * @param name
         * @param value
         */
        setPostType: function (postType) {
            var selected = HotUtil.getSelecteds();
            for (var i = 0; i < selected.length; i++) {
                var obj = selected[i];
                hot.setCellMeta(obj.row, obj.col, 'postType', postType);
            }
            hot.render();
        }
        ,
        dealClass: function (className, type) {
            var selected = HotUtil.getSelecteds();
            for (var i = 0; i < selected.length; i++) {
                var obj = selected[i];
                var meta = hot.getCellMeta(obj.row, obj.col);
                var oldClass = "";
                if (meta.className) {
                    oldClass = meta.className;
                }
                var classArr = oldClass.split(" ");
                var newClassArr = [];
                for (var j = 0; j < classArr.length; j++) {
                    if (className.indexOf('font-size') > -1) {
                        if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf('font-size') === -1) {
                            newClassArr.push(classArr[j]);
                        }
                    } else if (className.indexOf('font-color') > -1) {
                        if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf('font-color') === -1) {
                            newClassArr.push(classArr[j]);
                        }
                    } else {
                        if (classArr[j] !== className && classArr[j] !== " ") {
                            newClassArr.push(classArr[j]);
                        }
                    }

                }
                if (type === 'add') {
                    if (className !== 'font-color-black') {
                        newClassArr.push(className);
                    }
                }
                hot.setCellMeta(obj.row, obj.col, 'className', newClassArr.join(" "));
            }
            hot.render();
        }
        ,
        /**
         * 获取全部的字体大小
         * @returns {*[]}
         */
        fontSizeItems: function () {
            var items = [];
            for (var i = 12; i <= 30; i++) {
                var obj = {};
                var name = i + "";
                obj.key = 'fontSize:' + name;
                obj.name = name;
                obj.callback = function (key, selection, clickEvent) {
                    HotUtil.dealClass("font-size-" + key.split(":")[1], "add");
                }
                items.push(obj);
            }
            return items;
        }
        ,
        /**
         * 获取全部的签名岗位信息
         */
        postTypeItems: function () {
            var items = [];
            for (var i = 0; i < HotUtil.postTypes.length; i++) {
                var obj = {};
                obj.key = 'postType:' + HotUtil.postTypes[i];
                obj.name = HotUtil.postTypes[i];
                obj.callback = function (key, selection, clickEvent) {
                    HotUtil.setPostType(key.split(":")[1]);
                }
                items.push(obj);
            }
            return items;
        },

        /**
         * 获取全部的字体颜色
         * @returns {*[]}
         */
        fontColorItems: function () {
            var items = [];
            var colors = [{
                color: 'purple',
                name: '紫色'
            }, {
                color: 'green',
                name: '绿色'
            }, {
                color: 'orange',
                name: '橙色'
            }, {
                color: 'deeppink',
                name: '粉色'
            }, {
                color: 'black',
                name: '黑色'
            }];
            for (var i = 0; i < colors.length; i++) {
                var obj = {};
                obj.key = 'fontColor:' + colors[i].color;
                obj.name = '<span class="font-color-' + colors[i].color + '">' + colors[i].name + '</span>';
                obj.callback = function (key, selection, clickEvent) {
                    HotUtil.dealClass("font-color-" + key.split(":")[1], "add");
                }
                items.push(obj);
            }
            return items;
        }
        ,
        /**
         * 获取右键菜单
         */
        getContextItems: function (treeNode) {
            var obj = {
                row_above: {
                    name: '上方插入行'
                },
                row_below: {
                    name: '下方插入行'
                },
                sp1: '---------',
                col_left: {
                    name: '左方插入列',
                    disabled() {
                        return HotUtil.disableColContextMenu(this);
                    }
                },
                col_right: {
                    name: '右方插入列',
                    disabled() {
                        return HotUtil.disableColContextMenu(this);
                    }
                },
                sp2: '---------',
                remove_row: {
                    name: '移除该行',
                    disabled() {
                        //选中的单元格是否存在锁定的
                        var hasLock = HotUtil.atLeastOneReadOnly(this);
                        if (!hasLock) {
                            var selectedRanges = HotUtil.getSelectedRange();
                            if (selectedRanges.length > 0) {
                                //检测选中单元格同行是否存在锁定的
                                var selectedRange = HotUtil.getSelectedRange()[0];
                                for (var row = selectedRange.from.row; row <= selectedRange.to.row; row++) {
                                    for (var col = 0; col < hot.countCols(); col++) {
                                        if (col > -1 && row > -1) {
                                            if (this.getCellMeta(row, col).readOnly) {
                                                hasLock = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                hasLock = true;
                            }
                        }
                        return hasLock;
                    }
                },
                remove_col: {
                    name: '移除该列',
                    disabled() {
                        return HotUtil.disableColContextMenu(this);
                    }
                },
                sp3: '---------',
                alignment: {
                    name: '对齐',
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                fontSize: {
                    name() {
                        return '字体大小';
                    },
                    submenu: {
                        items: HotUtil.fontSizeItems()
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                fontColor: {
                    name() {
                        return '字体颜色';
                    },
                    submenu: {
                        items: HotUtil.fontColorItems()
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                mergeCells: {
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                bold: {
                    name() {
                        return '加粗';
                    },
                    callback(key, selection, clickEvent) {
                        HotUtil.dealClass("c-bold", "add");
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                cancelBold: {
                    name() {
                        return '取消加粗';
                    },
                    callback(key, selection, clickEvent) {
                        HotUtil.dealClass("c-bold", "cancel");
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                commentsAddEdit: {
                    name() {
                        var _this$getSelectedRang;
                        const highlight = (_this$getSelectedRang = this.getSelectedRangeLast()) === null || _this$getSelectedRang === void 0 ? void 0 : _this$getSelectedRang.highlight;
                        if (highlight !== null && highlight !== void 0 && highlight.isCell() && this.getPlugin("Comments").getCommentAtCell(highlight.row, highlight.col)) {
                            return "编辑批注";
                        }
                        return "添加批注";
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                commentsRemove: {
                    name() {
                        return "删除批注";
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                make_read_only: {
                    name() {
                        var label = "锁定";
                        if (HotUtil.atLeastOneReadOnly(this)) {
                            label = "解除锁定";
                        }
                        return label;
                    },
                    callback(key, selection, clickEvent) {
                        var _this = this;
                        var atLeastOneReadOnly = HotUtil.atLeastOneReadOnly(_this);

                        var label = "锁定";
                        if (atLeastOneReadOnly) {
                            label = "解除锁定";
                        }
                        var log = {};
                        log.operation = label + "单元格";
                        log.tablePid = treeNode['PID'];
                        log.tableId = treeNode['ID'];
                        var locations = "";
                        var tdCount = 0;
                        HotUtil.eachSelectedRange(_this, function (r, c) {
                            locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                            tdCount++;
                            _this.setCellMeta(r, c, 'readOnly', !atLeastOneReadOnly);
                            if (atLeastOneReadOnly) {
                                //解除锁定需要清除签名
                                var meta = _this.getCellMeta(r, c);
                                var eles = meta.eles || [];
                                var newEles = [];
                                for (var e = 0; e < eles.length; e++) {
                                    if (eles[e].type !== 'sign') {
                                        newEles.push(eles[e]);
                                    }
                                }
                                _this.setCellMeta(r, c, 'eles', newEles);
                            }
                        });
                        locations = locations.substring(0, locations.length - 1);
                        log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中" + label + "了" + tdCount + "个单元格，分别是" + locations;
                        log.reqResult = 1;
                        addConfirmLog(log);
                        _this.render();
                    },
                    disabled() {
                        if (isAdmin) {
                            return false;
                        } else {
                            return HotUtil.atLeastOneReadOnly(this);
                        }
                    }
                },
                setSignTd: {
                    name() {
                        return '上传签章';
                    },
                    callback(key, selection, clickEvent) {
                        var log = {};
                        log.operation = "上传签章";
                        log.tablePid = treeNode.PID;
                        log.tableId = treeNode.ID;
                        var content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
                        var locations = "";
                        var _this = this;
                        var srs = HotUtil.getSelectedRange();
                        HotUtil.uploadSignLayer(function (src, date, signName) {
                            HotUtil.eachArrays(srs, _this, function (r, c) {
                                locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                                var eles = _this.getCellMeta(r, c).eles || [];
                                eles.push({
                                    type: "sign",
                                    date: date,
                                    signName: signName,
                                    src: src
                                });
                                _this.setCellMeta(r, c, "eles", eles);
                                //添加到签名数据库中
                                postAjax('table', "AddOneSign", {
                                    data: {
                                        img: src,
                                        creator: sessionStorage.getItem("username"),
                                        id: treeNode['ID']
                                    }
                                }, false);
                            });
                            locations = locations.substring(0, locations.length - 1);
                            content = content + locations;
                            content += "上传了签章，签章日期为：" + date + "，签章路径为：" + src;
                            log.content = content;
                            log.reqResult = 1;
                            addConfirmLog(log);
                            _this.render();
                        });
                    },
                    disabled() {
                        if (HotUtil.getSelectedRange().length === 0) {
                            return true;
                        } else {
                            return !HotUtil.allReadOnly(this);
                        }
                    }
                },
                setImgTd: {
                    name() {
                        return '上传图片';
                    },
                    callback(key, selection, clickEvent) {
                        var _this = this;
                        var srs = HotUtil.getSelectedRange();
                        var log = {};
                        log.operation = "上传图片";
                        log.tablePid = treeNode.PID;
                        log.tableId = treeNode.ID;
                        log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
                        var locations = "";
                        HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
                            HotUtil.eachArrays(srs, _this, function (r, c) {
                                locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                                var eles = _this.getCellMeta(r, c).eles || [];
                                for (var i = 0; i < datas.length; i++) {
                                    var rowData = datas[i];
                                    if (rowData.success) {
                                        var params = {};
                                        params.id = rowData.index;
                                        params.photoPath = rowData.photoPath;
                                        params.photoFormat = rowData.photoFormat;
                                        params.photoName = rowData.photoName;
                                        params.photoShowNum = rowData.photoShowNum;
                                        params.type = "photo";
                                        params.src = "/File" + rowData.photoPath;
                                        params.class = "sign-img photo";
                                        params.date = layui.util.toDateString(new Date(), 'yyyy-MM-dd');
                                        eles.push(params);
                                    }
                                }
                                _this.setCellMeta(r, c, "eles", eles);
                            });
                            locations = locations.substring(0, locations.length - 1);
                            var photoText = "[";
                            var photoCount = 0;
                            for (var i = 0; i < datas.length; i++) {
                                var rowData = datas[i];
                                if (rowData.success) {
                                    photoText += "{照片名称：" + (rowData.photoName + "." + rowData.photoFormat) +
                                        "，路径：" + rowData.photoPath + "，大小：" + rowData.photoSize +
                                        "，编号：" + rowData.photoShowNum + "},";
                                    //添加到图片数据库中
                                    postAjax("table", "AddOnePhoto", {
                                        data: {
                                            id: treeNode['ID'],
                                            photoName: rowData.photoName,
                                            photoPath: rowData.photoPath,
                                            photoFormat: rowData.photoFormat,
                                            photoNumber: rowData.photoNumber,
                                            photoSize: rowData.photoSize,
                                            creator: sessionStorage.getItem("username")
                                        }
                                    }, false);
                                    photoCount++;
                                }
                            }
                            photoText = photoText.substring(0, photoText.length - 1);
                            photoText += "]";
                            log.content = log.content + locations + "上传了" + photoCount + "张图片,分别是" + photoText;
                            log.reqResult = 1;
                            addConfirmLog(log);
                            _this.render();
                            layer.close(layerIndex);
                        });
                    },
                    disabled() {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                },
                viewImgTd: {
                    name() {
                        return '查看图片';
                    },
                    callback(key, selection, clickEvent) {
                        var _this = this;
                        var row = selection[0].start.row;
                        var col = selection[0].start.col;
                        var cellMeta = this.getCellMeta(row, col);
                        var eles = cellMeta.eles;
                        var readOnly = cellMeta.readOnly || false;
                        var imgs = [];
                        for (var i = 0; i < eles.length; i++) {
                            if (eles[i]['type'] == 'photo') {
                                imgs.push(eles[i]);
                            }
                        }

                        var delImgs = [];
                        HotUtil.viewImageLayer(imgs, !readOnly, function () {
                            if ($(".picView-magnify-list").data("delete")) {
                                var newEles = [];
                                for (var i = 0; i < eles.length; i++) {
                                    if (eles[i].type == 'photo') {
                                        var isDel = false;
                                        for (var j = 0; j < delImgs.length; j++) {
                                            if (eles[i].id == delImgs[j]) {
                                                isDel = true;
                                                break;
                                            }
                                        }
                                        if (!isDel) {
                                            newEles.push(eles[i]);
                                        }
                                    } else {
                                        newEles.push(eles[i]);
                                    }
                                }
                                _this.setCellMeta(row, col, 'eles', newEles);
                                _this.render();
                            }
                        }, function ($i, $ul) {
                            var photoId = $i.attr("photo-id");
                            $i.parent().parent().remove();
                            $ul.data("delete", true);
                            delImgs.push(photoId);
                        });

                    },
                    disabled() {
                        var disable = true;
                        var selectedRange = HotUtil.getSelectedRange();
                        if (selectedRange.length == 1) {
                            var from = selectedRange[0].from;
                            var to = selectedRange[0].to;
                            var cellMeta = this.getCellMeta(from.row, from.col);
                            var colspan = cellMeta['colspan'] || 1;
                            var rowspan = cellMeta['rowspan'] || 1;
                            var isSingleCell = false;
                            if (from.row == to.row && from.col == to.col) {
                                isSingleCell = true;
                            } else {
                                if ((from.row + rowspan - 1) == to.row && (from.col + colspan - 1) == to.col) {
                                    isSingleCell = true;
                                }
                            }

                            if (isSingleCell) {
                                if (cellMeta.eles && cellMeta.eles.length > 0) {
                                    var eles = cellMeta.eles;
                                    for (var i = 0; i < eles.length; i++) {
                                        if (eles[i]['type'] == 'photo') {
                                            disable = false;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        return disable;
                    }
                }
            };
            if (HotUtil.workType !== '') {
                obj['postType'] = {
                    name() {
                        return '设置签名岗位';
                    },
                    submenu: {
                        items: HotUtil.postTypeItems()
                    },
                    disabled() {
                        if (HotUtil.getSelectedRange().length === 0) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
            }
            return obj;
        },
        /**
         * 自动填充自动增加序列
         * @param {Object} str
         */
        incrementString: function (str, type) {
            if (str == "" || str == null || str == " ") {
                return "";
            }
            var regex = /(\d+)(?!.*\d)/;
            var match = regex.exec(str);
            if (match) {
                var num = parseInt(match[1]);
                var incremented = (num + 1).toString();
                if (type == 'subtract') {
                    incremented = (num - 1).toString();
                }
                return str.slice(0, match.index) + incremented.padStart(match[1].length, '0') + str.slice(match.index + match[1].length);
            } else {
                return str;
            }
        }
        ,
        /**
         * 编辑表格初始化表
         * @param saveData
         * @param treeNode
         * @param tableHeader
         * @param afterChange
         */
        renderHot: function (saveData, treeNode, tableHeader, afterChange) {
            var fixedRowsTop = tableHeader;
            var initData = [], merged = [], metas = [], colWidths = [];
            var initColNum = 12, initRowNum = 15;

            for (var r = 0; r < initRowNum; r++) {
                var rows = [];
                for (var c = 0; c < initColNum; c++) {
                    rows.push(null);
                }
                initData.push(rows);
            }

            if (saveData) {
                saveData = JSON.parse(saveData);
                if (saveData['tableData']) {
                    initData = saveData['tableData'];
                }
                HotUtil.workType = saveData['workType'] || '';
                merged = saveData['merged'] || [];
                colWidths = saveData.colWidths || void 0;
                metas = saveData.meta || [];
            }
            var container = document.getElementById('handsontable');
            window.hot = new Handsontable(container, {
                data: initData,
                fixedRowsTop: fixedRowsTop,
                mergeCells: merged,
                rowHeaders: true,
                colHeaders: true,
                rowHeights: 40,
                dropdownMenu: false,
                customBorders: true,
                comments: true,
                colWidths: colWidths,
                fillHandle: true,
                renderer: HotUtil.myCellRenderer,
                language: 'zh-CN',
                licenseKey: 'non-commercial-and-evaluation',
                className: 'htMiddle htCenter',
                manualColumnResize: true,
                manualRowResize: true,
                afterChange: afterChange,
                contextMenu: {
                    items: HotUtil.getContextItems(treeNode)
                }
            });

            // hot.addHook('afterContextMenuShow', function (object) {
            //     $(object.menu.container).css("z-index", layer.zIndex + 1);
            // });

            hot.addHook('beforeContextMenuShow', function (object) {
                console.log(object);
            });
            /**
             * 自动填充顺序号
             */
            hot.addHook('afterAutofill', function (fillData, sourceRange, targetRange, direction) {
                //只有选中一个单元格自动填充才会增加序列
                if ((sourceRange.from.row === sourceRange.to.row) && (sourceRange.from.col === sourceRange.to.col)) {
                    var initValue = fillData[0][0];
                    var fromRow = targetRange.from.row;
                    var fromCol = targetRange.from.col;
                    var toRow = targetRange.to.row;
                    var toCol = targetRange.to.col;
                    if (direction === "down" || direction === "right") {
                        for (var r = fromRow; r <= toRow; r++) {
                            for (var c = fromCol; c <= toCol; c++) {
                                if (hot.getCellMeta(r, c).readOnly !== true) {
                                    var nextValue = "";
                                    if (direction === "down") {
                                        nextValue = hot.getDataAtCell(r - 1, c);
                                    } else if (direction === "right") {
                                        nextValue = hot.getDataAtCell(r, c - 1);
                                    }
                                    hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, "add"));
                                }
                            }
                        }
                    } else {
                        for (var r = toRow; r >= fromRow; r--) {
                            for (var c = toCol; c >= fromCol; c--) {
                                if (hot.getCellMeta(r, c).readOnly !== true) {
                                    var nextValue = "";
                                    if (direction === "up") {
                                        nextValue = hot.getDataAtCell(r + 1, c);
                                    } else if (direction === "left") {
                                        nextValue = hot.getDataAtCell(r, c + 1);
                                    }
                                    hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, 'subtract'));
                                }
                            }
                        }
                    }
                }
            });

            for (var m = 0; m < metas.length; m++) {
                var meta = metas[m];
                if (meta) {
                    //将表头添加背景颜色
                    if (meta.row < fixedRowsTop) {
                        if (meta.className) {
                            meta.className = meta.className + " td-bg";
                        } else {
                            meta.className = "td-bg";
                        }
                    }
                    window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
                    if (meta.readOnly) {
                        window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
                    }
                    if (meta['eles']) {
                        window.hot.setCellMeta(meta.row, meta.col, 'eles', meta['eles']);
                    }
                    if (meta['comment']) {
                        window.hot.setCellMeta(meta.row, meta.col, 'comment', meta['comment']);
                    }
                    if (meta['postType']) {
                        window.hot.setCellMeta(meta.row, meta.col, 'postType', meta['postType']);
                    }
                }
            }
            hot.render();

        }
        ,
        /**
         * 处理图片编号的显示
         * @param {Object} photoShowNums
         */
        dealImgNumShow: function (photoShowNums) {
            //表a1-12
            var tableNum = photoShowNums[0].substr(0, photoShowNums[0].lastIndexOf('-'));
            if (tableNum) {
                tableNum = tableNum + "-";
            }

            function getNum(photoShowNum) {
                //表a1-12-图8
                var tempArr = photoShowNum.split("-");
                var num = parseInt(tempArr[tempArr.length - 1].substr(1));
                return num;
            }

            var arr = [];

            for (var i = 0; i < photoShowNums.length; i++) {
                var photoShowNum = photoShowNums[i];
                var num = getNum(photoShowNum);
                arr.push(num);
            }


            // 定义一个新数组用于存储结果
            var newArr = [];

            // 使用for循环遍历原始数组
            for (var i = 0; i < arr.length; i++) {
                // 定义一个临时数组用于存储连续数字
                var tempArr = [arr[i]];

                // 使用while循环查找连续数字
                while (arr[i + 1] - arr[i] === 1) {
                    tempArr.push(arr[i + 1]);
                    i++;
                }
                // 将结果推入新数组
                newArr.push(tempArr);
            }

            var textArr = [""];

            for (var i = 0; i < newArr.length; i++) {
                var arr = newArr[i];
                if (arr.length == 1) {
                    textArr.push(tableNum + "图" + arr[0]);
                } else {
                    textArr.push(tableNum + "图" + arr[0] + "~" + "图" + arr[arr.length - 1]);
                }
            }
            var span = '<span style="color:red;">' + textArr.join("<br>") + '</span>'
            return span;
        }
        ,
        myCellRenderer: function (instance, td, row, col, prop, value, cellProperties) {
            Handsontable.renderers.TextRenderer.apply(this, arguments);
            const stringifyValue = Handsontable.helper.stringify(value);

            var eles = cellProperties['eles'];
            var isText = true;
            var signImgs = [];
            var photoNums = [];
            if (Array.isArray(eles)) {
                for (var i = 0; i < eles.length; i++) {
                    isText = false;
                    var ele = eles[i];
                    var eleType = ele.type;
                    if (eleType == "sign") {
                        var src = "/File" + ele.src;
                        var date = ele.date;
                        var $img = $('<br><img type="sign" src="' + src + '" class="sign-img" date="' + date + '"><br><span>' + date + '</span>');
                        signImgs.push($img);
                    } else if (eleType == "photo") {
                        photoNums.push(ele['photoShowNum']);
                    }
                }
            }
            if (!isText) {
                $(td).empty();
                $(td).append(stringifyValue);
                for (var i = 0; i < signImgs.length; i++) {
                    $(td).append(signImgs[i]);
                }
                if (photoNums.length > 0) {
                    $(td).append(HotUtil.dealImgNumShow(photoNums));
                }
            }
        }
        ,
        editTable: function (treeNode) {
            var log = {};
            log.operation = '编辑表格';
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上编辑表格";
            layer.open({
                title: '编辑：' + treeNode['NAME'],
                type: 1,
                fixed: false,
                maxmin: false,
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                zIndex: 1000,
                shadeClose: false,
                resize: false, //不允许拉伸
                area: [$('body').css('width'), $('body').css('height')],
                content: '<div id="handsontable" style="width:100%;height:100%;"></div>',
                cancel: function (index, layero) {
                    HotUtil.closeEdit(treeNode, function () {
                        if (window.hot) {
                            window.hot.destroy();
                        }
                        reloadTable(treeNode);
                    });
                },
                btn: ['保存', '取消'],
                yes: function () {
                    HotUtil.closeEdit(treeNode, function () {
                        HotUtil.saveTableData(treeNode, true, true);
                        if (window.hot) {
                            window.hot.destroy();
                        }
                    });
                },
                btn2: function () {
                    HotUtil.closeEdit(treeNode, function () {
                        if (window.hot) {
                            window.hot.destroy();
                        }
                        reloadTable(treeNode);
                        return true;
                    });
                },
                success: function () {

                    //重新请求表格数据 避免同时编辑的时候产生冲突
                    var afterChange = function (change, source) {
                        if (source != 'loadData' && source != 'populateFromArray') {
                            if (window.hot) {
                                if (!window.hot.getData.toString().startsWith('()')) {
                                    HotUtil.saveTableData(treeNode, false, false);
                                }
                            }
                        }
                    }
                    var cb_success = function (data) {
                        var saveData = data['SAVE_DATA'] || "";
                        HotUtil.renderHot(saveData, treeNode, data['TABLE_HEADER'], afterChange);
                        log.reqResult = 1;
                        addConfirmLog(log);
                    };
                    var cb_fail = function (res) {
                        log.reqResult = 0;
                        addConfirmLog(log);
                    };
                    postAjax("table", "QueryNodeById", {
                        id: treeNode['ID']
                    }, false, cb_success, cb_fail);
                }
            });
        }
        ,
        myGetCellsMeta: function () {
            var meta = [];
            var colCount = window.hot.countCols();
            var rowCount = window.hot.countRows();
            for (var i = 0; i < rowCount; i++) {
                for (var j = 0; j < colCount; j++) {
                    var cellMeta = hot.getCellMeta(i, j);
                    if (cellMeta.className) {
                        cellMeta.className = cellMeta.className.replaceAll("td-bg", "").trim();
                    }
                    meta.push(cellMeta);
                }
            }
            return meta;
        }
        ,
        /**
         * 获取表格列的宽度
         */
        getColWidths: function () {
            var countCols = hot.countCols();
            var colWidths = [];
            for (var i = 0; i < countCols; i++) {
                colWidths.push(hot.getColWidth(i));
            }
            return colWidths;
        }
        ,
        getHotData: function () {
            var oldData = window.hot.getData();
            var newData = [];
            for (var i = 0; i < oldData.length; i++) {
                var oldRow = oldData[i];
                var newRow = [];
                for (var j = 0; j < oldRow.length; j++) {
                    newRow.push(oldRow[j] || '');
                }
                newData.push(newRow);
            }
            return newData;
        }
        ,
        saveTableData: function (treeNode, isReloadTable, isValidEmpty) {
            var tableData = HotUtil.getHotData();
            var meta = HotUtil.myGetCellsMeta();
            if (isValidEmpty) {
                if (tableData.length == 0) {
                    layer.alert("空表不可提交");
                    return false;
                }
            }
            var merged = window.hot.getPlugin('MergeCells').mergedCellsCollection.mergedCells;
            var data = {
                tableData: tableData,
                merged: merged,
                meta: meta,
                colWidths: HotUtil.getColWidths(),
                workType: HotUtil.workType
            };
            data = JSON.stringify(data);
            //替换英文引号为中文引号
            data = data.replace(/'(.[^']*)'/g, "‘$1’");
            var param = {};
            param.id = treeNode['ID'];
            param.tableData = data;
            param.saveUser = sessionStorage.getItem("username");
            var log = {};
            log.operation = "保存数据";
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上保存数据";
            var cb_success = function () {
                log.reqResult = 1;
                if (isReloadTable) {
                    layer.closeAll();
                    reloadTable(treeNode);
                    addConfirmLog(log);
                }
            };
            var cb_fail = function () {
                log.reqResult = 0;
                addConfirmLog(log);
            };
            //使用ajax进行异步加载Tree
            postAjax("table", 'SaveTableData', param, isReloadTable, cb_success, cb_fail);
        }
        ,
        /**
         * 处理表格中图片的显示
         */
        dealTableImg: function ($table) {
            $table.find("td").each(function (i, n) {
                var $td = $(n);
                if ($td.find("img[type='photo']").length > 0) {
                    var imgs = [];
                    var photoShowNums = [];
                    $td.find("img[type='photo']").each(function (j, m) {
                        var $img = $(m);
                        var photoPath = $img.attr("src");
                        var photoName = $img.attr("photoName");
                        var photoShowNum = $img.attr("photoShowNum");
                        var photoId = $img.attr("id");
                        var photoformat = $img.attr("photoformat");
                        var imgData = {
                            photoPath: photoPath,
                            photoName: photoName,
                            photoShowNum: photoShowNum,
                            photoId: photoId,
                            photoformat: photoformat
                        };
                        imgs.push(imgData);
                        photoShowNums.push(photoShowNum);

                        //删除元素
                        var $br1 = $img.prev();
                        var $br2 = $img.next();
                        var $span = $br2.next();
                        $br1.remove();
                        $br2.remove();
                        $span.remove();
                        $img.remove();
                    });
                    $td.data("hasImg", true);
                    var numText = HotUtil.dealImgNumShow(photoShowNums);
                    $td.append(numText);
                    HotUtil.tdImgs[$td.attr("row") + "-" + $td.attr("col")] = imgs;
                } else {
                    $td.data("hasImg", false);
                }
            });
        }
        ,
        /**
         * 锁定之后的右键菜单
         * @param {Object} n td元素
         * @param {Object} treeNode 树节点
         * @param {Object} tableSel 显示表格的页面元素选择器
         * @param {Object} isUnlockRow 是否解锁此行
         */
        tdContextSignMenu: function (n, treeNode, tableSel, isUnlockRow) {
            var contextEle = void 0;
            var menus = [];
            if (isUnlockRow && isAdmin) {
                menus.push({
                    text: "解锁此行",
                    icon: imgPath + 'unlock.png',
                    callback: function () {
                        HotUtil.updateLockRow(contextEle, treeNode, 'unlock');
                    }
                });
            }
            menus = menus.concat([{
                text: "鼠标签名",
                icon: imgPath + 'sign.png',
                callback: function () {
                    HotUtil.sign(treeNode, contextEle, tableSel);
                }
            }, {
                text: "上传签章",
                icon: imgPath + 'upload-sign.png',
                callback: function () {
                    HotUtil.uploadSign(treeNode, contextEle, tableSel);
                }
            }]);
            if ($(n).attr("img") == "true") {
                menus.push({
                    text: "查看图片",
                    icon: imgPath + 'view.png',
                    callback: function () {
                        HotUtil.viewImage(treeNode, contextEle, tableSel, false);
                    }
                });
            }
            $(n).contextMenu({
                width: 110,
                menu: menus,
                target: function (ele) {
                    contextEle = ele;
                }
            });
        }
        ,
        /**
         * 更新锁定行的信息
         * @param {Object} contextMenuTd 右键的单元格
         * @param {Object} treeNode
         * @param {Object} type 是锁定行还是解锁行 lock or unlock
         */
        updateLockRow: function (contextMenuTd, treeNode, type) {
            var msg = "";
            if (type == 'lock') {
                msg = "锁定之后不可编辑，是否继续？";
            } else {
                msg = "解除锁定之后会清除签名图片，并且不可恢复，是否继续？";
            }
            layer.confirm(msg, {
                icon: 3,
                title: '提示'
            }, function (index) {
                var tds = [];
                contextMenuTd.parent().find("td").each(function (i, n) {
                    tds.push({
                        row: parseInt($(n).attr("row")),
                        col: parseInt($(n).attr("col"))
                    });
                });

                HotUtil.uploadLockTds(tds, treeNode, type, function () {
                    reloadTable(treeNode);
                });
            });
        }
        ,

        /**
         * 更新锁定单元格的信息
         * @param {Object} tds 选中的单元格信息
         * @param {Object} id 确认表的id
         * @param {Object} type 是锁定行还是解锁行 lock or unlock
         */
        uploadLockTds: function (tds, treeNode, type, successFn) {
            var log = {};
            if (type == "lock") {
                log.operation = "锁定";
            } else {
                log.operation = "解锁";
            }
            log.operation = log.operation + "行";
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            var row = tds[0].row + 1;
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】的表中" + log.operation + "了第" + row + "行";
            var cb_success = function () {
                log.reqResult = 1;
                addConfirmLog(log);
                successFn();
            }
            var cb_fail = function () {
                log.reqResult = 0;
                addConfirmLog(log);
            }
            postAjax('table', "UpdateLockRow", {
                id: treeNode['ID'],
                lockRow: JSON.stringify(tds),
                userName: sessionStorage.getItem('username'),
                type: type
            }, true, cb_success, cb_fail);
        }
        ,
        /**
         * 锁定之前的右键菜单
         * @param {Object} n td元素
         * @param {Object} treeNode 树节点
         * @param {Object} tableSel 显示表格的页面元素选择器
         * @param {Object} isLockRow 是否锁定此行
         */
        tdContextEditMenu: function (n, treeNode, tableSel, isLockRow) {
            var contextEle = void 0;
            var menus = [];
            if (isLockRow) {
                menus.push({
                    text: "锁定该行",
                    icon: imgPath + 'lock.png',
                    callback: function () {
                        HotUtil.openEdit(treeNode, 0, function () {
                            HotUtil.updateLockRow(contextEle, treeNode, 'lock');
                        });
                    }
                });
            }
            menus.push({
                text: "上传图片",
                icon: imgPath + 'upload-image.png',
                callback: function () {
                    HotUtil.openEdit(treeNode, 0, function () {
                        HotUtil.insertImage(treeNode, contextEle, tableSel);
                    });
                }
            });
            if (HotUtil.isAllowTakePhoto()) {
                menus.push({
                    text: "拍照",
                    icon: imgPath + 'take-photo.png',
                    callback: function () {
                        HotUtil.takePhoto(treeNode, contextEle, tableSel);
                    }
                });
            }
            if ($(n).attr("img") == "true") {
                menus.push({
                    text: "查看图片",
                    icon: imgPath + 'view.png',
                    callback: function () {
                        HotUtil.viewImage(treeNode, contextEle, tableSel, true);
                    }
                });
            }
            $(n).contextMenu({
                width: 110,
                menu: menus,
                target: function (ele) {
                    contextEle = ele;
                }
            });
        }
        ,
        base64Upload: function (dataURL, uploadURL, successFn) {
            var loadIndex = layer.load(); //上传loading
            // 解码Base64字符串
            var base64Data = dataURL.split(',')[1];
            var binaryData = atob(base64Data);

            // 将二进制数据创建为Blob对象
            var blobData = new Blob([new Uint8Array(binaryData.length).map(function (_, i) {
                return binaryData.charCodeAt(i);
            })], {
                type: 'image/bmp'
            });

            // 创建一个FormData对象并将Blob对象附加到其中
            var formData = new FormData();
            formData.append('file', blobData, 'image.bmp'); // 'file'是服务器接受文件的字段名， 'image.bmp'是上传的文件名
            formData.append('name', 'image.bmp');
            formData.append('reqIdent', new Date().getTime());
            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', getReqUrl('table', 'UploadImg'), true); // 上传到服务器的URL
            xhr.onreadystatechange = function () {

                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        layer.close(loadIndex);
                        var res = JSON.parse(xhr.responseText);
                        if (res.success) {
                            successFn(res.data.path);
                        } else {
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                    } else {
                        // 处理上传失败的回调
                        layer.alert("图片上传失败", {
                            icon: 2
                        });
                    }
                }
            };

            xhr.send(formData);
        }
        ,
        clickTakePhoto: function () {
            var canvas = document.getElementById('canvas');
            var video = document.getElementById('video');
            $("#video").hide();
            $("#canvas").show();
            var context = canvas.getContext('2d');
            context.drawImage(video, 0, 0);
        }
        ,
        takePhoto: function (treeNode, ele, tableSel) {
            ele = HotUtil.findDataTableEle(ele);
            var mediaStreamTrack = null;
            var isTake = false;
            var photoWidth = 960;
            var photoHeight = 600;
            layer.open({
                title: false,
                closeBtn: 0,
                type: 1,
                area: [photoWidth + 'px', (photoHeight + 68) + 'px'],
                content: '<div id="take-photo-content"></div>',
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                resize: false,
                btn: ['拍照', '重拍', '上传', '取消'],
                yes: function () {
                    HotUtil.clickTakePhoto();
                    isTake = true;
                },
                btn2: function () {
                    $("#video").show();
                    $("#canvas").hide();
                    isTake = false;
                    return false;
                },
                btn3: function () {
                    var dataUrl = document.getElementById('canvas').toDataURL();
                    if (isTake) {
                        HotUtil.base64Upload(dataUrl, "/Upload", function (filePath) {
                            HotUtil.updateTableSign(filePath, ele, tableSel, treeNode, "photo");
                            mediaStreamTrack.stop();
                            $("#video").remove();
                            $(".fixed-div").addClass("layui-hide");
                        });
                    } else {
                        layer.alert('请先拍照！', {
                            icon: 2
                        });
                    }
                    return false;
                },
                btn4: function () {
                    try {
                        mediaStreamTrack.stop();
                    } catch (e) {
                    }
                    $(".fixed-div").addClass("layui-hide");
                    $("#video").remove();
                    return true;
                },
                success: function () {
                    var tpl = '<video id="video" width="' + photoWidth + '" height="' + photoHeight + '"  autoplay="autoplay">\
							</video>\
							<canvas id="canvas" width="' + photoWidth + '" height="' + photoHeight + '" style="display:none" ></canvas>';
                    $("#take-photo-content").append(tpl);
                    var video = document.getElementById('video');
                    if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia || navigator
                        .mozGetUserMedia) {

                        //访问用户媒体设备的兼容方法
                        function getUserMedia(constraints, success, error) {
                            try {
                                if (navigator.mediaDevices.getUserMedia) {
                                    //最新的标准API
                                    navigator.mediaDevices.getUserMedia(constraints).then(success).catch(error);
                                } else if (navigator.webkitGetUserMedia) {
                                    //webkit核心浏览器
                                    navigator.webkitGetUserMedia(constraints, success, error)
                                } else if (navigator.mozGetUserMedia) {
                                    //firfox浏览器
                                    navigator.mozGetUserMedia(constraints, success, error);
                                } else if (navigator.getUserMedia) {
                                    //旧版API
                                    navigator.getUserMedia(constraints, success, error);
                                }
                            } catch (e) {
                                //TODO handle the exception
                            }
                        }

                        function renderMedia(type, errorFn) {
                            //默认后置摄像头
                            var facingMode = {
                                exact: "environment"
                            };
                            //前置摄像头
                            if (type === "user") {
                                facingMode = "user";
                            }
                            getUserMedia({
                                video: {
                                    width: photoWidth,
                                    height: photoHeight,
                                    facingMode: facingMode
                                }
                            }, function (stream) {
                                //兼容webkit核心浏览器
                                var CompatibleURL = window.URL || window.webkitURL;
                                //将视频流设置为video元素的源
                                console.log(stream);
                                mediaStreamTrack = stream.getTracks()[0];
                                //video.src = CompatibleURL.createObjectURL(stream);
                                video.srcObject = stream;
                                video.play();
                                //显示大的拍照按钮
                                $(".fixed-div").removeClass("layui-hide");
                                $(".fixed-div").unbind("click").bind("click", function () {
                                    HotUtil.clickTakePhoto();
                                    isTake = true;
                                });
                            }, function (error) {
                                errorFn(error);
                            });
                        }

                        //调用用户媒体设备, 访问摄像头
                        renderMedia("environment", function (error) {
                            renderMedia("user", function (error) {
                                $("#video").remove();
                                $("#take-photo-content").append('<div style="color:red;padding:20px;font-size:20px">访问用户媒体设备失败,请检查是否连接摄像头设备！</div>');
                                console.log(`访问用户媒体设备失败${error.name}, ${error.message}`);
                            });
                        });
                    } else {
                        alert('不支持访问用户媒体');
                    }
                }
            });
        }
        ,
        uploadSignLayer: function (uploadSuccessFn) {

            var layerHeight = "460px";
            if (isAdmin) {
                layerHeight = "510px";
            }

            var layerIndex = layer.open({
                title: "上传签章",
                type: 1,
                area: ['410px', layerHeight],
                content: '<div id="uploadSignContent" style="padding: 15px 0px 0px 0px;"></div>',
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                resize: false,
                btn: ['确定', '取消'],
                yes: function () {
                    var signSrc = $("#previewImg").attr("src");
                    if (signSrc) {
                        $("#uploadStart").click();
                    } else {
                        layer.alert("请选择签章图片！", {
                            icon: 2
                        });
                    }
                },
                btn2: function () {
                    return true;
                },
                success: function () {
                    var signDateDiv = "";
                    if (isAdmin) {
                        signDateDiv = '<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">签署日期:</label>\
									<div class="layui-input-block">\
										<input type="text" class="layui-input" id="sign-date" style="width:274px;">\
									</div>\
								</div>';
                    }
                    var tpl = '<form class="layui-form" lay-filter="uploadSignForm">' + signDateDiv + '<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">文件内容:</label>\
									<div class="layui-input-block">\
										<div class="layui-upload">\
                                             <div id="uploadChoice">选择文件</div>											\
                                             <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
										</div>\
									</div>\
								</div>\
								<div class="layui-form-item selectedFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">已选文件:</label>\
									<div class="layui-input-block">\
										<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
									</div>\
								</div>\
								<div class="layui-form-item selectedFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">签名用户:</label>\
									<div class="layui-input-block">\
									    <input type="text" class="layui-input" id="sign-name" style="width:274px;">\
									</div>\
								</div>\
								<div class="layui-form-item" id="previewFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">签章预览:</label>\
									<div class="layui-input-block">\
										<img style="max-width: 285px;max-height: 150px;" id="previewImg" />\
									</div>\
								</div>\
								<div class="layui-form-item" style="display:none;">\
									<center>\
										<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
										<button id="btn_cancel" class="layui-btn">取消</button>\
									</center>\
								</div>\
							</form>'
                    $("#uploadSignContent").append(tpl);
                    form.render(null, 'uploadSignForm');
                    laydate.render({
                        elem: '#sign-date' //指定元素
                    });
                    var reqIdent = new Date().getTime();
                    var uploader = WebUploader.create({
                        // 选完文件后，是否自动上传。
                        auto: false,
                        // 文件接收服务端。
                        server: getReqUrl('table', 'UploadImg'),
                        // 选择文件的按钮。可选。
                        pick: {
                            id: '#uploadChoice',
                            multiple: false // 设置multiple为false
                        },
                        timeout: 60 * 60 * 1000,
                        // 配置分片上传
                        chunked: true,
                        chunkSize: 10 * 1024 * 1024,
                        fileNumLimit: 1,
                        accept: {
                            title: 'Images',
                            extensions: 'gif,jpg,jpeg,bmp,png',
                            mimeTypes: 'image/*'
                        },
                        thumb: {
                            width: 285,
                            height: 150,
                            // 图片质量，只有type为`image/jpeg`的时候才有效。
                            quality: 100,
                            // 是否允许放大，如果想要生成小图的时候不失真，此选项应该设置为false.
                            allowMagnify: false,
                            // 是否允许裁剪。
                            crop: false
                        },
                        formData: {
                            reqIdent: reqIdent,
                            extraData: JSON.stringify({
                                username: sessionStorage.getItem("username")
                            })
                        }
                    });
                    uploader.on('uploadBeforeSend', function (object, data, headers) {

                    });

                    // 当有文件被添加进队列之前触发
                    uploader.on('beforeFileQueued', function (file) {
                        // 检查队列中是否已经有文件
                        if (uploader.getFiles().length > 0) {
                            // 如果有文件，先移除旧的文件
                            uploader.removeFile(uploader.getFiles()[0], true);
                        }
                    });

                    // 当有文件被添加进队列的时候
                    uploader.on('fileQueued', function (file) {
                        $(".selectedFile").show();
                        $("#selectedFileName").text(file.name);
                        $("#previewFile").show();
                        $("#sign-name").val(file.name.split('.')[0]);
                        uploader.makeThumb(file, function (error, ret) {
                            if (error) {

                            } else {
                                $("#previewImg").attr("src", ret);
                            }
                        });
                    });

                    uploader.on('uploadSuccess', function (file, res) {
                        if (res.success) {
                            var src = res.data.path;
                            var date = $("#sign-date").val() || layui.util.toDateString(new Date(), 'yyyy-MM-dd');
                            var signName = $("#sign-name").val();
                            uploadSuccessFn(src, date, signName);
                            layer.close(layerIndex);
                        } else {
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                    });

                    // 文件上传失败，显示上传出错。
                    uploader.on('uploadError', function (file) {

                    });

                    // 完成上传完毕，成功或者失败，先删除进度条。
                    uploader.on('uploadComplete', function (file) {

                    });

                    // 当所有文件上传结束时触发
                    uploader.on('uploadFinished', function () {

                    });

                    $("#uploadStart").on('click', function () {
                        uploader.upload();
                    });

                }
            });
        }
        ,
        uploadSign: function (treeNode, ele, tableSel) {
            ele = HotUtil.findDataTableEle(ele);
            HotUtil.uploadSignLayer(function (src, date, signName) {
                HotUtil.updateTableSign(src, ele, tableSel, treeNode, "upload", date, signName);
            });
        }
        ,
        sign: function (treeNode, ele, tableSel) {
            ele = HotUtil.findDataTableEle(ele);
            layer.open({
                title: "签名",
                type: 1,
                area: ['1000px', '575px'],
                content: '<div id="report-signContent"></div>',
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                resize: false,
                btn: ['确定', '重签', '取消'],
                yes: function () {
                    var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
                    if (dataUrl) {
                        HotUtil.updateTableSign(dataUrl, ele, tableSel, treeNode, "mouse");
                    } else {
                        layer.alert('请先签名！', {
                            icon: 2
                        });
                    }
                },
                btn2: function () {
                    $('.js-signature').eq(0).jqSignature('clearCanvas');
                    return false;
                },
                btn3: function () {
                    return true;
                },
                success: function () {
                    var tpl = '<div class="js-signature" data-width="1000" data-height="460" data-border="1px solid #333"\
									data-line-color="#000" data-line-width="5" data-auto-fit="true">\
									</div>';
                    $("#report-signContent").append(tpl);
                    $('.js-signature').jqSignature();
                }
            });
        }
        ,
        updateTableSign: function (signSrc, ele, tableSel, treeNode, type, signDate = layui.util.toDateString(new Date(), 'yyyy-MM-dd'), signName = "") {
            var log = {};
            if (type === "photo") {
                log.operation = "拍照";
            } else if (type === "mouse") {
                log.operation = "鼠标签名";
            } else if (type === "wacom") {
                log.operation = "手写板签名";
            } else if (type === "upload") {
                log.operation = "上传签章";
            }
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】的表中第" +
                (parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele).attr("col")) + 1) + "列" + log.operation;

            var cb_success = function (resData) {
                log.content = log.content + "，照片路径为：" + resData;
                log.reqResult = 1;
                reloadTable(treeNode);
                layer.closeAll();
            }
            var cb_fail = function () {
                log.reqResult = 0;
                addConfirmLog(log);
            };

            //同步新增
            postAjax('table', "AddSign", {
                id: treeNode['ID'],
                img: signSrc,
                row: $(ele).attr("row"),
                col: $(ele).attr("col"),
                type: type,
                date: signDate,
                signName: signName,
                creator: sessionStorage.getItem('username')
            }, true, cb_success, cb_fail);
        }
        ,

//找到数据存放表格对应的ele
        findDataTableEle: function (ele) {
            var tdCol = $(ele).attr("col");
            var tdRow = $(ele).attr("row");
            var dataTableEle = $(".data-table").find('td[col=' + tdCol + '][row=' + tdRow + ']');
            return dataTableEle[0];
        }
        ,
        getPreviewEle: function (imgs, isAllowDel) {
            var $ul = $('<ul class="picView-magnify-list" style="padding: 30px"></ul>');
            for (var i = 0; i < imgs.length; i++) {
                var photoPath = imgs[i].photoPath;
                if (photoPath.indexOf("File") == -1) {
                    photoPath = "/File" + photoPath;
                }
                var photoName = imgs[i].photoName + "." + imgs[i].photoformat;
                var photoShowNum = imgs[i].photoShowNum;
                var photoId = imgs[i].photoId || imgs[i].id;
                var deleteDiv = '<div class="img-icon">\
												<i class="layui-icon" photo-id="' + photoId + '" photo-name="' + photoName + '" photo-num="' + photoShowNum + '" photo-path="' + photoPath + '">&#xe640;</i>\
											</div>';
                var li = '<li>\
												<a href="javascript:void(0)" data-magnify="gallery" data-group="g1" data-src="' + photoPath + '" data-caption="' + photoShowNum + '">\
													<img src="' + photoPath + '">\
												</a>\
												<div class="img-title">' + photoShowNum + '</div>\
											</li>';
                var $li = $(li);
                $ul.append($li);

                if (isAllowDel) {
                    $li.prepend(deleteDiv);
                }
            }
            return $ul;
        }
        ,
        viewImageLayer: function (imgs, isAllowDel, cancelFn, delClickFn) {
            layer.open({
                title: '查看图片',
                type: 1,
                area: ['1140px', '560px'],
                zIndex: layer.zIndex + 1,
                content: '<div id="previewContent"></div>',
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                resize: false,
                cancel: function (index, layero) {
                    cancelFn();
                },
                success: function () {
                    var $ul = HotUtil.getPreviewEle(imgs, isAllowDel);
                    $('#previewContent').append($ul);
                    if (isAllowDel) {
                        //删除事件
                        $ul.find('i').click(function () {
                            var $i = $(this);
                            delClickFn($i, $ul);
                        });
                    }
                    $('[data-magnify]').magnify({
                        resizable: false,
                        initMaximized: true,
                        zIndex: layer.zIndex + 1,
                        headerToolbar: [
                            'close'
                        ],
                        i18n: {
                            minimize: '最小化',
                            maximize: '最大化',
                            close: '关闭',
                            zoomIn: '缩小',
                            zoomOut: '放大',
                            prev: '上一张',
                            next: '下一张',
                            fullscreen: '全屏',
                            actualSize: '实际大小',
                            rotateLeft: '向左旋转',
                            rotateRight: '向右旋转',
                        }
                    });

                }
            });
        }
        ,
        /**
         * 查看图片
         * @param {Object} treeNode 树节点
         * @param {Object} ele 当前td元素
         * @param {Object} tableSel 显示表格的页面元素选择器
         * @param {Object} isAllowDel 是否允许删除图片
         * @returns {boolean}
         */
        viewImage: function (treeNode, ele, tableSel, isAllowDel) {
            ele = HotUtil.findDataTableEle(ele);
            if ($(ele).attr("img") == "false") {
                layer.msg("暂无图片");
                return false;
            }
            var imgs = HotUtil.tdImgs[$(ele).attr("row") + "-" + $(ele).attr("col")] || [];
            HotUtil.viewImageLayer(imgs, isAllowDel, function () {
                if ($(".picView-magnify-list").data("delete")) {
                    reloadTable(treeNode);
                }
            }, function ($i, $ul) {
                var src = $i.attr("photo-path");
                var photoShowNum = $i.attr("photo-num");
                var photoName = $i.attr("photo-name");
                var log = {};
                log.operation = "删除照片";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中的第" +
                    (parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele).attr("col")) + 1) +
                    "列删除了照片（名称：" + photoName + "，编号：" + photoShowNum + "，路径：" + src + "）";

                postAjax("table", "DeletePhoto", {
                    id: treeNode['ID'],
                    src: src,
                    col: $(ele).attr("col"),
                    row: $(ele).attr("row")
                }, true, function () {
                    $i.parent().parent().remove();
                    $ul.data("delete", true);
                    log.reqResult = 1;
                    addConfirmLog(log);
                }, function () {
                    log.reqResult = 0;
                    addConfirmLog(log);
                });
            });
        }
        ,
        /**
         * @param {Object} treeNode
         * @param {Object} ele
         * @param {Object} photoIndex
         * @param {Object} tableSel
         */
        insertImage: function (treeNode, ele, tableSel) {
            ele = HotUtil.findDataTableEle(ele);
            var eleCol = parseInt($(ele).attr("col"));
            var eleRow = parseInt($(ele).attr("row"));
            HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
                var photos = [];
                var photoText = "[";
                var photoCount = 0;
                for (var i = 0; i < datas.length; i++) {
                    var rowData = datas[i];
                    if (rowData.success) {
                        var params = {};
                        params.id = treeNode.ID;
                        params.photoPath = rowData.photoPath;
                        params.photoFormat = rowData.photoFormat;
                        params.photoName = rowData.photoName;
                        params.photoNumber = rowData.photoNumber;
                        params.photoShowNum = rowData.photoShowNum;
                        params.photoSize = rowData.photoSize;
                        params.index = rowData.index;
                        params.creator = sessionStorage.getItem('username');
                        params.col = eleCol;
                        params.row = eleRow;
                        photos.push(params);
                        photoText += "{照片名称：" + (params.photoName + "." + params.photoFormat) + "，路径：" + params.photoPath + "，大小：" + params.photoSize + "，编号：" + params.photoShowNum + "},";
                        photoCount++;
                    }
                }
                photoText = photoText.substring(0, photoText.length - 1);
                photoText += "]";
                var log = {};
                log.operation = "上传图片";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                var logContent = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中的第" + (eleRow + 1) + "行第" + (eleCol + 1) +
                    "列中上传了" + photoCount + "张图片，分别是" + photoText;
                log.content = logContent;
                var cb_success = function () {
                    log.reqResult = 1;
                    reloadTable(treeNode);
                    layer.close(layerIndex);
                    addConfirmLog(log);
                }
                //请求失败的回调
                var cb_fail = function () {
                    log.reqResult = 0;
                    addConfirmLog(log);
                };
                //同步新增
                postAjax("table", "AddPhotos", {
                    datas: photos,
                    id: treeNode['ID']
                }, true, cb_success, cb_fail);
            });
        }
        ,
        insertImageLayer: function (treeNode, uploadAllDoneSuccess) {
            var tpl = '<div style="padding:12px;">\
							<div class="layui-upload" style="margin-bottom:2px;">\
								<button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择图片</button>\
								<button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>\
							</div>\
						    <table id="photo-table" lay-filter="photo-table"></table>\
						</div>';
            var cb_success = function (photoIndex) {
                var loadIndex;
                var layerIndex = layer.open({
                    title: '上传图片',
                    type: 1,
                    area: ['1100px', '660px'],
                    content: tpl,
                    anim: false,
                    openDuration: 200,
                    isOutAnim: false,
                    closeDuration: 200,
                    resize: false,
                    btn: ['上传', '取消'],
                    yes: function () {
                        var photoDatas = trimSpace(layui.table.cache["photo-table-id"]);
                        if (photoDatas.length == 0) {
                            layer.msg('请选择照片!');
                            return false;
                        }
                        var flag = true;
                        for (var i = 0; i < photoDatas.length; i++) {
                            var d = photoDatas[i];
                            if (d.photoName == '' || d.photoNumber == '') {
                                flag = false;
                                break;
                            }
                        }
                        if (!flag) {
                            layer.msg('照片名称和照片编号为必填项！');
                            return false;
                        } else {
                            $('#startUpload').click();
                        }
                    },
                    btn2: function () {
                        return true;
                    },
                    success: function () {

                        var photoTable = table.render({
                            elem: '#photo-table',
                            data: [],
                            height: 485,
                            cellMinWidth: 80, //全局定义常规单元格的最小宽度
                            id: 'photo-table-id',
                            lineStyle: 'height: 95px;',
                            limit: 999,
                            className: 'photo-table',
                            cols: [
                                [{
                                    title: '序号',
                                    type: 'numbers'
                                }, {
                                    field: 'photoName',
                                    title: '图片名称',
                                    width: 200
                                }, {
                                    field: 'photoSize',
                                    title: '图片大小',
                                    width: 100,
                                    align: 'right'
                                }, {
                                    field: 'photoShowNum',
                                    title: '图片编号',
                                    width: 350
                                }, {
                                    field: 'index',
                                    hide: true
                                }, {
                                    field: 'photoNumber',
                                    hide: true
                                }, {
                                    field: 'photoFormat',
                                    title: '图片格式',
                                    width: 100
                                }, {
                                    field: 'result',
                                    title: '图片预览',
                                    width: 120,
                                    templet: '<div>\
										<img style="max-width: 90px;height: 90px;" src="{{d.result}}" />\
									</div>'
                                }, {
                                    field: 'operation',
                                    title: '操作',
                                    templet: '<div>\
										<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>\
									</div>',
                                    width: 100
                                }]
                            ]
                        });

                        table.on('tool(photo-table)', function (obj) {
                            if (obj.event === 'del') {
                                obj.del();
                                var index = obj.tr.find('td[data-field="index"]').children('div').text();
                                layui.table.cache["photo-table-id"] = trimSpace(layui.table.cache["photo-table-id"]);
                                removeArray('index', index, layui.table.cache["photo-table-id"]);
                                delete existPhotos[index];
                                photoTable.reload({
                                    data: layui.table.cache["photo-table-id"]
                                });
                            }
                        });


                        function getTableIndex(key, tableData) {
                            var index = -1;
                            for (var i = 0; i < tableData.length; i++) {
                                if (key == tableData[i].index) {
                                    index = i;
                                    break;
                                }
                            }
                            return index;
                        }

                        //上传成功的照片合集
                        var uploadSuccessPhotos = [];
                        //上传组件
                        var photoUploadIns = upload.render({
                            elem: '#chooseFile',
                            url: getReqUrl('table', 'UploadImg'),
                            data: {
                                reqIdent: function () {
                                    return new Date().getTime()
                                },
                                name: function (index, file) {
                                    return file.name;
                                }
                            },
                            auto: false,
                            multiple: true,
                            accept: 'images',
                            field: 'file',
                            bindAction: '#startUpload',
                            dataType: "json",
                            before: function (obj) {
                                loadIndex = layer.load(); //上传loading
                            },
                            choose: function (obj) {
                                layui.table.cache["photo-table-id"] = trimSpace(layui.table.cache["photo-table-id"]); //重置表格数据
                                var tableData = layui.table.cache["photo-table-id"];
                                //将每次选择的文件追加到文件队列
                                var files = obj.pushFile();
                                existPhotos = files;

                                function isInTable(key, tableData) {
                                    for (var i = 0; i < tableData.length; i++) {
                                        if (key == tableData[i].index) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }

                                var allConut = Object.keys(existPhotos).length;
                                for (var key in existPhotos) {
                                    if (!isInTable(key, tableData)) {
                                        var file = existPhotos[key];
                                        var fileName = file.name;
                                        var o = {};
                                        o.photoName = fileName.substr(0, fileName.lastIndexOf('.'));
                                        o.photoFormat = fileName.substr(fileName.lastIndexOf('.') + 1);
                                        o.photoSize = (file.size / 1024).toFixed(1) + 'kb';
                                        o.index = key;
                                        o.file = file;
                                        layui.table.cache["photo-table-id"].push(o);
                                        layui.table.cache["photo-table-id"].sort(function (x, y) {
                                            return x['photoName'].localeCompare(y['photoName'], "zh");
                                        });
                                        for (var i = 0, initIndex = photoIndex; i < layui.table.cache["photo-table-id"].length; i++, initIndex++) {
                                            var tempStr = treeNode['NAME'];
                                            if (treeNode['TABLE_NUM']) {
                                                tempStr = treeNode['TABLE_NUM'];
                                            }
                                            layui.table.cache["photo-table-id"][i].photoShowNum = tempStr + "-图" + initIndex;
                                            layui.table.cache["photo-table-id"][i].photoNumber = initIndex;
                                        }
                                        photoTable.reload({
                                            data: layui.table.cache["photo-table-id"]
                                        });
                                    }
                                }

                                //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                                obj.preview(function (index, file, result) {
                                    var rowInex = getTableIndex(index, layui.table.cache["photo-table-id"]);
                                    table.updateRow('photo-table-id', {
                                        index: rowInex,
                                        data: {
                                            result: result
                                        },
                                        related: function (field, index) {
                                            true;
                                        }
                                    });
                                });
                                photoUploadIns.reload();
                            },
                            done: function (res, fileIndex) {
                                if (res.success) {
                                    var rowInex = getTableIndex(fileIndex, layui.table.cache["photo-table-id"]);
                                    table.updateRow('photo-table-id', {
                                        index: rowInex,
                                        data: {
                                            photoPath: res.data.path,
                                            success: true
                                        },
                                        related: function (field, index) {
                                            true;
                                        }
                                    });
                                } else {
                                    layer.alert(res.msg, {
                                        icon: 2
                                    });
                                }
                            },
                            allDone: function (obj) {
                                layer.close(loadIndex);
                                if (obj.aborted > 0) {
                                    layer.msg('上传失败！');
                                } else {
                                    uploadAllDoneSuccess(layui.table.cache["photo-table-id"], layerIndex, loadIndex);
                                }
                            }
                        });
                    }
                });
            }
            //请求失败的回调
            postAjax("table", "GetNewPhotoNum", {
                id: treeNode.ID
            }, false, cb_success);
        }
        ,
//表格行的右键菜单
        trContextMenu: function ($newTr, treeNode, tableSel) {
            var trIsHasLockTd = false;
            //处理锁定单元格的显示
            $newTr.find("td").each(function (j, m) {
                if ($(m).attr("lock") == "true") {
                    trIsHasLockTd = true;
                    $(m).addClass("htDimmed");
                    if (j == 0) {
                        HotUtil.tdContextSignMenu(m, treeNode, tableSel, true);
                    } else {
                        HotUtil.tdContextSignMenu(m, treeNode, tableSel, false);
                    }
                } else {
                    HotUtil.tdContextEditMenu(m, treeNode, tableSel, false);
                }
            })
            if (!trIsHasLockTd) {
                //没有锁定的话
                $newTr.find("td").each(function (j, m) {
                    if (j == 0) {
                        HotUtil.tdContextEditMenu(m, treeNode, tableSel, true);
                    } else {
                        HotUtil.tdContextEditMenu(m, treeNode, tableSel, false);
                    }
                })
            }
        }
        ,
        locationTreeNodeUtil: function (allDatas, ztreeObj, idName, callbackFn) {
            function recursionTree(allDatasIndex) {
                var treeId = allDatas[allDatasIndex][idName];
                if (treeId !== undefined) {
                    var thisNode = ztreeObj.getNodeByParam(idName, treeId, null);
                    if (allDatasIndex == allDatas.length - 1) {
                        ztreeObj.selectNode(thisNode);
                        callbackFn(thisNode);
                    } else {
                        //如果这个节点是父节点的话
                        if (thisNode.ISPARENT) {
                            //判断是否是展开的状态
                            if (thisNode.open) {
                                var newIndex = allDatasIndex + 1;
                                recursionTree(newIndex);
                            } else {
                                //如果没有展开的话需要请求该节点下的子数据
                                ztreeObj.reAsyncChildNodes(thisNode, "refresh", false, function () {
                                    //展开之后再判断下一层级的节点
                                    var newIndex = allDatasIndex + 1;
                                    recursionTree(newIndex);
                                });
                            }
                        }
                    }
                }
            }

            recursionTree(0);
        }
        ,
        /**
         * 检查此客户端是否允许拍照
         */
        isAllowTakePhoto: function () {
            var flag = false;
            try {
                if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia) {
                    flag = true;
                }
            } catch (e) {

            }
            return flag;
        }
        ,
        /**
         * 定位在线确认树节点
         * @param {*} nodeId
         */
        locationTreeNode: function (nodeId) {
            var cb_success = function (data) {
                HotUtil.locationTreeNodeUtil(data, ztreeObj, 'ID', function (thisNode) {
                    loadTreeMenu();
                    reloadTable(thisNode);
                });
            }
            postAjax("tree", "QueryAllPId", {
                id: nodeId
            }, false, cb_success);
        }
        ,
//表格增加跳转连接
        tableAddLink: function (treeNode, $table) {
            //如果表为A表 则需要在表中显示B表的超链接 根据B表的表序号来识别
            if (treeNode['TYPE'] === 'a') {
                //首先查询型号下的所有表的信息
                var cb_success = function (resData) {
                    var bNodes = resData;
                    for (var i = 0; i < bNodes.length; i++) {
                        var bNode = bNodes[i];
                        $table.find("td").each(function (j, td) {
                            if ($(td).text().indexOf(bNode['TABLE_NUM']) > -1) {
                                var locationArr = [];
                                $(td).find(".location-text").each(function (x, span) {
                                    locationArr.push({
                                        id: $(span).attr("refid"),
                                        num: $(span).text()
                                    });
                                    $(span).text('');
                                });
                                var spanHtml = '<span class="location-text" style="color:blue;cursor:pointer;text-decoration:underline"  refid="' + bNode['ID'] + '">' + bNode['TABLE_NUM'] + '</span>';
                                $(td).html($(td).html().replaceAll(bNode['TABLE_NUM'], spanHtml));
                                for (var x = 0; x < locationArr.length; x++) {
                                    $(td).find('span[refid=' + locationArr[x].id + ']').text(locationArr[x].num);
                                }
                            }
                        });
                    }
                    $(".location-text").click(function (e) {
                        HotUtil.locationTreeNode($(e.target).attr("refid"));
                    });
                }
                postAjax("tree", "QueryModelNodesById", {
                    id: treeNode['ID']
                }, false, cb_success);
            }
        }
        ,
        loadHtmlTable: function (resData, treeNode) {
            var tableSel = $("#my-table");
            tableSel.empty();
            var html = resData['html'] || "";
            HotUtil.tdImgs = JSON.parse(resData['tdImgs'] || '{}');
            //更新node节点的数据
            treeNode['HTML_DATA'] = html;
            treeNode['SAVE_DATA'] = resData['SAVE_DATA'] || "";
            treeNode['TABLE_STATUS'] = resData['TABLE_STATUS'] || "";
            treeNode['SECURITY'] = resData['SECURITY'];
            treeNode['FILE_PATH'] = resData['FILE_PATH'];
            treeNode['FILE_FORMAT'] = resData['FILE_FORMAT'];
            ztreeObj.updateNode(treeNode);
            initTbr(treeNode);
            HotUtil.workType = '';
            if (treeNode['SAVE_DATA']) {
                HotUtil.workType = JSON.parse(treeNode['SAVE_DATA']).workType || '';
            }
            $(".table-security").text(getSecurityName(treeNode['SECURITY']));
            if (HotUtil.isPdf(treeNode)) {
                tableSel.css({
                    "padding": "0px 0px",
                    "width": "100%"
                });
                tableSel.append('<iframe id="iframe"></iframe>');
                var ifHeight = tableSel.parent().height() - $("#my-tbr").height() - 5;
                if (treeNode['TYPE'].indexOf('table') > -1) {
                    ifHeight = ifHeight - 40;
                }
                $("#iframe").css({
                    "height": ifHeight + "px",
                    "border": "none",
                    "width": "100%"
                });
                $("#iframe").attr("src", "/File" + treeNode['FILE_PATH']);
            } else {
                if (html != "") {
                    //添加表格
                    // var $table = $(html.replaceAll("\n", "<br>"));
                    var $table = $(html);
                    // HotUtil.dealTableImg($table);
                    //设置表格的class 区别显示的表格 表示为存储数据的表格 因为他们的class都有 layui-table
                    $table.addClass('data-table').show();
                    tableSel.css({
                        "padding": "0px 15px",
                        "width": "calc(100% - 30px)"
                    });

                    if (resData['TABLE_HEADER'] !== 0 && resData['TABLE_HEADER'] !== undefined && resData['TABLE_HEADER'] !== "" && resData['TABLE_HEADER'] !== "0-0") {
                        //如果存在表头的话 固定表头显示
                        var $thead = $('<thead class="sticky-thead"></thead>');
                        var top = "-1px";
                        $thead.css({
                            "top": top,
                        });

                        var tableHeader = resData['TABLE_HEADER'];
                        //处理表头的显示
                        $table.find("tr").each(function (i, n) {
                            if (i <= (tableHeader - 1)) {
                                // $(n).css({
                                // 	"font-weight": "bold",
                                // 	"background-color": "#e6e6e6"
                                // });
                                $thead.append($(n).clone(true));
                                $(n).remove();
                            }
                        });
                        $table.prepend($thead);
                    }
                    var colWidths = JSON.parse(treeNode['SAVE_DATA']).colWidths || [];
                    if (colWidths.length > 0) {
                        var $colgroup = $('<colgroup></colgroup>');
                        for (var i = 0; i < colWidths.length; i++) {
                            $colgroup.append('<col width="' + colWidths[i] + '">');
                        }
                        $table.prepend($colgroup);
                    }
                    tableSel.append($table);
                    tableSel.find(".layui-table tbody tr:hover").css("background-color", "");
                    $table.find("tr").each(function (i, n) {
                        HotUtil.trContextMenu($(n), treeNode, tableSel);
                    });

                    if (treeNode['TABLE_STATUS'] === 'sign') {
                        $(".layui-table td").each(function (i, n) {
                            HotUtil.tdContextSignMenu(n, treeNode, tableSel, false);
                        });
                    }
                    $table.find("td").each(function (i, n) {
                        var comment = $(n).attr("comment");
                        if (comment) {
                            $(n).mouseover(function () {
                                layer.tips(comment, this);
                            });

                            $(n).mouseout(function () {
                                layer.closeAll();
                            });
                        }
                    });
                    HotUtil.tableAddLink(treeNode, $table);
                } else {
                    tableSel.append('<span style="color:red;padding: 10px;"> 请先编辑表格！</span>');
                }
            }
        }
        ,
        updateHeaderRow: function (treeNode) {
            var tpl = '<form class="layui-form" action="" lay-filter="header-form">\
						<div class="layui-form-item">\
							<div class="layui-inline">\
								<label class="layui-form-label">行数</label>\
									<div class="layui-input-inline" style="width: 180px;">\
										<input type="number" name="header" lay-verify="required|number" autocomplete="off" class="layui-input">\
									</div>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="submit-header" lay-submit="" lay-filter="submit-header">确认</button>\
								</div>\
							</div>\
						</div>\
					</form>';
            layer.open({
                title: '设置表格表头行',
                type: 1,
                fixed: false,
                maxmin: false,
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                shadeClose: false,
                resize: false, //不允许拉伸
                area: ['380px', '200px'],
                content: '<div id="headerContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                btn: ['确认', '取消'],
                yes: function () {
                    $('#submit-header').click();
                },
                btn2: function () {
                    return true;
                },
                success: function () {
                    $("#headerContent").append(tpl);
                }
            });
            form.render(null, 'header-form');
            form.on('submit(submit-header)', function (data) {
                if (/^\+?[0-9]+$/.test(data.field['header'])) {
                    //日志记录
                    var log = {};
                    log.operation = "设置表头行";
                    log.tablePid = treeNode['PID'];
                    log.tableId = treeNode['ID'];
                    var cb_success = function () {
                        log.reqResult = 1;
                        layer.closeAll();
                        reloadTable(treeNode);
                        addConfirmLog(log);
                    };
                    var cb_fail = function () {
                        log.reqResult = 0;
                        addConfirmLog(log);
                    };
                    var headerRow = data.field['header'];
                    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" +
                        "设置表头行为（" + headerRow + "）";
                    postAjax("table", 'UpdateTableHeader', {
                        id: treeNode['ID'],
                        header: headerRow
                    }, true, cb_success, cb_fail);
                } else {
                    layer.alert('请输入正整数！', {
                        icon: 2
                    })
                }
                return false;
            });
        }
        ,
        exportExcel: function (treeNode) {
            //日志记录
            var log = {};
            log.operation = "导出Excel";
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出了Excel文件";

            var loading;
            $.fileDownload(getReqUrl('table', 'ExportExcel'), {
                httpMethod: 'POST',
                data: {
                    "id": treeNode['ID']
                },
                prepareCallback: function (url) {
                    loading = layer.msg("正在导出...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                },
                abortCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出异常！！");
                },
                successCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                },
                failCallback: function (html, url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出失败！！");
                }
            });
        }
        ,
        exportPdf: function (treeNode) {
            //日志记录
            var log = {};
            log.operation = "导出PDF";
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出了PDF文件";
            var loading;
            $.fileDownload(getReqUrl('table', 'ExportPdf'), {
                httpMethod: 'POST',
                data: {
                    "id": treeNode['ID']
                },
                prepareCallback: function (url) {
                    loading = layer.msg("正在导出...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                },
                abortCallback: function (url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出异常！！");
                },
                successCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                },
                failCallback: function (html, url) {
                    var msg = '导出失败！表格中可能存在多余的合并单元格，请处理之后重试。';
                    log.reqResult = 0;
                    log.content = log.content + ",报错：" + msg;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.alert(msg, {
                        icon: 2
                    });
                }
            });
        }
        ,
        exportImg: function (treeNode) {
            //日志记录
            var log = {};
            log.operation = "下载所有照片";
            log.tablePid = treeNode['PID'];
            log.tableId = treeNode['ID'];
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上下载所有照片";

            var loading;
            $.fileDownload(getReqUrl('table', 'ExportImg'), {
                httpMethod: 'POST',
                data: {
                    "id": treeNode['ID']
                },
                prepareCallback: function (url) {
                    loading = layer.msg("正在导出...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                },
                abortCallback: function (url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出异常！！");
                },
                successCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                },
                failCallback: function (html, url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出失败！！");
                }
            });
        }
        ,
        importPdf: function (treeNode) {
            layer.confirm("导入Pdf会覆盖现有表格，是否继续？", {
                icon: 3,
                title: '提示'
            }, function (index) {
                layer.close(index);
                var fileFlag = false;
                layer.open({
                    title: "导入Pdf",
                    type: 1,
                    anim: false,
                    openDuration: 200,
                    isOutAnim: false,
                    closeDuration: 200,
                    shadeClose: false,
                    // fixed: false,
                    maxmin: false,
                    resize: false, //不允许拉伸
                    area: ['350px', '220px'],
                    content: '<div id="importPdfContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                    btn: ['确认', '取消'],
                    yes: function () {
                        if (!fileFlag) {
                            layer.alert('请选择需要导入的pdf文件!', {
                                icon: 2
                            });
                            return false;
                        }
                        $('#uploadStart').click();
                    },
                    btn2: function () {
                        layer.closeAll();
                    },
                    success: function () {
                        var addTpl = $("#uploadHtml")[0].innerHTML;
                        $("#importPdfContent").append(addTpl);
                        form.render(null, 'uploadForm');
                        var log = {};
                        log.operation = "导入Pdf";
                        log.tablePid = treeNode['PID'];
                        log.tableId = treeNode['ID'];

                        var uploadInst = upload.render({
                            elem: '#uploadChoice',
                            url: getReqUrl('table', 'ImportPdf', '&id=' + treeNode['ID'] + '&saveUser=' + sessionStorage.getItem("username")),
                            auto: false,
                            accept: 'file',
                            field: 'uploadFile',
                            exts: 'pdf|PDF',
                            bindAction: '#uploadStart',
                            dataType: "json",
                            choose: function (obj) {
                                fileFlag = true;
                                var o = obj.pushFile();
                                var filename = '';
                                for (var k in o) {
                                    var file = o[k];
                                    filename = file.name;
                                }
                                $("#selectedFile").show();
                                $("#selectedFileName").text(filename);
                                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导入了Pdf文件(" + filename + ")";
                            },
                            before: function () {
                                layer.load();
                            },
                            done: function (res) {
                                layer.closeAll();
                                if (res.success) {
                                    layer.msg("导入成功");
                                    reloadTable(treeNode);
                                    log.reqResult = 1;
                                } else {
                                    log.reqResult = 0;
                                    layer.alert(res.msg, {
                                        icon: 2
                                    });
                                }
                                addConfirmLog(log);
                            }
                        });
                    }
                });
            });
        }
        ,
        importExcel: function (treeNode) {
            function importExcelLayer(index) {
                if (index) {
                    layer.close(index);
                }
                var fileFlag = false;
                layer.open({
                    title: "导入Excel",
                    type: 1,
                    anim: false,
                    openDuration: 200,
                    isOutAnim: false,
                    closeDuration: 200,
                    shadeClose: false,
                    // fixed: false,
                    maxmin: false,
                    resize: false, //不允许拉伸
                    area: ['350px', '220px'],
                    content: '<div id="importExcelContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                    btn: ['确认', '取消'],
                    yes: function () {
                        if (!fileFlag) {
                            layer.alert('请选择需要导入的excel文件!', {
                                icon: 2
                            });
                            return false;
                        }
                        $('#uploadStart').click();
                    },
                    btn2: function () {
                        layer.closeAll();
                    },
                    success: function () {
                        var addTpl = $("#uploadHtml")[0].innerHTML;
                        $("#importExcelContent").append(addTpl);
                    }
                });
                form.render(null, 'uploadForm');

                var log = {};
                log.operation = "导入Excel";
                log.tablePid = treeNode['PID'];
                log.tableId = treeNode['ID'];

                var uploadInst = upload.render({
                    elem: '#uploadChoice',
                    url: getReqUrl('table', 'ImportExcel', '&id=' + treeNode['ID'] + '&saveUser=' + sessionStorage.getItem("username")),
                    auto: false,
                    accept: 'file',
                    field: 'uploadFile',
                    exts: 'xls|xlsx',
                    bindAction: '#uploadStart',
                    dataType: "json",
                    choose: function (obj) {
                        fileFlag = true;
                        var o = obj.pushFile();
                        var filename = '';
                        for (var k in o) {
                            var file = o[k];
                            filename = file.name;
                        }
                        $("#selectedFile").show();
                        $("#selectedFileName").text(filename);
                        log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导入了Excel文件(" + filename + ")";
                    },
                    before: function (obj) {
                        layer.load();
                    },
                    done: function (res) {
                        layer.closeAll();
                        if (res.success) {
                            layer.msg("导入成功");
                            reloadTable(treeNode);
                            log.reqResult = 1;
                        } else {
                            log.reqResult = 0;
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                        addConfirmLog(log);
                    }
                });
            }

            if (treeNode.HTML_DATA) {
                if (treeNode.HTML_DATA.indexOf('lock="true"') > -1) {
                    layer.alert('表格已有部分内容锁定，不可导入！', {
                        icon: 2
                    });
                } else {
                    layer.confirm("导入Excel会覆盖现有表格，并且已经上传的图片会被删除，是否继续？", {
                        icon: 3,
                        title: '提示'
                    }, function (index) {
                        importExcelLayer(index);
                    });
                }
            } else {
                importExcelLayer();
            }
        }
        ,
        clearLock: function (treeNode) {
            if (treeNode.TABLE_STATUS != 'sign') {
                layer.alert("表格还未确认，无需清除签名！");
                return false;
            }
            layer.confirm("清除签名之后不可恢复，您确定吗？", {
                icon: 3,
                title: '提示'
            }, function (index) {
                var log = {};
                log.operation = "清除签名";
                log.tablePid = treeNode['PID'];
                log.tableId = treeNode['ID'];
                log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上清除签名";
                var cb_success = function () {
                    log.reqResult = 1;
                    //更新node节点的数据
                    treeNode.TABLE_STATUS = 'edit';
                    ztreeObj.updateNode(treeNode);
                    reloadTable(treeNode);
                    layer.closeAll();
                    addConfirmLog(log);
                };
                var cb_fail = function () {
                    log.reqResult = 0;
                    addConfirmLog(log);
                };
                postAjax('table', "UnLockTable", {
                    id: treeNode['ID']
                }, true, cb_success, cb_fail);
            });
        }
        ,
        /**
         * 锁定整个表格 去除单行的锁定信息
         * @param {Object} treeNode
         */
        lockTable: function (treeNode) {
            if (treeNode.HTML_DATA) {
                if (treeNode.TABLE_STATUS == 'sign') {
                    layer.alert("表格已经锁定！");
                    return false;
                }
                layer.confirm("请确认表格数据无误，锁定之后不可再次编辑表格！", {
                    icon: 3,
                    title: '提示'
                }, function (index) {
                    var log = {};
                    log.operation = "锁定整表";
                    log.tablePid = treeNode['PID'];
                    log.tableId = treeNode['ID'];
                    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上锁定整表";

                    var cb_success = function () {
                        log.reqResult = 1;
                        //更新node节点的数据
                        treeNode.TABLE_STATUS = 'sign';
                        ztreeObj.updateNode(treeNode);
                        reloadTable(treeNode);
                        layer.closeAll();
                        addConfirmLog(log);
                    };
                    var cb_fail = function () {
                        log.reqResult = 0;
                        addConfirmLog(log);
                    };
                    postAjax('table', "LockTable", {
                        id: treeNode['ID']
                    }, true, cb_success, cb_fail);
                });
            } else {
                layer.alert("请先编辑表格");
            }
        }
        ,
        downloadFile: function (fileName, filePath, fileFormat = " ") {
            var url = getReqUrl("table", "DownloadFile", "&filePath=" + filePath
                + "&fileName=" + encodeURIComponent(fileName) + "&fileFormat=" + fileFormat);
            console.log(url);
            window.open(url);
        }
    }
;

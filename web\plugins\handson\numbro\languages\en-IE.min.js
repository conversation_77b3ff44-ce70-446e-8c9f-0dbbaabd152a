!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).enIE=e()}}(function(){return function i(f,u,d){function l(n,e){if(!u[n]){if(!f[n]){var r="function"==typeof require&&require;if(!e&&r)return r(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var t=u[n]={exports:{}};f[n][0].call(t.exports,function(e){return l(f[n][1][e]||e)},t,t.exports,i,f,u,d)}return u[n].exports}for(var s="function"==typeof require&&require,e=0;e<d.length;e++)l(d[e]);return l}({1:[function(e,n,r){"use strict";n.exports={languageTag:"en-IE",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var n=e%10;return 1==~~(e%100/10)?"th":1===n?"st":2===n?"nd":3===n?"rd":"th"},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=en-IE.min.js.map

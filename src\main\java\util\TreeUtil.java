package util;

import static util.PdfUtil.getTableName;
import static util.TableUtil.dealObjNull;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.util.*;
import cn.hutool.db.Entity;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelWriter;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;
import org.apache.poi.ss.usermodel.CellStyle;

public class TreeUtil {

	/**
	 * 查询该节点下是否存在子节点
	 */
	public static JSONArray dealNodesIsParent(JSONArray nodes) {
		for (int i = 0; i < nodes.size(); i++) {
			JSONObject node = nodes.getJSONObject(i);
			String id = node.getStr("ID");
			String countSql = "select count(*) count from LAUNCH_CONFIRM where PID='" + id + "'";
			int count = SqliteUtil.executeQuery(countSql).getJSONObject(0).getInt("count");
			if (count > 0) {
				node.set("ISPARENT", true);
			} else {
				node.set("ISPARENT", false);
			}
		}
		return nodes;
	}

	/**
	 * 获取最新的结构树id
	 */
	public static String getNewTreeId() {
		String sql = "select max(ID)+1 ID from LAUNCH_CONFIRM";
		JSONArray nodes = SqliteUtil.executeQuery(sql);
		if (!nodes.isEmpty()) {
			return nodes.getJSONObject(0).getStr("ID");
		} else {
			return "1";
		}
	}

	/**
	 * 获取该节点下的最后一个排序
	 */
	public static int getLastSort(String pid) {
		String sql = "select max(SORT) + 1 SORT " +
				"from LAUNCH_CONFIRM " +
				"where PID = '" + pid + "'";
		JSONArray nodes = SqliteUtil.executeQuery(sql);
		if (!nodes.isEmpty()) {
			return nodes.getJSONObject(0).getInt("SORT", 1);
		} else {
			return 1;
		}
	}

	public static String getQueryAllChildSql(String id) {
		return getQueryAllChildSql(id, "");
	}

	/**
	 * 获取所有子节点的ID
	 *
	 * @param id
	 * @return
	 */
	public static String getQueryAllChildIdSql(String id) {
		return getQueryAllChildFieldSql(id, "ID");
	}

	public static String getQueryAllChildFieldSql(String id, String field) {
		String sql = getUnionChildSql(id) +
				"SELECT " + field + " " +
				"FROM SubTree  ";
		return sql;
	}

	public static String getUnionChildSql(String id) {
		String sql = "WITH RECURSIVE SubTree AS (SELECT " + getFieldSql("self") +
				"                           FROM LAUNCH_CONFIRM self" +
				"                           WHERE ID = " + id + " " +
				"                           UNION ALL " +
				"                           SELECT " + getFieldSql("child") +
				"                           FROM LAUNCH_CONFIRM child " +
				"                                    INNER JOIN SubTree parent ON parent.ID = child.PID) ";
		return sql;
	}

	/**
	 * 获取所有节点的查询sql 包含所有父级节点的信息
	 *
	 * @return
	 */
	public static String getTreeAllView(String viewName) {
		String sql = "WITH RECURSIVE " + viewName + " AS (SELECT " + getFieldSql("self") + ", " +
				" NULL AS FOLDER_ID, " +
				" NULL AS FOLDER_NAME, " +
				" NULL AS FOLDER_SORT, " +
				" NULL AS MODEL_ID, " +
				" NULL AS MODEL_NAME, " +
				" NULL AS MODEL_SORT, " +
				" NULL AS PROJECT_ID, " +
				" NULL AS PROJECT_NAME, " +
				" NULL AS PROJECT_SORT, " +
				" NULL AS A_ID, " +
				" NULL AS A_NAME, " +
				" NULL AS A_NUM, " +
				" NULL AS A_SORT, " +
				" NULL AS B_ID, " +
				" NULL AS B_NAME, " +
				" NULL AS B_SORT" +
				" FROM LAUNCH_CONFIRM self" +
				" WHERE TYPE = 'root'" +
				" UNION ALL " +
				"  SELECT " + getFieldSql("lc") + "," +
				"         CASE WHEN lc.TYPE = 'folder' THEN lc.ID ELSE  " + viewName + ".FOLDER_ID END       AS FOLDER_ID, " +
				"         CASE WHEN lc.TYPE = 'folder' THEN lc.NAME ELSE  " + viewName + ".FOLDER_NAME END   AS FOLDER_NAME, " +
				"         CASE WHEN lc.TYPE = 'folder' THEN lc.SORT ELSE  " + viewName + ".FOLDER_SORT END   AS FOLDER_SORT, " +
				"         CASE WHEN lc.TYPE = 'model' THEN lc.ID ELSE  " + viewName + ".MODEL_ID END         AS MODEL_ID, " +
				"         CASE WHEN lc.TYPE = 'model' THEN lc.NAME ELSE  " + viewName + ".MODEL_NAME END     AS MODEL_NAME, " +
				"         CASE WHEN lc.TYPE = 'model' THEN lc.SORT ELSE  " + viewName + ".MODEL_SORT END     AS MODEL_SORT, " +
				"         CASE WHEN lc.TYPE = 'project' THEN lc.ID ELSE  " + viewName + ".PROJECT_ID END     AS PROJECT_ID, " +
				"         CASE WHEN lc.TYPE = 'project' THEN lc.NAME ELSE  " + viewName + ".PROJECT_NAME END AS PROJECT_NAME, " +
				"         CASE WHEN lc.TYPE = 'project' THEN lc.SORT ELSE  " + viewName + ".PROJECT_SORT END AS PROJECT_SORT, " +
				"         CASE WHEN lc.TYPE = 'a' THEN lc.ID ELSE  " + viewName + ".A_ID END                 AS A_ID, " +
				"         CASE WHEN lc.TYPE = 'a' THEN lc.NAME ELSE  " + viewName + ".A_NAME END             AS A_NAME, " +
				"         CASE WHEN lc.TYPE = 'a' THEN lc.TABLE_NUM ELSE  " + viewName + ".A_NUM END        AS A_NUM, " +
				"         CASE WHEN lc.TYPE = 'a' THEN lc.SORT ELSE  " + viewName + ".A_SORT END             AS A_SORT, " +
				"         CASE WHEN lc.TYPE = 'b' THEN lc.ID ELSE  " + viewName + ".B_ID END                 AS B_ID, " +
				"         CASE WHEN lc.TYPE = 'b' THEN lc.NAME ELSE  " + viewName + ".B_NAME END             AS B_NAME, " +
				"         CASE WHEN lc.TYPE = 'b' THEN lc.SORT ELSE  " + viewName + ".B_SORT END             AS B_SORT " +
				"  FROM LAUNCH_CONFIRM lc " +
				"           JOIN " + viewName + " ON lc.PID =  " + viewName + ".ID) ";
		return sql;
	}

	public static String getUnionParentSql(String id) {
		String sql = "WITH RECURSIVE ParentTree AS (SELECT " + getFieldSql("self") +
				"                           FROM LAUNCH_CONFIRM self" +
				"                           WHERE ID = " + id + " " +
				"                           UNION ALL " +
				"                           SELECT " + getFieldSql("parent") +
				"                           FROM LAUNCH_CONFIRM parent " +
				"                                    INNER JOIN ParentTree child ON parent.ID = child.PID) ";
		return sql;
	}

	/**
	 * 获取该节点以及下面的所有子节点的查询sql
	 *
	 * @param id
	 * @return
	 */
	public static String getQueryAllChildSql(String id, String whereSql) {
		String sql = getUnionChildSql(id) +
				"SELECT * " + getSecuritySql() +
				"FROM SubTree  " + whereSql +
				"order by id ";
		return sql;
	}

	/**
	 * 获取密级查询sql
	 *
	 * @return
	 */

	public static String getSecuritySql() {
		return "       ,CASE SECURITY " +
				"           WHEN 0 THEN '公开' " +
				"           WHEN 1 THEN '内部' " +
				"           WHEN 2 THEN '秘密' " +
				"           WHEN 3 THEN '机密' " +
				"           ELSE '未知' " +
				"           END AS SECURITY_NAME ";
	}

	/**
	 * 获取字段查询sql
	 *
	 * @param tableAlias
	 * @return
	 */
	public static String getFieldSql(String tableAlias) {
		return tableAlias + ".ID, " +
				tableAlias + ".PID, " +
				tableAlias + ".NAME, " +
				tableAlias + ".TABLE_NUM, " +
				tableAlias + ".TYPE, " +
				tableAlias + ".LEVEL_NUM, " +
				tableAlias + ".FILE_NAME, " +
				tableAlias + ".FILE_PATH, " +
				tableAlias + ".FILE_FORMAT, " +
				tableAlias + ".CREATOR, " +
				tableAlias + ".CREATE_TIME, " +
				tableAlias + ".SORT, " +
				tableAlias + ".SAVE_DATA, " +
				tableAlias + ".HTML_DATA, " +
				tableAlias + ".SAVE_TIME, " +
				tableAlias + ".SAVE_USER, " +
				tableAlias + ".TABLE_STATUS, " +
				tableAlias + ".TABLE_HEADER, " +
				tableAlias + ".COPY_ID, " +
				tableAlias + ".CURRENT_EDITOR, " +
				tableAlias + ".EDIT_TIME, " +
				tableAlias + ".SECURITY ";
	}

	/**
	 * 获取基础字段查询sql（用于轻量级查询，避免加载大字段）
	 *
	 * @param tableAlias
	 * @return
	 */
	public static String getBasicFieldSql(String tableAlias) {
		return tableAlias + ".ID, " +
				tableAlias + ".PID, " +
				tableAlias + ".NAME, " +
				tableAlias + ".TABLE_NUM, " +
				tableAlias + ".TYPE, " +
				tableAlias + ".LEVEL_NUM, " +
				tableAlias + ".FILE_NAME, " +
				tableAlias + ".FILE_PATH, " +
				tableAlias + ".FILE_FORMAT, " +
				tableAlias + ".CREATOR, " +
				tableAlias + ".CREATE_TIME, " +
				tableAlias + ".SORT, " +
				tableAlias + ".SAVE_TIME, " +
				tableAlias + ".SAVE_USER, " +
				tableAlias + ".TABLE_STATUS, " +
				tableAlias + ".TABLE_HEADER, " +
				tableAlias + ".COPY_ID, " +
				tableAlias + ".CURRENT_EDITOR, " +
				tableAlias + ".EDIT_TIME, " +
				tableAlias + ".SECURITY ";
	}

	/**
	 * 获取PDF导出用字段查询sql（包含表格内容但优化内存使用）
	 *
	 * @param tableAlias
	 * @return
	 */
	public static String getPdfFieldSql(String tableAlias) {
		return tableAlias + ".ID, " +
				tableAlias + ".PID, " +
				tableAlias + ".NAME, " +
				tableAlias + ".TABLE_NUM, " +
				tableAlias + ".TYPE, " +
				tableAlias + ".LEVEL_NUM, " +
				tableAlias + ".FILE_NAME, " +
				tableAlias + ".FILE_PATH, " +
				tableAlias + ".FILE_FORMAT, " +
				tableAlias + ".CREATOR, " +
				tableAlias + ".CREATE_TIME, " +
				tableAlias + ".SORT, " +
				tableAlias + ".SAVE_DATA, " +
				tableAlias + ".HTML_DATA, " +
				tableAlias + ".SAVE_TIME, " +
				tableAlias + ".SAVE_USER, " +
				tableAlias + ".TABLE_STATUS, " +
				tableAlias + ".TABLE_HEADER, " +
				tableAlias + ".COPY_ID, " +
				tableAlias + ".CURRENT_EDITOR, " +
				tableAlias + ".EDIT_TIME, " +
				tableAlias + ".SECURITY ";
	}

	public static String getQueryAllParentSql(String id) {

		return getQueryAllParentSql(id, "", "");
	}

	/**
	 * 获取基础字段的子节点查询sql（轻量级查询，不包含大字段）
	 *
	 * @param id
	 * @return
	 */
	public static String getQueryAllChildBasicFieldsSql(String id) {
		String sql = getUnionChildSql(id) +
				"SELECT " + getBasicFieldSql("SubTree") + getSecuritySql() +
				" FROM SubTree " +
				" ORDER BY ID ";
		return sql;
	}

	/**
	 * 获取PDF导出用的子节点查询sql（包含表格内容但进行内存优化）
	 *
	 * @param id
	 * @return
	 */
	public static String getQueryAllChildForPdfSql(String id) {
		String sql = getUnionChildSql(id) +
				"SELECT " + getPdfFieldSql("SubTree") + getSecuritySql() +
				" FROM SubTree " +
				" ORDER BY ID ";
		return sql;
	}

	/**
	 * 获取该节点以及上面的所有父节点的查询sql
	 *
	 * @param id
	 * @return
	 */
	public static String getQueryAllParentSql(String id, String whereSql, String orderBySql) {
		String sql = getUnionParentSql(id) +
				"SELECT * " + TreeUtil.getSecuritySql() +
				"FROM ParentTree " + whereSql +
				"  " + orderBySql;
		return sql;
	}

	/**
	 * 获取该节点的型号id
	 */
	public static String getModelId(String tableId) {
		String modelSql = TreeUtil.getQueryAllParentSql(tableId, "where TYPE = 'model'", "");
		String modelId = "";
		JSONArray models = SqliteUtil.executeQuery(modelSql);
		if (!models.isEmpty()) {
			modelId = models.getJSONObject(0).getStr("ID");
		}
		return modelId;
	}

	// 索引创建状态标记
	private static boolean indexesCreated = false;
	private static final String INDEX_STATUS_TABLE = "SYSTEM_INDEX_STATUS";

	/**
	 * 检查索引是否已创建
	 */
	public static boolean checkIndexesCreated() {
		try {
			// 检查索引状态表是否存在
			String checkTableSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + INDEX_STATUS_TABLE + "'";
			JSONArray result = SqliteUtil.executeQuery(checkTableSql);
			
			if (result.isEmpty()) {
				// 表不存在，说明索引未创建
				return false;
			}
			
			// 检查索引创建状态
			String checkStatusSql = "SELECT status FROM " + INDEX_STATUS_TABLE + " WHERE index_type='performance_indexes' LIMIT 1";
			JSONArray statusResult = SqliteUtil.executeQuery(checkStatusSql);
			
			if (!statusResult.isEmpty()) {
				String status = statusResult.getJSONObject(0).getStr("status");
				return "created".equals(status);
			}
			
			return false;
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - 检查索引状态时发生错误: " + e.getMessage());
			return false;
		}
	}

	/**
	 * 标记索引已创建
	 */
	private static void markIndexesCreated() {
		try {
			// 创建索引状态表
			String createTableSql = "CREATE TABLE IF NOT EXISTS " + INDEX_STATUS_TABLE + " (" +
					"index_type TEXT PRIMARY KEY, " +
					"status TEXT, " +
					"create_time TEXT" +
					")";
			SqliteUtil.executeCommand(createTableSql);
			
			// 插入或更新索引状态
			String insertSql = "INSERT OR REPLACE INTO " + INDEX_STATUS_TABLE + 
					" (index_type, status, create_time) VALUES ('performance_indexes', 'created', '" + DateUtil.now() + "')";
			SqliteUtil.executeCommand(insertSql);
			
			indexesCreated = true;
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - 标记索引状态时发生错误: " + e.getMessage());
		}
	}

	/**
	 * 内存使用情况监控和优化建议
	 * 在关键操作前后调用，提供内存使用信息和优化建议
	 *
	 * @param operation 当前操作名称
	 */
	public static void logMemoryUsage(String operation) {
		Runtime runtime = Runtime.getRuntime();
		long totalMemory = runtime.totalMemory();
		long freeMemory = runtime.freeMemory();
		long usedMemory = totalMemory - freeMemory;
		long maxMemory = runtime.maxMemory();

		double usedPercent = (double) usedMemory / maxMemory * 100;

		System.out.println(DateUtil.now() + " - [内存监控] " + operation + " - " +
			"已用内存: " + (usedMemory / 1024 / 1024) + "MB/" + (maxMemory / 1024 / 1024) + "MB (" +
			String.format("%.1f", usedPercent) + "%)");

		// 如果内存使用超过80%，建议进行GC
		if (usedPercent > 80) {
			System.out.println(DateUtil.now() + " - [内存警告] 内存使用率过高 (" + String.format("%.1f", usedPercent) + "%)，建议进行垃圾回收");
			System.gc();

			// GC后再次检查
			try {
				Thread.sleep(100); // 稍等片刻让GC完成
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}

			totalMemory = runtime.totalMemory();
			freeMemory = runtime.freeMemory();
			usedMemory = totalMemory - freeMemory;
			usedPercent = (double) usedMemory / maxMemory * 100;

			System.out.println(DateUtil.now() + " - [内存监控] GC后 - " +
				"已用内存: " + (usedMemory / 1024 / 1024) + "MB/" + (maxMemory / 1024 / 1024) + "MB (" +
				String.format("%.1f", usedPercent) + "%)");
		}
	}

	/**
	 * 检查可用内存是否足够执行操作
	 *
	 * @param operation 操作名称
	 * @param minFreeMemoryMB 最小需要的可用内存（MB）
	 * @throws OutOfMemoryError 如果可用内存不足
	 */
	public static void checkMemoryAvailable(String operation, long minFreeMemoryMB) {
		Runtime runtime = Runtime.getRuntime();
		long maxMemory = runtime.maxMemory();
		long totalMemory = runtime.totalMemory();
		long freeMemory = runtime.freeMemory();
		long availableMemory = maxMemory - totalMemory + freeMemory;
		long minFreeMemoryBytes = minFreeMemoryMB * 1024 * 1024;

		if (availableMemory < minFreeMemoryBytes) {
			String errorMsg = operation + " 需要至少 " + minFreeMemoryMB + "MB 可用内存，" +
				"但当前只有 " + (availableMemory / 1024 / 1024) + "MB 可用内存";
			System.out.println(DateUtil.now() + " - [内存检查] " + errorMsg);
			throw new OutOfMemoryError(errorMsg);
		}

		System.out.println(DateUtil.now() + " - [内存检查] " + operation + " 内存检查通过，" +
			"可用内存: " + (availableMemory / 1024 / 1024) + "MB");
	}

	/**
	 * 创建性能优化索引（自动检查避免重复执行）
	 */
	public static boolean createPerformanceIndexes() {
		// 如果内存中已标记为已创建，直接返回
		if (indexesCreated) {
			System.out.println(DateUtil.now() + " - 性能索引已创建（内存缓存）");
			return true;
		}
		
		// 检查数据库中的索引状态
		if (checkIndexesCreated()) {
			indexesCreated = true;
			System.out.println(DateUtil.now() + " - 性能索引已创建（数据库检查）");
			return true;
		}
		
		try {
			System.out.println(DateUtil.now() + " - 开始创建性能优化索引...");
			
			// 为LAUNCH_CONFIRM表创建关键索引
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_launch_confirm_id ON LAUNCH_CONFIRM(ID)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_launch_confirm_pid ON LAUNCH_CONFIRM(PID)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_launch_confirm_type ON LAUNCH_CONFIRM(TYPE)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_launch_confirm_pid_type ON LAUNCH_CONFIRM(PID, TYPE)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_launch_confirm_level_sort ON LAUNCH_CONFIRM(LEVEL_NUM, SORT)");
			
			// 为CONFIRM_DOWNLOAD表创建关键索引
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_confirm_download_creator ON CONFIRM_DOWNLOAD(CREATOR)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_confirm_download_table_id ON CONFIRM_DOWNLOAD(TABLE_ID)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_confirm_download_start_time ON CONFIRM_DOWNLOAD(START_TIME DESC)");
			SqliteUtil.executeCommand("CREATE INDEX IF NOT EXISTS idx_confirm_download_creator_start_time ON CONFIRM_DOWNLOAD(CREATOR, START_TIME DESC)");
			
			// 标记索引已创建
			markIndexesCreated();
			
			System.out.println(DateUtil.now() + " - 性能优化索引创建完成");
			return true;
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - 创建索引时发生错误: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 自动初始化性能索引（在系统启动时调用）
	 */
	public static void autoInitPerformanceIndexes() {
		try {
			createPerformanceIndexes();
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - 自动初始化索引时发生错误: " + e.getMessage());
		}
	}

	/**
	 * 添加一个节点
	 *
	 * @param params
	 * @return
	 */
	public static String AddTableNode(JSONObject params) {
		String pid = params.getStr("pid");
		String name = params.getStr("name");
		String tableNum = params.getStr("tableNum");
		String type = params.getStr("type");
		String creator = params.getStr("creator");
		String level = params.getStr("level");
		String security = params.getStr("security", "1");

		String nextId = TreeUtil.getNewTreeId();
		int sort = TreeUtil.getLastSort(pid);
		String nowTime = DateUtil.now();

		Entity entity = Entity.create("LAUNCH_CONFIRM")
				.set("ID", nextId)
				.set("PID", pid)
				.set("NAME", name)
				.set("TABLE_NUM", tableNum)
				.set("TYPE", type)
				.set("LEVEL_NUM", level)
				.set("CREATOR", creator)
				.set("CREATE_TIME", nowTime)
				.set("SORT", sort)
				.set("TABLE_STATUS", "edit")
				.set("SECURITY", security);
		SqliteUtil.executeInsertTransaction(entity);
		
		// 清除缓存，因为树结构发生了变化
		clearSearchDropdownCache();
		
		return nextId;
	}

	/**
	 * 构建树
	 *
	 * @param id
	 * @param pid
	 * @return
	 */
	public static JSONArray buildTree(String id, String pid) {
		JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildSql(id));
		List<Map> nodeList = JSONUtil.toList(nodes, Map.class);
		NodeParser<Map, String> nodeParser = (object, treeNode) -> {
			treeNode.setId(Convert.toStr(object.get("ID")));
			treeNode.setParentId(Convert.toStr(object.get("PID")));
			treeNode.setName(Convert.toStr(object.get("NAME")));
			treeNode.setWeight(Convert.toInt(object.get("SORT"), 0));
			treeNode.putExtra("data", object);
		};
		List<Tree<String>> treeNodes = cn.hutool.core.lang.tree.TreeUtil.build(nodeList, pid, nodeParser);
		JSONArray array = JSONUtil.parseArray(treeNodes);
		return array;
	}

	/**
	 * 构建PDF导出用的优化树（包含必要的表格数据字段，但进行内存优化处理）
	 * 相比完整查询减少内存使用，相比轻量级查询包含表格内容
	 *
	 * @param id
	 * @param pid
	 * @return
	 */
	public static JSONArray buildTreeForPdf(String id, String pid) {
		System.out.println(DateUtil.now() + " - [PDF优化] 开始PDF导出用树形结构构建，节点ID: " + id);
		long startTime = System.currentTimeMillis();

		// 检查内存是否足够（要求至少100MB可用内存）
		checkMemoryAvailable("PDF导出树形结构构建", 100);
		logMemoryUsage("构建开始前");

		// 使用PDF优化查询，包含表格内容但避免过度内存使用
		JSONArray nodes = SqliteUtil.executeQuery(getQueryAllChildForPdfSql(id));
		System.out.println(DateUtil.now() + " - [PDF优化] 查询到节点数量: " + nodes.size());
		logMemoryUsage("数据查询后");

		// 转换为Map列表进行树形结构构建
		List<Map> nodeList = JSONUtil.toList(nodes, Map.class);
		logMemoryUsage("Map转换后");

		NodeParser<Map, String> nodeParser = (object, treeNode) -> {
			treeNode.setId(Convert.toStr(object.get("ID")));
			treeNode.setParentId(Convert.toStr(object.get("PID")));
			treeNode.setName(Convert.toStr(object.get("NAME")));
			treeNode.setWeight(Convert.toInt(object.get("SORT"), 0));
			treeNode.putExtra("data", object);
		};

		List<Tree<String>> treeNodes = cn.hutool.core.lang.tree.TreeUtil.build(nodeList, pid, nodeParser);
		JSONArray array = JSONUtil.parseArray(treeNodes);
		logMemoryUsage("树形结构构建后");

		// 清理中间对象，促进GC回收
		nodeList.clear();
		nodeList = null;
		treeNodes.clear();
		treeNodes = null;

		long endTime = System.currentTimeMillis();
		System.out.println(DateUtil.now() + " - [PDF优化] PDF导出用树形结构构建完成，耗时: " + (endTime - startTime) + "ms");

		// 触发GC建议
		System.gc();
		logMemoryUsage("构建完成后");

		return array;
	}

	/**
	 * 构建轻量级树（仅用于树形结构展示，不包含表格内容）
	 * 只查询基本字段，避免加载HTML_DATA等大字段导致内存溢出
	 *
	 * @param id
	 * @param pid
	 * @return
	 */
	public static JSONArray buildTreeLightWeight(String id, String pid) {
		System.out.println(DateUtil.now() + " - [内存优化] 开始轻量级树形结构构建，节点ID: " + id);
		long startTime = System.currentTimeMillis();

		// 检查内存是否足够（要求至少50MB可用内存）
		checkMemoryAvailable("轻量级树形结构构建", 50);
		logMemoryUsage("构建开始前");

		// 使用轻量级查询，只获取树形结构必需的字段
		JSONArray nodes = SqliteUtil.executeQuery(getQueryAllChildBasicFieldsSql(id));
		System.out.println(DateUtil.now() + " - [内存优化] 轻量级查询完成，节点数量: " + nodes.size());
		logMemoryUsage("数据查询后");

		// 转换为Map列表
		List<Map> nodeList = JSONUtil.toList(nodes, Map.class);
		logMemoryUsage("Map转换后");

		NodeParser<Map, String> nodeParser = (object, treeNode) -> {
			treeNode.setId(Convert.toStr(object.get("ID")));
			treeNode.setParentId(Convert.toStr(object.get("PID")));
			treeNode.setName(Convert.toStr(object.get("NAME")));
			treeNode.setWeight(Convert.toInt(object.get("SORT"), 0));
			treeNode.putExtra("data", object);
		};

		List<Tree<String>> treeNodes = cn.hutool.core.lang.tree.TreeUtil.build(nodeList, pid, nodeParser);
		JSONArray array = JSONUtil.parseArray(treeNodes);
		logMemoryUsage("树形结构构建后");

		// 清理中间对象，促进GC回收
		nodeList.clear();
		nodeList = null;
		treeNodes.clear();
		treeNodes = null;

		long endTime = System.currentTimeMillis();
		System.out.println(DateUtil.now() + " - [内存优化] 轻量级树形结构构建完成，耗时: " + (endTime - startTime) + "ms");

		// 触发GC建议
		System.gc();
		logMemoryUsage("构建完成后");

		return array;
	}

	/**
	 * 从JSON数组中获取所有文件路径和图片地址的列表。
	 *
	 * @param data 包含HTML数据和文件路径的JSON数组。
	 * @return 所有需要复制的文件路径和图片地址的列表。
	 */
	public static List<String> getCopyFiles(JSONArray data) {
		List<String> copyFiles = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			JSONObject node = data.getJSONObject(i);
			String htmlData = node.getStr("HTML_DATA");
			String filePath = node.getStr("FILE_PATH", "");
			// 处理文件路径
			if (StrUtil.isNotEmpty(filePath)) {
				if (filePath.contains("//")) {
					copyFiles.add(filePath);
				}
			}
			// 查找HTML中的图片
			JSONArray images = PdfUtil.html2Images(htmlData);
			for (int j = 0; j < images.size(); j++) {
				JSONObject imgObj = images.getJSONObject(j);
				JSONArray imgArr = imgObj.getJSONArray("img");
				// 处理图片地址
				if (ObjectUtil.isNotNull(imgArr)) {
					for (int k = 0; k < imgArr.size(); k++) {
						String src = imgArr.getJSONObject(k).getStr("src");
						String newSrc = StrUtil.replaceIgnoreCase(src, "File", "");
						copyFiles.add(newSrc);
					}
				}
			}
		}
		return copyFiles;
	}

	/**
	 * 查询日志
	 *
	 * @param id
	 * @return
	 */
	public static JSONArray queryLogByID(String id) {
		String sql = "select * from CONFIRM_LOG where TABLE_ID in (" + getQueryAllChildIdSql(id) + ")";
		return SqliteUtil.executeQuery(sql);
	}

	/**
	 * 查询日志 将日期转换为时间戳
	 *
	 * @param id
	 * @return
	 */
	public static JSONArray queryLogForTimeByID(String id) {
		JSONArray objects = queryLogByID(id);
		for (int i = 0; i < objects.size(); i++) {
			JSONObject object = objects.getJSONObject(i);
			String logTime = object.getStr("LOG_TIME", "");
			if (StrUtil.contains(logTime, "-")) {
				object.set("LOG_TIME", DateUtil.parseDateTime(object.getStr("LOG_TIME")).getTime());
			}
		}
		return objects;
	}

	/**
	 * 查询签名
	 *
	 * @param id
	 * @return
	 */
	public static JSONArray querySignByID(String id) {
		String sql = "select * from LAUNCH_SIGN where LAUNCH_ID in (" + getQueryAllChildIdSql(id) + ")";
		return SqliteUtil.executeQuery(sql);
	}

	/**
	 * 查询图片
	 *
	 * @param id
	 * @return
	 */
	public static JSONArray queryPhotoByID(String id) {
		String sql = "select * from LAUNCH_PHOTO where LAUNCH_ID in (" + getQueryAllChildIdSql(id) + ")";
		return SqliteUtil.executeQuery(sql);
	}

	/**
	 * 导出数据包
	 *
	 * @param id 节点ID
	 */
	public static File exportZip(String id) throws Exception {
		System.out.println(DateUtil.now() + " - [开始] 导出数据包，节点ID: " + id);
		long startTime = System.currentTimeMillis();
		// 触发垃圾回收释放文件句柄
		System.out.println(DateUtil.now() + " - [系统] 开始触发垃圾回收以释放文件句柄");
		System.gc();
		System.out.println(DateUtil.now() + " - [系统] 垃圾回收执行完毕");
		// 获取临时目录路径
		String fileTempPath = Util.getFileTempPath();
		System.out.println(DateUtil.now() + " - [目录] 临时文件目录: " + fileTempPath);
		// 清空临时目录
		System.out.println(DateUtil.now() + " - [目录] 开始清空临时目录");
		FileUtil.clean(fileTempPath);
		System.out.println(DateUtil.now() + " - [目录] 临时目录清空完成");
		// 查询节点数据
		System.out.println(DateUtil.now() + " - [数据] 开始查询节点及子节点数据，节点ID: " + id);
		JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildSql(id));
		System.out.println(DateUtil.now() + " - [数据] 查询结果: " + nodes.size() + "条节点记录");
		// 如果没有节点数据，记录警告并继续
		if (nodes.isEmpty()) {
			System.out.println(DateUtil.now() + " - [警告] 未找到任何节点数据，ID可能不存在: " + id);
		} else {
			System.out.println(DateUtil.now() + " - [数据] 根节点信息: " + nodes.getJSONObject(0).toString());
		}
		// 获取上传路径
		String fileUploadPath = Util.getFileUploadPath();
		System.out.println(DateUtil.now() + " - [路径] 文件上传目录: " + fileUploadPath);
		System.out.println(DateUtil.now() + " - [路径] 临时文件目录: " + fileTempPath);
		// 获取需要复制的文件列表
		System.out.println(DateUtil.now() + " - [文件] 开始分析需要复制的文件列表");
		List<String> copyFiles = getCopyFiles(nodes);
		System.out.println(DateUtil.now() + " - [文件] 需要复制的文件数量: " + copyFiles.size() + "个");
		// 创建data.json文件
		String dataPath = fileTempPath + "data.json";
		System.out.println(DateUtil.now() + " - [数据] 开始写入节点数据到文件: " + dataPath);
		try (BufferedWriter writer = FileUtil.getWriter(dataPath, StandardCharsets.UTF_8, false)) {
			nodes.write(writer);
			File dataFile = new File(dataPath);
			System.out.println(DateUtil.now() + " - [数据] 节点数据写入完成，文件大小: " + dataFile.length() + " 字节");
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - [错误] 写入节点数据失败: " + e.getMessage());
			throw e;
		}
		// 获取并写入日志数据
		System.out.println(DateUtil.now() + " - [日志] 开始查询关联的日志数据");
		JSONArray logData = queryLogForTimeByID(id);
		String logPath = fileTempPath + "log.json";
		System.out.println(DateUtil.now() + " - [日志] 查询到" + logData.size() + "条日志记录，开始写入: " + logPath);
		try (BufferedWriter writer = FileUtil.getWriter(logPath, StandardCharsets.UTF_8, false)) {
			logData.write(writer);
			File logFile = new File(logPath);
			System.out.println(DateUtil.now() + " - [日志] 日志数据写入完成，文件大小: " + logFile.length() + " 字节");
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - [错误] 写入日志数据失败: " + e.getMessage());
			throw e;
		}
		// 获取并写入签名数据
		System.out.println(DateUtil.now() + " - [签名] 开始查询关联的签名数据");
		JSONArray signData = querySignByID(id);
		String signPath = fileTempPath + "sign.json";
		System.out.println(DateUtil.now() + " - [签名] 查询到" + signData.size() + "条签名记录，开始写入: " + signPath);
		try (BufferedWriter writer = FileUtil.getWriter(signPath, StandardCharsets.UTF_8, false)) {
			signData.write(writer);
			File signFile = new File(signPath);
			System.out.println(DateUtil.now() + " - [签名] 签名数据写入完成，文件大小: " + signFile.length() + " 字节");
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - [错误] 写入签名数据失败: " + e.getMessage());
			throw e;
		}
		// 获取并写入图片数据
		System.out.println(DateUtil.now() + " - [图片] 开始查询关联的图片数据");
		JSONArray photoData = queryPhotoByID(id);
		String photoPath = fileTempPath + "photo.json";
		System.out.println(DateUtil.now() + " - [图片] 查询到" + photoData.size() + "条图片记录，开始写入: " + photoPath);
		try (BufferedWriter writer = FileUtil.getWriter(photoPath, StandardCharsets.UTF_8, false)) {
			photoData.write(writer);
			File photoFile = new File(photoPath);
			System.out.println(DateUtil.now() + " - [图片] 图片数据写入完成，文件大小: " + photoFile.length() + " 字节");
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - [错误] 写入图片数据失败: " + e.getMessage());
			throw e;
		}
		// 复制图片文件
		System.out.println(DateUtil.now() + " - [复制] 开始复制图片文件，总数: " + copyFiles.size());
		int successCount = 0;
		int failCount = 0;
		long totalBytes = 0;
		// 遍历并复制文件
		for (int i = 0; i < copyFiles.size(); i++) {
			String src = copyFiles.get(i);
			String srcFilepath = fileUploadPath + src;
			String descFilepath = fileTempPath + "photo" + src;
			int retry = 0;
			boolean copied = false;
			while (retry < 3 && !copied) {
				try {
					File srcFile = new File(srcFilepath);
					// 检查源文件是否存在
					if (!srcFile.exists()) {
						System.out.println(DateUtil.now() + " - [警告] 源文件不存在: " + srcFilepath);
						break;
					}
					// 文件可用性检查
					if (!isFileAvailable(srcFile)) {
						// 只在文件被占用时输出日志
						System.out.println(DateUtil.now() + " - [重试] 文件被占用，等待重试 (" + (retry + 1) + "/3)... " + srcFilepath);
						Thread.sleep(1000);
						retry++;
						continue;
					}
					// 获取源文件大小
					long fileSize = srcFile.length();
					// 使用NIO的Files.copy替代FileUtil.copy
					Path source = Paths.get(srcFilepath);
					Path target = Paths.get(descFilepath);
					// 创建目标目录
					Files.createDirectories(target.getParent());
					// 尝试以独占方式打开文件
					try (FileChannel inChannel = FileChannel.open(source, StandardOpenOption.READ);
						 FileChannel outChannel = FileChannel.open(target,
								 StandardOpenOption.CREATE,
								 StandardOpenOption.WRITE,
								 StandardOpenOption.TRUNCATE_EXISTING)) {
						long transferSize = inChannel.size();
						inChannel.transferTo(0, transferSize, outChannel);
						copied = true;
						successCount++;
						totalBytes += transferSize;
					}
				} catch (AccessDeniedException e) {
					System.out.println(DateUtil.now() + " - [重试] 文件被占用，等待重试 (" + (retry + 1) + "/3)... " + srcFilepath + " 错误: " + e.getMessage());
					Thread.sleep(1000);
					retry++;
				} catch (Exception e) {
					System.out.println(DateUtil.now() + " - [错误] 复制文件失败: " + srcFilepath + " 错误类型: " + e.getClass().getName() + " 错误信息: " + e.getMessage());
					e.printStackTrace(System.out);
					break;
				}
			}
			if (!copied) {
				System.out.println(DateUtil.now() + " - [失败] 文件复制失败 (已重试" + retry + "次): " + srcFilepath);
				failCount++;
			}
		}
		System.out.println(DateUtil.now() + " - [复制] 文件复制总结: 成功" + successCount + "个，失败" + failCount + "个，总大小" + totalBytes + "字节");
		// 创建ZIP文件
		System.out.println(DateUtil.now() + " - [压缩] 开始创建ZIP压缩文件");
		File zipFile = ZipUtil.zip(fileTempPath, Charset.forName("GBK"));
		System.out.println(DateUtil.now() + " - [压缩] ZIP文件创建成功: " + zipFile.getAbsolutePath() + " 大小: " + zipFile.length() + "字节");
		// 加密文件
		System.out.println(DateUtil.now() + " - [加密] 开始加密ZIP文件");
		File encryptFile = new File(zipFile.getParent() + "//" + System.currentTimeMillis());
		Util.encryptFile(zipFile, encryptFile);
		System.out.println(DateUtil.now() + " - [加密] 加密完成，加密后文件: " + encryptFile.getAbsolutePath() + " 大小: " + encryptFile.length() + "字节");
		// 重命名文件
		String newFileName = getTableName(nodes.getJSONObject(0));
		System.out.println(DateUtil.now() + " - [重命名] 重命名文件为: " + newFileName);
		File finalFile = FileUtil.rename(encryptFile, newFileName, true);
		System.out.println(DateUtil.now() + " - [重命名] 重命名成功，最终文件: " + finalFile.getAbsolutePath());
		// 计算总耗时
		long endTime = System.currentTimeMillis();
		long duration = endTime - startTime;
		System.out.println(DateUtil.now() + " - [完成] 导出数据包完成，总耗时: " + duration + "毫秒 (" + (duration / 1000) + "秒)");
		return finalFile;
	}

	public static void copyNode(JSONArray srcData, String newId, boolean isClearSign, String copyCreator, String copySecurity) {
		String nowTime = DateUtil.now();
		Map<String, String> idMap = new HashMap<>();
		for (int i = 0; i < srcData.size(); i++) {
			JSONObject src = srcData.getJSONObject(i);
			String srcId = src.getStr("ID");
			String distId = Convert.toStr(NumberUtil.add(Convert.toBigDecimal(newId), i));
			src.set("newId", distId);
			idMap.put(srcId, distId);
		}
		for (int j = 0; j < srcData.size(); j++) {
			JSONObject dist = srcData.getJSONObject(j);
			for (String srcId : idMap.keySet()) {
				if (StrUtil.equals(dist.getStr("PID"), srcId)) {
					dist.set("newPID", idMap.get(srcId));
				}
			}
		}
		for (int i = 0; i < srcData.size(); i++) {
			JSONObject src = srcData.getJSONObject(i);

			String saveDataStr = src.getStr("SAVE_DATA", "");
			String htmlStr = src.getStr("HTML_DATA", "");
			String createTime = src.getStr("CREATE_TIME", nowTime);
			String saveTime = src.getStr("SAVE_TIME", nowTime);
			String creator = src.getStr("CREATOR", "admin");
			String saveUser = src.getStr("SAVE_USER", "admin");
			String security = src.getStr("SECURITY", "1");
			String tableStatus = src.getStr("TABLE_STATUS", "edit");
			if (isClearSign) {
				if (StrUtil.isNotBlank(saveDataStr)) {
					JSONObject saveData = dealObjNull(saveDataStr);
					JSONArray metas = saveData.getJSONArray("meta");
					if (ObjectUtil.isNotNull(metas)) {
						for (int j = 0; j < metas.size(); j++) {
							JSONObject meta = metas.getJSONObject(j);
							meta.set("readOnly", false);
							meta.set("eles", new JSONArray());
							meta.set("comment", "");
						}
					}
					saveDataStr = saveData.toString();
					htmlStr = TableUtil.TableData2Html(saveDataStr);
				}
				tableStatus = "edit";
				saveUser = copyCreator;
				creator = copyCreator;
				security = copySecurity;
			}
			Entity entity = Entity.create("LAUNCH_CONFIRM")
					.set("ID", src.getStr("newId"))
					.set("PID", src.getStr("newPID", src.getStr("ID")))
					.set("NAME", src.getStr("NAME"))
					.set("TABLE_NUM", src.getStr("TABLE_NUM", ""))
					.set("TYPE", src.getStr("TYPE"))
					.set("LEVEL_NUM", src.getInt("LEVEL_NUM"))
					.set("FILE_NAME", src.getStr("FILE_NAME", ""))
					.set("FILE_PATH", src.getStr("FILE_PATH", ""))
					.set("FILE_FORMAT", src.getStr("FILE_FORMAT", ""))
					.set("CREATOR", creator)
					.set("CREATE_TIME", createTime)
					.set("SORT", src.getInt("SORT", 1))
					.set("SAVE_DATA", saveDataStr)
					.set("SAVE_TIME", saveTime)
					.set("SAVE_USER", saveUser)
					.set("TABLE_STATUS", tableStatus)
					.set("TABLE_HEADER", src.getInt("TABLE_HEADER"))
					.set("COPY_ID", src.getStr("ID"))
					.set("SECURITY", security)
					.set("HTML_DATA", htmlStr);
			SqliteUtil.executeInsertTransaction(entity);
		}
		
		// 清除缓存，因为树结构发生了变化
		clearSearchDropdownCache();
	}


	public static JSONObject importData(String dataStr, String id, String pid, String type) {
		JSONObject res = new JSONObject();
		JSONArray nodes = JSONUtil.parseArray(dataStr);
		try {
			if (nodes.isEmpty()) {
				res.set("success", false);
				res.set("msg", "导入型号数据失败，原因:数据为空");
			} else {
				for (int i = 0; i < nodes.size(); i++) {
					JSONObject node = nodes.getJSONObject(i);
					String nodeType = node.getStr("TYPE");
					if (StrUtil.equals(nodeType, "report")) {
						node.set("TYPE", "a");
					} else if (StrUtil.equals(nodeType, "table")) {
						node.set("TYPE", "b");
					}
				}
				JSONObject rootNode = nodes.getJSONObject(0);
				String srcType = rootNode.getStr("TYPE");
				if (!StrUtil.equals(type, srcType)) {
					res.set("success", false);
					res.set("msg", "导入型号数据失败，原因:数据类型不匹配");
				} else {
					//清除导入型号的原有数据
					String queryAllChildIdSql = getQueryAllChildIdSql(id);
					SqliteUtil.executeCommand("delete from LAUNCH_CONFIRM where id in (" + queryAllChildIdSql + ")");
					SqliteUtil.executeCommand("delete from LAUNCH_SIGN where LAUNCH_ID in (" + queryAllChildIdSql + ")");
					SqliteUtil.executeCommand("delete from LAUNCH_PHOTO where LAUNCH_ID in (" + queryAllChildIdSql + ")");

					String newId = TreeUtil.getNewTreeId();
					int lastSort = TreeUtil.getLastSort(pid);
					TreeUtil.copyNode(nodes, newId, false, "", "");
					//更新根节点的信息
					String updateRootSql = "update LAUNCH_CONFIRM set PID=" + pid + ",SORT=" + lastSort + " WHERE ID=" + newId;
					SqliteUtil.executeCommand(updateRootSql);
					
					// 清除缓存，因为树结构发生了变化
					clearSearchDropdownCache();
					
					res.set("success", true);
					res.set("data", newId);
					res.set("msg", "导入型号成功！");
				}
			}
		} catch (Exception e) {
			res.set("success", false);
			res.set("msg", "导入型号数据失败，原因:" + e);
		}
		return res;
	}

	/**
	 * 导入照片数据
	 */
	public static JSONObject importPhotoData(String photoStr, String newId) {
		JSONObject res = new JSONObject();
		try {
			JSONArray photos = JSONUtil.parseArray(photoStr);
			JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildFieldSql(newId, "ID,COPY_ID"));
			String nowTime = DateUtil.now();
			for (int i = 0; i < photos.size(); i++) {
				JSONObject photo = photos.getJSONObject(i);
				String launchId = photo.getStr("LAUNCH_ID");
				for (int j = 0; j < nodes.size(); j++) {
					JSONObject node = nodes.getJSONObject(j);
					String copyId = node.getStr("COPY_ID");
					if (StrUtil.equals(launchId, copyId)) {
						photo.set("newId", node.getStr("ID"));
						break;
					}
				}
				String insertOnePhotoSql = TableUtil.getInsertOnePhotoSql();
				insertOnePhotoSql = StrUtil.format(insertOnePhotoSql, photo.getStr("newId"), photo.getStr("PHOTO_NAME", ""), photo.getStr("PHOTO_PATH", ""),
						photo.getStr("PHOTO_FORMAT", ""), photo.getStr("PHOTO_NUMBER", ""), photo.getStr("PHOTO_SIZE", ""),
						photo.getStr("CREATOR", ""), photo.getStr("CREATE_TIME", nowTime));
				SqliteUtil.executeCommand(insertOnePhotoSql);
			}
			res.set("success", true);
			res.set("msg", "导入照片数据成功");
		} catch (Exception e) {
			res.set("success", false);
			res.set("msg", "导入照片数据失败，原因:" + e);
		}
		return res;
	}

	/**
	 * 导入签名数据
	 */
	public static JSONObject importSignData(String signStr, String newId) {
		JSONObject res = new JSONObject();
		try {
			JSONArray signs = JSONUtil.parseArray(signStr);
			JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildFieldSql(newId, "ID,COPY_ID"));
			String nowTime = DateUtil.now();
			for (int i = 0; i < signs.size(); i++) {
				JSONObject sign = signs.getJSONObject(i);
				String launchId = sign.getStr("LAUNCH_ID");
				for (int j = 0; j < nodes.size(); j++) {
					JSONObject node = nodes.getJSONObject(j);
					String copyId = node.getStr("COPY_ID");
					if (StrUtil.equals(launchId, copyId)) {
						sign.set("newId", node.getStr("ID"));
						break;
					}
				}
				String insertOneSignSql = TableUtil.getInsertOneSignSql();
				insertOneSignSql = StrUtil.format(insertOneSignSql, sign.getStr("newId"), sign.getStr("IMG", ""),
						sign.getStr("CREATOR", ""), sign.getStr("CREATE_TIME", nowTime));
				SqliteUtil.executeCommand(insertOneSignSql);
			}
			res.set("success", true);
			res.set("msg", "导入签名数据成功");
		} catch (Exception e) {
			res.set("success", false);
			res.set("msg", "导入签名数据失败，原因:" + e);
		}
		return res;
	}


	/**
	 * 导入日志数据
	 */
	public static JSONObject importLogData(String logStr, String newId) {
		JSONObject res = new JSONObject();
		try {
			JSONArray logs = JSONUtil.parseArray(logStr);
			JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildFieldSql(newId, "ID,PID,COPY_ID"));
			String modelId = TreeUtil.getModelId(newId);
			String nowTime = DateUtil.now();
			for (int i = 0; i < logs.size(); i++) {
				JSONObject log = logs.getJSONObject(i);
				String tableId = log.getStr("TABLE_ID");
				for (int j = 0; j < nodes.size(); j++) {
					JSONObject node = nodes.getJSONObject(j);
					String copyId = node.getStr("COPY_ID");
					if (StrUtil.equals(tableId, copyId)) {
						log.set("newId", node.getStr("ID"));
						log.set("newPId", node.getStr("PID"));
						break;
					}
				}
				String insertOneLogSql = TableUtil.getInsertOneLogSql();
				String logTime = log.getStr("LOG_TIME", nowTime);
				if (!StrUtil.contains(logTime, "-")) {
					logTime = DateUtil.formatDateTime(new Date(Convert.toLong(logTime)));
				}
				insertOneLogSql = StrUtil.format(insertOneLogSql, log.getStr("USERNAME"), logTime,
						log.getStr("MODULE_TYPE"), log.getStr("TABLE_NAME"), log.getStr("newId"),
						log.getStr("newPId"), modelId, log.getStr("OPERATION"),
						log.getStr("CONTENT"), log.getStr("REQ_RESULT"), log.getStr("MY_IP"));
				SqliteUtil.executeCommand(insertOneLogSql);
			}
			res.set("success", true);
			res.set("msg", "导入日志数据成功");
		} catch (Exception e) {
			res.set("success", false);
			res.set("msg", "导入日志数据失败，原因:" + e);
		}
		return res;
	}

	/**
	 * 生成文件
	 */
	public static void generateFile(String tableId, String tablePid, int exportType, String creator) {
		String startTime = DateUtil.now();
		String insertSql = "insert into CONFIRM_DOWNLOAD " +
				"(MODULE_NAME, TABLE_NAME,TABLE_ID, " +
				"EXPORT_TYPE, START_TIME, CREATOR)values(" +
				"'{}','{}',{},{},'{}','{}')";
		String module = "发射场确认";
		insertSql = StrUtil.format(insertSql, module, "LAUNCH_CONFIRM", tableId, exportType, startTime, creator);
		SqliteUtil.executeCommand(insertSql);
		int downloadId = SqliteUtil.executeQuery("SELECT rowid FROM CONFIRM_DOWNLOAD ORDER BY rowid DESC LIMIT 1").getJSONObject(0).getInt("ID");
		String fileUploadPath = Util.getFileUploadPath();
		String filePath = fileUploadPath + "//confirmDownload//" + creator + "//" + module + "//";
		FileUtil.mkdir(filePath);
		JSONObject param = new JSONObject();
		if (exportType == 1) {
			// PDF导出 - 使用轻量级树形结构构建，避免内存溢出
			System.out.println(DateUtil.now() + " - [PDF导出] 开始PDF导出，使用内存优化方式");
			logMemoryUsage("PDF导出开始前");

			try {
				// 检查内存是否足够（要求至少100MB可用内存）
				checkMemoryAvailable("PDF导出", 100);

				// 使用PDF优化方法构建树形结构，包含表格内容但进行内存优化
				JSONArray nodes = TreeUtil.buildTreeForPdf(tableId, tablePid);
				System.out.println(DateUtil.now() + " - [PDF导出] 树形结构构建完成，开始生成PDF文件");
				logMemoryUsage("树形结构构建后");

				JSONObject res = PdfUtil.exportTreeToPdf(nodes, filePath);
				logMemoryUsage("PDF生成后");

				// 及时清理nodes对象
				nodes.clear();
				nodes = null;
				System.gc(); // 建议GC回收
				logMemoryUsage("对象清理后");

				if (res.getBool("success")) {
					File file = new File(res.getStr("data"));
					dealFileInfo(file, param, fileUploadPath);
					System.out.println(DateUtil.now() + " - [PDF导出] PDF文件生成成功");
				} else {
					param.set("isComplete", 2);
					param.set("msg", "文件生成失败，原因：" + res.getStr("msg"));
					System.out.println(DateUtil.now() + " - [PDF导出] PDF文件生成失败: " + res.getStr("msg"));
				}
			} catch (OutOfMemoryError e) {
				System.out.println(DateUtil.now() + " - [PDF导出] 内存溢出错误: " + e.getMessage());
				logMemoryUsage("内存溢出时");
				param.set("isComplete", 2);
				param.set("msg", "文件生成失败，内存不足：" + e.getMessage());
			} catch (Exception e) {
				System.out.println(DateUtil.now() + " - [PDF导出] 其他错误: " + e.getMessage());
				e.printStackTrace();
				param.set("isComplete", 2);
				param.set("msg", "文件生成失败，原因：" + e.getMessage());
			}
		} else if (exportType == 2) {

		} else if (exportType == 3) {

		} else if (exportType == 4) {
			try {
				File file = TreeUtil.exportZip(tableId);
				String path = filePath + System.currentTimeMillis();
				FileUtil.mkdir(path);
				file = FileUtil.copy(file, new File(path), true);
				dealFileInfo(file, param, fileUploadPath);
			} catch (Exception e) {
				e.printStackTrace();
				param.set("isComplete", 2);
				param.set("msg", "文件生成失败，原因：" + e);
			}
		}
		String endTime = DateUtil.now();
		String updateSql = StrUtil.format(
				"update CONFIRM_DOWNLOAD set FILE_NAME ='{}',FILE_PATH='{}',FILE_SIZE='{}',IS_COMPLETE={},END_TIME='{}',MSG='{}' where id={}",
				param.getStr("fileName"), param.getStr("filePath"), param.getStr("fileSize"),
				param.getInt("isComplete"), endTime, param.getStr("msg"), downloadId);
		SqliteUtil.executeCommand(updateSql);
	}

	private static void dealFileInfo(File file, JSONObject param, String fileUploadPath) {
		File newFile = FileUtil.rename(file, System.currentTimeMillis() + "", true, true);
		param.set("fileName", file.getName());
		param.set("filePath", FileUtil.subPath(fileUploadPath, newFile.getAbsolutePath()));
		param.set("fileSize", FileUtil.readableFileSize(newFile));
		param.set("isComplete", 1);
		param.set("msg", "文件已生成");
	}

	// 缓存搜索下拉列表数据
	private static JSONObject searchDropdownCache = null;
	private static long searchDropdownCacheTime = 0;
	private static final long CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

	/**
	 * 查询下载记录的搜索条件（优化版：一次递归聚合，避免逐行递归CTE）
	 */
	public static JSONObject queryDownloadSearch(String username, String s_folder, String s_model, String s_project) {
		JSONObject res = new JSONObject();
		
		// 如果没有任何筛选条件，使用缓存（5分钟）
		if (StrUtil.isBlank(s_folder) && StrUtil.isBlank(s_model) && StrUtil.isBlank(s_project)) {
			long currentTime = System.currentTimeMillis();
			if (searchDropdownCache != null && (currentTime - searchDropdownCacheTime) < CACHE_DURATION) {
				return searchDropdownCache;
			}
		}

		String cte =
				"WITH RECURSIVE parent_path AS (" +
				"  SELECT lc.ID, lc.PID, lc.TYPE, lc.SORT, lc.NAME, lc.TABLE_NUM, cd.ID AS DOWNLOAD_ID " +
				"  FROM CONFIRM_DOWNLOAD cd " +
				"  JOIN LAUNCH_CONFIRM lc ON lc.ID = cd.TABLE_ID " +
				"  WHERE cd.CREATOR='" + username + "' " +
				"  UNION ALL " +
				"  SELECT lc.ID, lc.PID, lc.TYPE, lc.SORT, lc.NAME, lc.TABLE_NUM, parent_path.DOWNLOAD_ID " +
				"  FROM LAUNCH_CONFIRM lc " +
				"  JOIN parent_path ON lc.ID = parent_path.PID" +
				"), agg AS (" +
				"  SELECT DOWNLOAD_ID, " +
				"         MAX(CASE WHEN TYPE='a' THEN TABLE_NUM || '：' || NAME END) AS A_TABLE, " +
				"         MAX(CASE WHEN TYPE='folder' THEN printf('%02d', SORT) || '-' || NAME END) AS FOLDER, " +
				"         MAX(CASE WHEN TYPE='model' THEN NAME END) AS MODEL, " +
				"         MAX(CASE WHEN TYPE='project' THEN printf('%02d', SORT) || '-' || NAME END) AS PROJECT " +
				"  FROM parent_path GROUP BY DOWNLOAD_ID" +
				") ";

		String sql = cte + " SELECT DISTINCT FOLDER, MODEL, PROJECT FROM agg WHERE 1=1";
		if (StrUtil.isNotBlank(s_folder)) {
			sql += " AND FOLDER LIKE '%" + s_folder + "%'";
		}
		if (StrUtil.isNotBlank(s_model)) {
			sql += " AND MODEL LIKE '%" + s_model + "%'";
		}
		if (StrUtil.isNotBlank(s_project)) {
			sql += " AND PROJECT LIKE '%" + s_project + "%'";
		}
		sql += " ORDER BY FOLDER, MODEL, PROJECT";

		JSONArray nodes = SqliteUtil.executeQuery(sql);
		JSONArray folders = new JSONArray(), models = new JSONArray(), projects = new JSONArray();
		for (int i = 0; i < nodes.size(); i++) {
			JSONObject node = nodes.getJSONObject(i);
			String folder = node.getStr("FOLDER", "");
			if (StrUtil.isNotBlank(folder) && !folders.contains(folder)) {
				folders.add(folder);
			}
			String model = node.getStr("MODEL", "");
			if (StrUtil.isNotBlank(model) && !models.contains(model)) {
				models.add(model);
			}
			String project = node.getStr("PROJECT", "");
			if (StrUtil.isNotBlank(project) && !projects.contains(project)) {
				projects.add(project);
			}
		}

		res.set("folders", folders);
		res.set("models", models);
		res.set("projects", projects);

		// 缓存结果（仅当没有筛选条件时）
		if (StrUtil.isBlank(s_folder) && StrUtil.isBlank(s_model) && StrUtil.isBlank(s_project)) {
			searchDropdownCache = res;
			searchDropdownCacheTime = System.currentTimeMillis();
		}

		return res;
	}

	/**
	 * 清除搜索下拉列表缓存（在树结构发生变化时调用）
	 */
	public static void clearSearchDropdownCache() {
		searchDropdownCache = null;
		searchDropdownCacheTime = 0;
	}

	/**
	 * 获取节点的层级信息（优化版本，避免递归CTE）
	 */
	public static JSONObject getNodeHierarchyInfo(String nodeId) {
		if (StrUtil.isBlank(nodeId)) {
			return new JSONObject();
		}
		
		// 获取节点的完整父级路径
		String parentSql = getQueryAllParentSql(nodeId, "", "order by LEVEL_NUM");
		JSONArray parents = SqliteUtil.executeQuery(parentSql);
		
		JSONObject result = new JSONObject();
		result.set("A_ID", "");
		result.set("A_NAME", "");
		result.set("A_NUM", "");
		result.set("FOLDER_ID", "");
		result.set("FOLDER_NAME", "");
		result.set("FOLDER_SORT", "");
		result.set("MODEL_ID", "");
		result.set("MODEL_NAME", "");
		result.set("MODEL_SORT", "");
		result.set("PROJECT_ID", "");
		result.set("PROJECT_NAME", "");
		result.set("PROJECT_SORT", "");
		
		// 遍历父级节点，提取各层级信息
		for (int i = 0; i < parents.size(); i++) {
			JSONObject parent = parents.getJSONObject(i);
			String type = parent.getStr("TYPE");
			
			switch (type) {
				case "folder":
					result.set("FOLDER_ID", parent.getStr("ID"));
					result.set("FOLDER_NAME", parent.getStr("NAME"));
					result.set("FOLDER_SORT", parent.getInt("SORT"));
					break;
				case "model":
					result.set("MODEL_ID", parent.getStr("ID"));
					result.set("MODEL_NAME", parent.getStr("NAME"));
					result.set("MODEL_SORT", parent.getInt("SORT"));
					break;
				case "project":
					result.set("PROJECT_ID", parent.getStr("ID"));
					result.set("PROJECT_NAME", parent.getStr("NAME"));
					result.set("PROJECT_SORT", parent.getInt("SORT"));
					break;
				case "a":
					result.set("A_ID", parent.getStr("ID"));
					result.set("A_NAME", parent.getStr("NAME"));
					result.set("A_NUM", parent.getStr("TABLE_NUM"));
					break;
			}
		}
		
		return result;
	}

	/**
	 * 查询下载记录的表格数据（旧：逐行递归CTE，保留以兼容）
	 *
	 * @return
	 */
	public static String getDownloadTableSql() {
		return "SELECT a.*, " +
				"       CASE WHEN a.TABLE_ID IS NOT NULL THEN " +
				"           (SELECT lc_a.TABLE_NUM || '：' || lc_a.NAME " +
				"            FROM LAUNCH_CONFIRM lc_a " +
				"            WHERE lc_a.ID = (" +
				"                WITH RECURSIVE parent_path AS (" +
				"                    SELECT ID, PID, TYPE FROM LAUNCH_CONFIRM WHERE ID = a.TABLE_ID " +
				"                    UNION ALL " +
				"                    SELECT lc.ID, lc.PID, lc.TYPE " +
				"                    FROM LAUNCH_CONFIRM lc " +
				"                    INNER JOIN parent_path pp ON lc.ID = pp.PID " +
				"                ) " +
				"                SELECT ID FROM parent_path WHERE TYPE = 'a' LIMIT 1" +
				"            )" +
				"       ) ELSE '' END AS A_TABLE, " +
				"       CASE WHEN a.TABLE_ID IS NOT NULL THEN " +
				"           (SELECT printf('%02d', lc_f.SORT) || '-' || lc_f.NAME " +
				"            FROM LAUNCH_CONFIRM lc_f " +
				"            WHERE lc_f.ID = (" +
				"                WITH RECURSIVE parent_path AS (" +
				"                    SELECT ID, PID, TYPE FROM LAUNCH_CONFIRM WHERE ID = a.TABLE_ID " +
				"                    UNION ALL " +
				"                    SELECT lc.ID, lc.PID, lc.TYPE " +
				"                    FROM LAUNCH_CONFIRM lc " +
				"                    INNER JOIN parent_path pp ON lc.ID = pp.PID " +
				"                ) " +
				"                SELECT ID FROM parent_path WHERE TYPE = 'folder' LIMIT 1" +
				"            )" +
				"       ) ELSE '' END AS FOLDER, " +
				"       CASE WHEN a.TABLE_ID IS NOT NULL THEN " +
				"           (SELECT lc_m.NAME " +
				"            FROM LAUNCH_CONFIRM lc_m " +
				"            WHERE lc_m.ID = (" +
				"                WITH RECURSIVE parent_path AS (" +
				"                    SELECT ID, PID, TYPE FROM LAUNCH_CONFIRM WHERE ID = a.TABLE_ID " +
				"                    UNION ALL " +
				"                    SELECT lc.ID, lc.PID, lc.TYPE " +
				"                    FROM LAUNCH_CONFIRM lc " +
				"                    INNER JOIN parent_path pp ON lc.ID = pp.PID " +
				"                ) " +
				"                SELECT ID FROM parent_path WHERE TYPE = 'model' LIMIT 1" +
				"            )" +
				"       ) ELSE '' END AS MODEL, " +
				"       CASE WHEN a.TABLE_ID IS NOT NULL THEN " +
				"           (SELECT printf('%02d', lc_p.SORT) || '-' || lc_p.NAME " +
				"            FROM LAUNCH_CONFIRM lc_p " +
				"            WHERE lc_p.ID = (" +
				"                WITH RECURSIVE parent_path AS (" +
				"                    SELECT ID, PID, TYPE FROM LAUNCH_CONFIRM WHERE ID = a.TABLE_ID " +
				"                    UNION ALL " +
				"                    SELECT lc.ID, lc.PID, lc.TYPE " +
				"                    FROM LAUNCH_CONFIRM lc " +
				"                    INNER JOIN parent_path pp ON lc.ID = pp.PID " +
				"                ) " +
				"                SELECT ID FROM parent_path WHERE TYPE = 'project' LIMIT 1" +
				"            )" +
				"       ) ELSE '' END AS PROJECT " +
				"FROM CONFIRM_DOWNLOAD a " +
				"ORDER BY a.START_TIME DESC";
	}

	/**
	 * 查询下载记录的表格数据（优化版：针对指定用户一次性递归聚合）
	 * 仅返回选择所需字段，未包含排序与筛选。
	 */
	public static String getDownloadTableSqlOptimized(String creator) {
		return "WITH RECURSIVE parent_path AS (" +
				"  SELECT lc.ID, lc.PID, lc.TYPE, lc.SORT, lc.NAME, lc.TABLE_NUM, cd.ID AS DOWNLOAD_ID " +
				"  FROM CONFIRM_DOWNLOAD cd " +
				"  JOIN LAUNCH_CONFIRM lc ON lc.ID = cd.TABLE_ID " +
				"  WHERE cd.CREATOR='" + creator + "' " +
				"  UNION ALL " +
				"  SELECT lc.ID, lc.PID, lc.TYPE, lc.SORT, lc.NAME, lc.TABLE_NUM, parent_path.DOWNLOAD_ID " +
				"  FROM LAUNCH_CONFIRM lc " +
				"  JOIN parent_path ON lc.ID = parent_path.PID" +
				"), agg AS (" +
				"  SELECT DOWNLOAD_ID, " +
				"         MAX(CASE WHEN TYPE='a' THEN TABLE_NUM || '：' || NAME END) AS A_TABLE, " +
				"         MAX(CASE WHEN TYPE='folder' THEN printf('%02d', SORT) || '-' || NAME END) AS FOLDER, " +
				"         MAX(CASE WHEN TYPE='model' THEN NAME END) AS MODEL, " +
				"         MAX(CASE WHEN TYPE='project' THEN printf('%02d', SORT) || '-' || NAME END) AS PROJECT " +
				"  FROM parent_path GROUP BY DOWNLOAD_ID" +
				") " +
				"SELECT cd.*, agg.A_TABLE, agg.FOLDER, agg.MODEL, agg.PROJECT " +
				"FROM CONFIRM_DOWNLOAD cd " +
				"JOIN agg ON agg.DOWNLOAD_ID = cd.ID " +
				"WHERE cd.CREATOR='" + creator + "'";
	}


	public static JSONObject importZip(String fullPath, String fileTempPath, String id, String pid, String type) {
		System.out.println(DateUtil.now() + " - 开始导入ZIP文件，源文件路径：" + fullPath + "，临时目录：" + fileTempPath + "，id：" + id + "，pid：" + pid + "，type：" + type);
		JSONObject res = new JSONObject();
		String decryptFile = null;
		boolean needCleanDecryptFile = false;
		try {
			decryptFile = fullPath + "-decrypt";
			System.out.println(DateUtil.now() + " - 开始解密文件：" + fullPath + " -> " + decryptFile);
			try {
				Util.decryptFile(FileUtil.file(fullPath), FileUtil.file(decryptFile));
				needCleanDecryptFile = true; // 解密成功，需要清理解密文件
			} catch (Exception e) {
				System.out.println(DateUtil.now() + " - 文件解密失败，原因：" + e);
				decryptFile = fullPath;
				needCleanDecryptFile = false; // 解密失败，使用原文件，不需要清理
			}

			System.out.println(DateUtil.now() + " - 文件解密完成：" + decryptFile);

			System.out.println(DateUtil.now() + " - 开始解压文件到临时目录：" + fileTempPath);
			Util.customUnzip(decryptFile, fileTempPath);
			System.out.println(DateUtil.now() + " - 文件解压完成");

			String srcPhotoPath = fileTempPath + "photo";
			System.out.println(DateUtil.now() + " - 检查照片目录是否存在：" + srcPhotoPath);
			if (FileUtil.exist(srcPhotoPath)) {
				System.out.println(DateUtil.now() + " - 照片目录存在，准备复制照片文件");
				String uploadPath = Util.getFileUploadPath();
				System.out.println(DateUtil.now() + " - 获取上传目录路径：" + uploadPath);

				File[] photos = new File(srcPhotoPath).listFiles();
				if (photos != null) {
					System.out.println(DateUtil.now() + " - 找到照片文件夹数量：" + photos.length);
					int copySuccess = 0;
					int copyFailed = 0;

					for (int i = 0; i < photos.length; i++) {
						File f = photos[i];
						if (FileUtil.isDirectory(f)) {
							try {
								System.out.println(DateUtil.now() + " - 开始复制文件夹 [" + (i + 1) + "/" + photos.length + "]: " + f.getAbsolutePath() + " -> " + uploadPath);
								FileUtil.copy(f.getAbsolutePath(), uploadPath, true);
								System.out.println(DateUtil.now() + " - 文件夹复制成功：" + f.getAbsolutePath());
								copySuccess++;
							} catch (Exception e) {
								System.out.println(DateUtil.now() + " - 复制文件夹失败 [" + (i + 1) + "/" + photos.length + "]: " + f.getAbsolutePath() + "，错误原因： " + e);
								copyFailed++;
							}
						} else {
							System.out.println(DateUtil.now() + " - 跳过非文件夹项目：" + f.getAbsolutePath());
						}
					}
					System.out.println(DateUtil.now() + " - 文件夹复制完成，成功：" + copySuccess + "，失败：" + copyFailed);
				} else {
					System.out.println(DateUtil.now() + " - 照片目录为空或无法访问");
				}
			} else {
				System.out.println(DateUtil.now() + " - 照片目录不存在，跳过照片复制步骤");
			}

			String dataPath = fileTempPath + "data.json";
			System.out.println(DateUtil.now() + " - 检查数据文件是否存在：" + dataPath);
			if (FileUtil.exist(dataPath)) {
				System.out.println(DateUtil.now() + " - 数据文件存在，开始读取");
				String dataStr = FileUtil.readUtf8String(dataPath);
				System.out.println(DateUtil.now() + " - 数据文件读取完成，数据长度：" + dataStr.length() + " 字符");

				System.out.println(DateUtil.now() + " - 开始导入主数据，id：" + id + "，pid：" + pid + "，type：" + type);
				JSONObject importDataRes = TreeUtil.importData(dataStr, id, pid, type);
				if (importDataRes.getBool("success")) {
					String newId = importDataRes.getStr("data");
					System.out.println(DateUtil.now() + " - 主数据导入成功，新ID：" + newId);

					String photoPath = fileTempPath + "photo.json";
					System.out.println(DateUtil.now() + " - 检查照片数据文件是否存在：" + photoPath);
					if (FileUtil.exist(photoPath)) {
						System.out.println(DateUtil.now() + " - 照片数据文件存在，开始读取");
						String photoStr = FileUtil.readUtf8String(photoPath);
						System.out.println(DateUtil.now() + " - 照片数据文件读取完成，数据长度：" + photoStr.length() + " 字符");

						System.out.println(DateUtil.now() + " - 开始导入照片数据，newId：" + newId);
						JSONObject importPhotoRes = TreeUtil.importPhotoData(photoStr, newId);
						if (!importPhotoRes.getBool("success")) {
							System.out.println(DateUtil.now() + " - 照片数据导入失败，错误信息：" + importPhotoRes.getStr("msg"));
							res = importPhotoRes;
						} else {
							System.out.println(DateUtil.now() + " - 照片数据导入成功");
						}
					} else {
						System.out.println(DateUtil.now() + " - 照片数据文件不存在，跳过照片数据导入步骤");
					}

					String signPath = fileTempPath + "sign.json";
					System.out.println(DateUtil.now() + " - 检查签名数据文件是否存在：" + signPath);
					if (FileUtil.exist(signPath)) {
						System.out.println(DateUtil.now() + " - 签名数据文件存在，开始读取");
						String signStr = FileUtil.readUtf8String(signPath);
						System.out.println(DateUtil.now() + " - 签名数据文件读取完成，数据长度：" + signStr.length() + " 字符");

						System.out.println(DateUtil.now() + " - 开始导入签名数据，newId：" + newId);
						JSONObject importSignRes = TreeUtil.importSignData(signStr, newId);
						if (!importSignRes.getBool("success")) {
							System.out.println(DateUtil.now() + " - 签名数据导入失败，错误信息：" + importSignRes.getStr("msg"));
							res = importSignRes;
						} else {
							System.out.println(DateUtil.now() + " - 签名数据导入成功");
						}
					} else {
						System.out.println(DateUtil.now() + " - 签名数据文件不存在，跳过签名数据导入步骤");
					}

					String logPath = fileTempPath + "log.json";
					System.out.println(DateUtil.now() + " - 检查日志数据文件是否存在：" + logPath);
					if (FileUtil.exist(logPath)) {  // 注意：原代码这里检查的是signPath，可能是错误的
						System.out.println(DateUtil.now() + " - 日志数据文件存在，开始读取");
						String logStr = FileUtil.readUtf8String(logPath);
						System.out.println(DateUtil.now() + " - 日志数据文件读取完成，数据长度：" + logStr.length() + " 字符");

						System.out.println(DateUtil.now() + " - 开始导入日志数据，newId：" + newId);
						JSONObject importLogRes = TreeUtil.importLogData(logStr, newId);
						if (!importLogRes.getBool("success")) {
							System.out.println(DateUtil.now() + " - 日志数据导入失败，错误信息：" + importLogRes.getStr("msg"));
							res = importLogRes;
						} else {
							System.out.println(DateUtil.now() + " - 日志数据导入成功");
						}
					} else {
						System.out.println(DateUtil.now() + " - 日志数据文件不存在，跳过日志数据导入步骤");
					}

					System.out.println(DateUtil.now() + " - 所有数据导入成功完成");
					res.set("success", true);
					res.set("rootId", newId);
				} else {
					System.out.println(DateUtil.now() + " - 主数据导入失败，错误信息：" + importDataRes.getStr("msg"));
					res = importDataRes;
				}
			} else {
				System.out.println(DateUtil.now() + " - 数据文件不存在，导入失败");
				res.set("success", false);
				res.set("msg", "导入失败，原因：data.json不存在");
			}

		} catch (Exception exception) {
			System.out.println(DateUtil.now() + " - 导入过程发生异常：" + exception);
			exception.printStackTrace(System.out);
			res.set("success", false);
			res.set("msg", "文件解压失败，原因：" + exception.getLocalizedMessage());
		} finally {
			// 清理临时文件和目录
			try {
				System.out.println(DateUtil.now() + " - 开始清理临时资源");

				// 清理解密临时文件
				if (needCleanDecryptFile && decryptFile != null && !decryptFile.equals(fullPath)) {
					File decryptTempFile = new File(decryptFile);
					if (decryptTempFile.exists()) {
						boolean deleted = decryptTempFile.delete();
						System.out.println(DateUtil.now() + " - 清理解密临时文件：" + decryptFile + "，结果：" + (deleted ? "成功" : "失败"));
					}
				}

				// 清理解压临时目录
				if (fileTempPath != null) {
					File tempDir = new File(fileTempPath);
					if (tempDir.exists()) {
						try {
							FileUtil.del(tempDir);
							System.out.println(DateUtil.now() + " - 清理解压临时目录：" + fileTempPath + "，结果：成功");
						} catch (Exception e) {
							System.out.println(DateUtil.now() + " - 清理解压临时目录失败：" + e.getMessage());
						}
					}
				}

				// 触发垃圾回收释放文件句柄
				System.gc();
				System.out.println(DateUtil.now() + " - 临时资源清理完成");

			} catch (Exception cleanupException) {
				System.out.println(DateUtil.now() + " - 清理临时资源时发生异常：" + cleanupException.getMessage());
			}

			System.out.println(DateUtil.now() + " - importZip方法执行完毕，返回结果：" + res);
		}
		return res;
	}

	public static void clearAllData() {
		SqliteUtil.executeCommand("delete from CONFIRM_DOWNLOAD");
		SqliteUtil.executeCommand("delete from CONFIRM_LOG");
		SqliteUtil.executeCommand("delete from LAUNCH_PHOTO");
		SqliteUtil.executeCommand("delete from LAUNCH_SIGN");
		SqliteUtil.executeCommand("delete from LAUNCH_CONFIRM where ID>2");
	}

	public static void selectWorkType(String id, String workType) {
		JSONArray nodes = SqliteUtil.executeQuery("select * from LAUNCH_CONFIRM where ID=" + id);
		JSONObject node = nodes.getJSONObject(0);
		JSONObject saveData = new JSONObject();
		if (StrUtil.isNotBlank(node.getStr("SAVE_DATA"))) {
			saveData = JSONUtil.parseObj(node.getStr("SAVE_DATA"));
		}
		saveData.set("workType", workType);
		Entity entity = Entity.create("LAUNCH_CONFIRM")
				.set("SAVE_DATA", saveData.toString());
		Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
		SqliteUtil.executeUpdateTransaction(entity, where);
	}

	/**
	 * 导出工时统计
	 *
	 * @param id
	 */
	public static JSONObject exportWorkHours(String id, String name) {
		JSONObject res = new JSONObject();
		JSONArray data = new JSONArray();
		JSONArray nodes = SqliteUtil.executeQuery(getQueryAllChildSql(id));
		for (int x = 0; x < nodes.size(); x++) {
			JSONObject node = nodes.getJSONObject(x);
			String saveDataStr = node.getStr("SAVE_DATA");
			if (StrUtil.isBlank(saveDataStr)) {
				continue;
			}
			JSONObject saveData = JSONUtil.parseObj(saveDataStr);
			String workType = saveData.getStr("workType", "");
			if (StrUtil.isBlank(workType)) {
				continue;
			}
			JSONArray meta = saveData.getJSONArray("meta");
			if (ObjUtil.isNull(meta)) {
				continue;
			}
			for (int i = 0; i < meta.size(); i++) {
				JSONObject cell = meta.getJSONObject(i);
				String postType = cell.getStr("postType", "");
				JSONArray eles = cell.getJSONArray("eles");
				if (ObjUtil.isNull(eles)) {
					continue;
				}
				for (int j = 0; j < eles.size(); j++) {
					JSONObject ele = eles.getJSONObject(j);
					String type = ele.getStr("type");
					if (type.equals("sign")) {
						String date = ele.getStr("date");
						String signName = ele.getStr("signName");
						if (StrUtil.isNotBlank(signName) &&
								StrUtil.isNotBlank(postType) &&
								(!StrUtil.equals(postType, "取消设置")) &&
								StrUtil.isNotBlank(workType) &&
								StrUtil.isNotBlank(date)) {
							JSONObject set = new JSONObject()
									.set("date", date)
									.set("signName", signName)
									.set("postType", postType)
									.set("workType", workType);
							data.put(set);
						}
					}
				}
			}
		}

		if (!data.isEmpty()) {
			JSONArray workTypes = Util.getWorkType();
			JSONObject obj = new JSONObject();
			String dateStr = "";
			long startTime = 0, endTime = 0;
			//计算时间范围
			for (int i = 0; i < data.size(); i++) {
				JSONObject object = data.getJSONObject(i);
				Date date = object.getDate("date");
				long time = date.getTime();
				if (i == 0) {
					startTime = time;
					endTime = time;
				}
				if (startTime >= time) {
					startTime = time;
				}
				if (endTime <= time) {
					endTime = time;
				}

				String key = object.getStr("signName") + "_" + object.getStr("postType");
				String workType = object.getStr("workType");
				JSONObject countObj = obj.getJSONObject(key);
				if (ObjUtil.isNull(countObj)) {
					countObj = new JSONObject();
				}
				int count = countObj.getInt(workType, 0);
				countObj.set(workType, count + 1);
				obj.set(key, countObj);
			}
			dateStr = DateUtil.format(new Date(startTime), "yyyyMMdd") + " 到" + DateUtil.format(new Date(endTime), "yyyyMMdd");

			ArrayList<Map<String, Object>> rows = new ArrayList<>();
			int index = 0;
			for (String key : obj.keySet()) {
				index++;
				List<String> strings = StrUtil.split(key, "_");
				Map<String, Object> row = new LinkedHashMap<>();
				row.put("序号", index);
				row.put("姓名", strings.get(0));
				row.put("时间", dateStr);
				row.put("岗位", strings.get(1));
				for (int w = 0; w < workTypes.size(); w++) {
					row.put(workTypes.getStr(w), "");
				}
				JSONObject countObj = obj.getJSONObject(key);
				for (String workType : countObj.keySet()) {
					row.put(workType, countObj.get(workType));
				}
				rows.add(row);
			}
			rows.sort((o1, o2) -> {
				int result = 0;

				result = CompareUtil.compare(o1.get("姓名").toString(), o2.get("姓名").toString());
				if (result == 0) {
					result = CompareUtil.compare(o1.get("岗位").toString(), o2.get("岗位").toString());
				}
				// 如果需要降序排序，可以在这里反转结果
				return result;
			});
			for (int i = 0; i < rows.size(); i++) {
				rows.get(i).put("序号", i + 1);
			}
			String resFile = Util.getFileTempPath() + name + "(内部).xlsx";
			ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(resFile);
			writer.writeRow(Arrays.asList("序号", "姓名", "时间", "岗位", "工作项目"));
			writer.write(rows, true);
			writer.writeRow(Collections.singletonList(""));
			writer.merge(0, 1, 0, 0, "序号", false)
					.merge(0, 1, 1, 1, "姓名", false)
					.merge(0, 1, 2, 2, "时间", false)
					.merge(0, 1, 3, 3, "岗位", false)
					.merge(0, 0, 4, (4 + workTypes.size() - 1), "工作项目", false)
					.merge(rows.size() + 2, rows.size() + 2, 0, (4 + workTypes.size() - 1), "注：（1）膜粘贴包括漆膜、F46膜、聚酰亚胺镀铝膜、导热膜；\n" +
							"  （2）导热填料包括导热硅橡胶、导热硅脂、导热垫、铟箔等。", false);

			CellStyle cellStyle = writer.getCellStyle();
			writer.setRowStyleIfHasData(0, cellStyle);
			writer.setRowStyleIfHasData(1, cellStyle);
			writer.setRowHeight(rows.size() + 2, 37);

			cellStyle.setWrapText(true);
			writer.setRowStyleIfHasData(rows.size() + 2, cellStyle);

			writer.setColumnWidth(0, 5);
			writer.setColumnWidth(1, 7);
			writer.setColumnWidth(2, 22);
			writer.setColumnWidth(3, 5);
			for (int w = 0; w < workTypes.size(); w++) {
				writer.setColumnWidth(w + 4, (workTypes.getStr(w).length() * 2) + 2);
			}

			writer.close();

			res.set("data", resFile)
					.set("success", true);
		} else {
			res.set("msg", "未发现有效的工时签名！")
					.set("success", false);
		}

		return res;
	}

	public static void main(String[] args) throws Exception {
		TimeInterval timer = new TimeInterval();
		exportWorkHours("0", "tongji");
		System.out.println(timer.intervalPretty());
	}

	public static boolean isFileAvailable(File file) {
		try (FileInputStream fis = new FileInputStream(file);
			 FileChannel channel = fis.getChannel()) {
			FileLock lock = channel.tryLock(0L, Long.MAX_VALUE, true);
			if (lock != null) {
				lock.release();
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}

}

!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).koKR=e()}}((function(){return function e(n,o,r){function i(f,u){if(!o[f]){if(!n[f]){var d="function"==typeof require&&require;if(!u&&d)return d(f,!0);if(t)return t(f,!0);var l=new Error("Cannot find module '"+f+"'");throw l.code="MODULE_NOT_FOUND",l}var s=o[f]={exports:{}};n[f][0].call(s.exports,(function(e){return i(n[f][1][e]||e)}),s,s.exports,e,n,o,r)}return o[f].exports}for(var t="function"==typeof require&&require,f=0;f<r.length;f++)i(r[f]);return i}({1:[function(e,n,o){"use strict";n.exports={languageTag:"ko-KR",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"천",million:"백만",billion:"십억",trillion:"일조"},ordinal:function(){return"."},currency:{symbol:"₩",code:"KPW"}}},{}]},{},[1])(1)}));
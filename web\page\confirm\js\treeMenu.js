function getTypeInfo(type) {
    var nodeTypeNameLabel = '节点', level, isTableNum = true;
    if (type === "folder") {
        level = 0;
        nodeTypeNameLabel = "分类";
        isTableNum = false;
    } else if (type === "model") {
        level = 1;
        nodeTypeNameLabel = "型号";
        isTableNum = false;
    } else if (type === "project") {
        level = 2;
        nodeTypeNameLabel = "项目";
        isTableNum = false;
    } else if (type === "a") {
        level = 3;
        nodeTypeNameLabel = 'A表';
    } else if (type === "b") {
        level = 4;
        nodeTypeNameLabel = 'B表';
    }
    return {
        isTableNum: isTableNum,
        level: level,
        nodeTypeNameLabel: nodeTypeNameLabel
    };
}

function getSecurityName(securityKey) {
    var securityName = "";
    for (var i = 0; i < security.length; i++) {
        if (securityKey == security[i].KEY) {
            securityName = security[i].NAME;
            break;
        }
    }
    return securityName;
}

function checkNodeNameIsRepeat(parentId, nodeName, oldNodeName) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (nodeName == d['NAME']) {
                    flag = true;
                    break;
                }
            }
            if (nodeName == oldNodeName) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    postSyncAjax("tree", 'QueryTreeNodeByPid', {
        ID: parentId
    }, cb_success);
    return flag;
}

function checkTableNumIsRepeat(parentId, tableNum, oldTableNum) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (tableNum == d['TABLE_NUM']) {
                    flag = true;
                    break;
                }
            }
            if (tableNum == oldTableNum) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    postSyncAjax("tree", 'QueryTreeNodeByPid', {
        ID: parentId
    }, cb_success);
    return flag;
}

function addTableNode(treeNode, type, isCopy = false) {
    var typeInfo = getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level,
        isTableNum = typeInfo.isTableNum;
    var optText = isCopy ? '复制' : '添加';
    var optTitle = optText + nodeTypeNameLabel;
    //日志记录
    var log = {};
    log.operation = optTitle;
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];

    var securityOptions = "";
    for (var i = 0; i < security.length; i++) {
        securityOptions += '<option value="' + security[i].KEY + '">' + security[i].NAME + '</option>';
    }

    var addTpl =
        '<form class="layui-form" action="" lay-filter="add-node-form">\
            <div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
					<label class="layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
					<div class="layui-input-block">\
						<input type="text" name="tableNum" lay-verify="' + (isTableNum ? 'required' : '') + '" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号"  class="layui-input">\
					</div>\
				</div>\
				<div class="layui-form-item">\
					<label class="layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
					<div class="layui-input-block">\
						<input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
					</div>\
				</div>\
				<div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
					<label class="layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
					<div class="layui-input-block">\
						<select name="security" id="security">' + securityOptions + '</select>\
					</div>\
				</div>\
				<div class="layui-form-item" style="display:none;">\
					<div class="layui-input-block">\
						<div class="layui-footer">\
							<button class="layui-btn" id="addNodeSubmit" lay-submit="" lay-filter="submit-node">确认</button>\
							<button type="reset" id="addNodeReset" class="layui-btn layui-btn-primary">重置</button>\
						</div>\
					</div>\
				</div>\
			</form>';
    layer.open({
        title: optTitle,
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false,
        area: ['500px', isTableNum ? '280px' : '170px'],
        content: '<div id="addTableNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '重置', '取消'],
        yes: function () {
            $('#addNodeSubmit').click();
        },
        btn2: function () {
            $('#addNodeReset').click();
            return false;
        },
        btn3: function () {
            return true;
        },
        success: function (layero, userLayerIndex, that) {
            $(layero).find('.layui-layer-content').css("overflow", "visible");
            $("#addTableNodeContent").append(addTpl);
        }
    });
    form.render(null, 'add-node-form');

    form.on('submit(submit-node)', function (formData) {
        var param = {};
        var parentId = isCopy ? treeNode.PID : treeNode.ID;
        if (checkNodeNameIsRepeat(parentId, formData.field.name)) {
            layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
                icon: 2
            });
            return false;
        }
        if (isTableNum) {
            if (checkTableNumIsRepeat(parentId, formData.field.tableNum)) {
                layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
                    icon: 2
                });
                return false;
            }
        }

        param.name = formData.field.name;
        param.tableNum = formData.field.tableNum || '';
        param.security = formData.field.security;
        param.creator = sessionStorage.getItem("username");
        if (isCopy) {
            param.id = treeNode['ID'];
            param.pid = treeNode['PID'];
        } else {
            param.pid = treeNode['ID'];
            param.type = type;
            param.level = level;
        }
        var content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上" + optText + "了" + nodeTypeNameLabel;
        if (isTableNum) {
            content += "，具体内容为（" + nodeTypeNameLabel + "序号：" + param.tableNum +
                "，" + nodeTypeNameLabel + "名称：" + param.name +
                "，" + nodeTypeNameLabel + "密级：" + getSecurityName(param.security) + "）";
        } else {
            content += "，具体内容为（" + nodeTypeNameLabel + "名称：" + param.name + "）";
        }
        log.content = content;
        var loadIndex = layer.load();
        var cb_success = function (data) {
            layer.closeAll();
            reloadTree(parentId, data.id);
            log.reqResult = 1;
            addConfirmLog(log);
        };
        var cb_fail = function () {
            log.reqResult = 0;
            addConfirmLog(log);
        }
        postAjax("tree", isCopy ? 'CopyNode' : 'AddTableNode', param, true, cb_success, cb_fail);
        return false;
    });
}

function editTableNode(treeNode) {
    var log = {};
    var type = treeNode['TYPE'];
    var oldSecurity = treeNode['SECURITY'];
    var typeInfo = getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level,
        isTableNum = typeInfo.isTableNum;
    var nameTpl = '<div class="layui-form-item">\
											<label class="layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="newName" value="' + treeNode['NAME'] +
        '" lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
											</div>\
										</div>';
    var tableNumTpl = '<div class="layui-form-item">\
											<label class="layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
											<div class="layui-input-block">\
												<input type="text" name="newTableNum" value="' + treeNode['TABLE_NUM'] + '" \
        lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号" class="layui-input">\
											</div>\
										</div>';
    var securityOptions = "";
    for (var i = 0; i < security.length; i++) {
        if (security[i].KEY == oldSecurity) {
            securityOptions += '<option selected value="' + security[i].KEY + '">' + security[i].NAME + '</option>';
        } else {
            securityOptions += '<option value="' + security[i].KEY + '">' + security[i].NAME + '</option>';
        }
    }

    var securityTpl = '<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
											<div class="layui-input-block">\
												<select name="security" id="security">' + securityOptions + '</select>\
											</div>\
										</div>';
    if (!isTableNum) {
        tableNumTpl = "";
        securityTpl = "";
    }

    //日志记录
    log.operation = '编辑' + nodeTypeNameLabel;
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];

    var updateTpl =
        '<form class="layui-form" action="" lay-filter="editNodeForm">\
            ' + tableNumTpl + nameTpl + securityTpl + '\
				<div class="layui-form-item" style="display:none;">\
					<div class="layui-input-block">\
						<div class="layui-footer">\
							<button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="editNodeSubmit">确认</button>\
							<button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
						</div>\
					</div>\
				</div>\
			</form>';
    layer.open({
        title: '编辑' + nodeTypeNameLabel,
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false, //不允许拉伸
        area: ['600px', isTableNum ? '280px' : '170px'],
        content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '重置', '关闭'],
        yes: function () {
            $('#editNodeSubmit').click();
        },
        btn2: function () {
            $('#editNodeReset').click();
            return false;
        },
        btn3: function () {
            return true;
        },
        success: function (layero, userLayerIndex, that) {
            $(layero).find('.layui-layer-content').css("overflow", "visible");
            $("#editNodeContent").append(updateTpl);
        }
    });

    form.render(null, 'editNodeForm');
    form.on('submit(editNodeSubmit)', function (data) {

            var parentId = treeNode.PID;
            var param = {};
            if (checkNodeNameIsRepeat(parentId, data.field.newName, treeNode['NAME'])) {
                layer.alert("该节点名称已经存在！", {icon: 2});
                return false;
            }
            if (isTableNum) {
                if (checkTableNumIsRepeat(parentId, data.field.newTableNum, treeNode['TABLE_NUM'])) {
                    layer.alert("该节点表序号已经存在！", {icon: 2});
                    return false;
                }
                param.tableNum = data.field['newTableNum'] || "";
                param.security = data.field.security;
            }
            param.name = data.field.newName;
            param.id = treeNode['ID'];

            //是否有锁定编辑的权限
            param.hasLockEdit = "true";
            var content = "将节点【" + treeNode.NAME + "（" + treeNode.ID + "）】更改为";
            if (isTableNum) {
                content += "（" + nodeTypeNameLabel + "序号：" + param.tableNum + "，" + nodeTypeNameLabel +
                    "名称：" + param.name + "，" + nodeTypeNameLabel + "密级：" + getSecurityName(param.security) + "）";
            } else {
                content += "（" + nodeTypeNameLabel + "名称：" + param.name + "）";
            }
            log.content = content;
            var cb_success = function () {
                layer.closeAll();
                reloadTree(treeNode['PID'], treeNode['ID']);
                log.reqResult = 1;
                addConfirmLog(log);
            };
            var cb_fail = function () {
                log.reqResult = 0;
                addConfirmLog(log);
            };
            postAjax('tree', 'UpdateTableNode', param, true, cb_success, cb_fail);
            return false;
        }
    )
    ;
}

function deleteTableNode(treeNode) {
    var log = {};
    //日志记录
    log.operation = "删除节点";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "删除节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】";
    var msg = "确认删除 节点 -- " + treeNode['NAME'] + " 吗？";
    if (treeNode['ISPARENT']) {
        msg = "该节点下有子节点会一并删除,确认删除吗?"
    }
    layer.confirm(msg, {
        icon: 3,
        title: '提示'
    }, function (index) {
        var cb_success = function () {
            layer.closeAll();
            reloadTree(treeNode['PID']);
            log.reqResult = 1;
            addConfirmLog(log);
        };
        var cb_fail = function () {
            log.reqResult = 0;
            //addConfirmLog(log);
        }
        postAjax('tree', 'DeleteNode', {
            id: treeNode['ID']
        }, true, cb_success, cb_fail);
    });
}

function selectFolder(treeNode) {
    var addTpl = '<form class="layui-form" action="" lay-filter="selectFolderForm">\
						<div class="layui-form-item">\
							<label class="layui-form-label">型号名称:</label>\
							<div class="layui-input-block">\
								<input type="text" readOnly="readOnly" value="' + treeNode['NAME'] + '" class="layui-input">\
							</div>\
						</div>\
						<div class="layui-form-item" style="">\
							<label class="layui-form-label">分类名称:</label>\
							<div class="layui-input-block">\
								<select name="folder" id="folder" lay-filter="folder" lay-verify="required">\
									<option value=""></option>\
								</select>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="selectFolderSubmit" lay-submit="" lay-filter="selectFolderSubmit">确定</button>\
								</div>\
							</div>\
						</div>\
					</form>';
    var log = {};
    log.operation = '选择分类';
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];

    layer.open({
        title: '选择分类',
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false,
        area: ['500px', '220px'],
        content: '<div id="selectFolderContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确定', '取消'],
        yes: function () {
            $('#selectFolderSubmit').click();
        },
        btn2: function () {
            return true;
        },
        success: function () {
            $("#selectFolderContent").append(addTpl);
            $("#selectFolderContent").parent().css("overflow", "visible");
            postSyncAjax("tree", 'QueryFolder', {},
                function (res) {
                    if (res.success) {
                        var datas = res.data;
                        for (var i = 0; i < datas.length; i++) {
                            var folderName = datas[i]['NAME'];
                            var folderId = datas[i]['ID'];
                            if (folderName == treeNode.getParentNode()['NAME']) {
                                $("#folder").append('<option selected value="' + folderId + '">' + folderName + '</option>');
                            } else {
                                $("#folder").append('<option value="' + folderId + '">' + folderName + '</option>');
                            }
                        }
                    } else {
                        layer.alert(res.msg, {
                            icon: 2
                        });
                    }
                });
        }
    });
    form.render(null, 'selectFolderForm');
    form.on('submit(selectFolderSubmit)',
        function (data) {
            var param = data.field;
            var folderId = param.folder;
            param.id = treeNode['ID'];
            param.folderId = folderId;
            var folderName = $("#folder option:selected").text();
            log.content = "将型号【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】的分类调整为【" + folderName + "（" + folderId + "）】";
            postAjax("tree", 'SelectFolder', param, true,
                function () {
                    layer.closeAll();
                    var rootNode = ztreeObj.getNodesByParam("TYPE", "root", null)[0];
                    ztreeObj.reAsyncChildNodes(rootNode, 'refresh', false,
                        function () {
                            ztreeObj.reAsyncChildNodes(ztreeObj.getNodeByParam("ID", folderId, null), 'refresh', false,
                                function () {
                                    loadTreeMenu();
                                });
                        });
                    log.reqResult = 1;
                    addConfirmLog(log);
                }, function () {
                    log.reqResult = 0;
                    addConfirmLog(log);
                });
            return false;
        });
}

function importTableNode(treeNode, type) {

    var typeInfo = getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level;
    //日志记录
    var log = {};
    log.operation = "导入" + nodeTypeNameLabel;
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];

    var fileFlag = false;
    var uploadInst;
    layer.open({
        title: "导入" + nodeTypeNameLabel,
        type: 1,
        area: ['460px', "340px"],
        content: '<div id="importTableContent" style="padding: 15px 0px 0px 0px;"></div>',
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        btn: ['确定', '取消'],
        yes: function () {
            if (!fileFlag) {
                layer.alert('请选择需要导入的excel文件!', {
                    icon: 2
                });
                return false;
            }
            var tableName = $("#b-tableName").val();

            if (checkNodeNameIsRepeat(treeNode['ID'], tableName)) {
                layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
                    icon: 2
                });
                return false;
            }

            var tableNum = $("#b-tableNum").val();
            if (tableNum == "") {
                layer.alert('请输入' + nodeTypeNameLabel + '的序号!', {
                    icon: 2
                });
                return false;
            }

            if (checkTableNumIsRepeat(treeNode['ID'], tableNum)) {
                layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
                    icon: 2
                });
                return false;
            }
            uploadInst.config.url = getReqUrl('table', 'ImportTable', '&pid=' + treeNode['ID'] + '&type=' + type + '&level=' + level +
                '&tableName=' + tableName + '&tableNum=' + tableNum +
                '&saveUser=' + sessionStorage.getItem("username"));
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上" +
                "导入了" + nodeTypeNameLabel + "，具体内容为（" + nodeTypeNameLabel +
                "序号：" + tableNum + "，" + nodeTypeNameLabel +
                "名称：" + tableName + "）";
            $('#uploadStart').click();
        },
        btn2: function () {
            return true;
        },
        success: function () {
            var tpl = '<form class="layui-form" lay-filter="importTableForm">\
                        <div class="layui-form-item">\
                            <label class="fieldlabel layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
                            <div class="layui-input-block">\
                                <input type="text" name="tableNum" id="b-tableNum" lay-verify="required" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号" style="width:330px" class="layui-input">\
                            </div>\
                        </div>\
                        <div class="layui-form-item layui-hide">\
                            <label class="fieldlabel layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
                            <div class="layui-input-block">\
                                <input type="text" name="name" id="b-tableName" lay-verify="required" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" style="width:330px" class="layui-input">\
                            </div>\
                        </div>\
                        <div class="layui-form-item">\
                            <label class="fieldlabel layui-form-label">文件内容:</label>\
                            <div class="layui-input-block">\
                                <div class="layui-upload">\
                                    <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>\
                                    <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
                                </div>\
                            </div>\
                        </div>\
                        <div class="layui-form-item" id="selectedFile" style="display: none;">\
                            <label class="fieldlabel layui-form-label">已选文件:</label>\
                            <div class="layui-input-block">\
                                <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
                            </div>\
                        </div>\
                        <div class="layui-form-item" style="display:none;">\
                            <center>\
                                <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
                                <button id="btn_cancel" class="layui-btn">取消</button>\
                            </center>\
                        </div>\
                    </form>'
            $("#importTableContent").append(tpl);
            form.render(null, 'importTableForm');
            uploadInst = upload.render({
                elem: '#uploadChoice',
                url: getReqUrl('table', 'ImportTable'),
                auto: false,
                exts: 'xls|xlsx',
                field: 'uploadFile',
                bindAction: '#uploadStart',
                dataType: "json",
                choose: function (obj) {
                    fileFlag = true;
                    var files = obj.pushFile();
                    obj.preview(function (index, file, result) {
                        var filename = file.name;
                        $("#selectedFile").show();
                        $("#selectedFileName").text(filename);
                        filename = filename.replace("：", ":");
                        if (filename.indexOf(":") > -1) {
                            filename = filename.split(":")[1];
                        }

                        if (filename.indexOf(".") > -1) {
                            filename = filename.split(".")[0];
                        }
                        $("#b-tableName").val(filename);
                        $("#b-tableName").parent().parent().removeClass("layui-hide");
                    });
                },
                before: function (obj) {
                    layer.load();
                },
                done: function (res, index, upload) {
                    layer.closeAll();
                    if (res.success) {
                        log.reqResult = 1;
                        layer.msg("导入成功");
                        reloadTree(treeNode.ID, res.data.id);
                    } else {
                        log.reqResult = 0;
                        layer.alert(res.msg, {
                            icon: 2
                        });
                    }
                    addConfirmLog(log);
                }
            });
        }
    });
}

function exportExcel(treeNode) {
    var log = {};
    log.operation = "结构树导出Excel";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出Excel";

    var loading;
    $.fileDownload(getReqUrl("tree", "ExportMoreExcel"), {
        httpMethod: 'POST',
        data: {
            "id": treeNode['ID'],
            "pid": treeNode['PID']
        },
        prepareCallback: function () {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function () {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出失败！！");
        }
    });
}

function workHours(treeNode) {
    var log = {};
    log.operation = "结构树统计工时";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上统计工时";

    var loading;
    $.fileDownload(getReqUrl("tree", "ExportWorkHours"), {
        httpMethod: 'POST',
        data: {
            "id": treeNode['ID'],
            "name": treeNode['NAME'] + "工时统计"
        },
        prepareCallback: function () {
            loading = layer.msg("正在统计中...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function () {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function (responseHtml, url, error) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            try {
                layer.msg(JSON.parse($(responseHtml)[0].innerText).msg);
            } catch (e) {
                layer.msg("导出失败！！");
            }

        }
    });
}

/**
 * 导出数据包
 */
function exportZip(treeNode) {
    var log = {};
    log.operation = "导出数据包";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出数据包";
    var loading;
    $.fileDownload(getReqUrl('tree', 'ExportZip'), {
        httpMethod: 'POST',
        data: {
            "id": treeNode['ID'],
            "name": treeNode['NAME']
        },
        prepareCallback: function () {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function () {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出失败！！");
        }
    });
}

function exportPdf(treeNode) {
    var log = {};
    log.operation = "结构树导出PDF";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出PDF";
    var loading;
    $.fileDownload(getReqUrl('tree', 'ExportMorePdf'), {
        httpMethod: 'POST',
        data: {
            "id": treeNode['ID'],
            "pid": treeNode['PID']
        },
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            layer.close(loading);
            log.reqResult = 0;
            layer.msg("导出异常！！");
            addConfirmLog(log);
        },
        successCallback: function (url) {
            log.reqResult = 1;
            layer.close(loading);
            addConfirmLog(log);
        },
        failCallback: function (html, url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.alert(html, {
                icon: 2
            })
        }
    });
}

function exportPdfZip(treeNode) {
    var log = {};
    log.operation = "导出PDF压缩包";
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导出PDF压缩包";
    var loading;
    $.fileDownload(getReqUrl("tree", "ExportPdfZip"), {
        httpMethod: 'POST',
        data: {
            "id": treeNode.ID,
            "name": treeNode.NAME
        },
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function (url) {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function (html, url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.alert(html, {
                icon: 2
            })
        }
    });
}

function importZip(treeNode) {
    layer.confirm("导入数据包会覆盖点该节点的原有数据，是否继续？", {
        icon: 3,
        title: '提示'
    }, function (index) {
        var log = {};
        log.operation = "导入数据包";
        log.tablePid = treeNode['PID'];
        log.tableId = treeNode['ID'];

        var fileFlag = false;
        layer.close(index);
        layer.open({
            title: "导入数据包",
            type: 1,
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            shadeClose: false,
            // fixed: false,
            maxmin: false,
            resize: false, //不允许拉伸
            area: ['350px', '220px'],
            content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
            btn: ['确认', '取消'],
            yes: function () {
                if (!fileFlag) {
                    layer.alert('请选择需要导入的数据文件!', {
                        icon: 2
                    });
                    return false;
                }
                $('#uploadStart').click();
            },
            btn2: function () {
                layer.closeAll();
            },
            success: function () {
                var addTpl = $("#uploadHtml")[0].innerHTML;
                $("#uploadContent").append(addTpl);
            }
        });
        form.render(null, 'uploadForm');

        var uploadInst = upload.render({
            elem: '#uploadChoice',
            url: getReqUrl('tree', 'ImportZip', '&id=' + treeNode['ID'] + '&pid=' + treeNode['PID'] + '&type=' + treeNode['TYPE']),
            auto: false,
            accept: 'file',
            field: 'uploadFile',
            bindAction: '#uploadStart',
            dataType: "json",
            choose: function (obj) {
                fileFlag = true;
                var o = obj.pushFile();
                var fileName = '';
                for (var k in o) {
                    var file = o[k];
                    fileName = file.name;
                }
                $("#selectedFile").show();
                $("#selectedFileName").text(fileName);
                log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导入数据包(" + fileName + ")";
            },
            before: function () {
                layer.load();
            },
            done: function (res) {
                layer.closeAll();
                if (res.success) {
                    log.reqResult = 1;
                    layer.msg("导入成功");
                    reloadTree(treeNode['PID'], res['rootId']);
                } else {
                    log.reqResult = 0;
                    layer.alert(res.msg, {icon: 2});
                }
                addConfirmLog(log);
            }
        });
    });
}

function importBigZip(treeNode) {
    layer.confirm("导入数据包会覆盖点该节点的原有数据，是否继续？", {
        icon: 3,
        title: '提示'
    }, function (index) {
        var log = {};
        log.operation = "导入数据包";
        log.tablePid = treeNode['PID'];
        log.tableId = treeNode['ID'];
        var loadIndex = 0;
        var fileFlag = false;
        layer.close(index);
        layer.open({
            title: "导入数据包",
            type: 1,
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            shadeClose: false,
            // fixed: false,
            maxmin: false,
            resize: false, //不允许拉伸
            area: ['350px', '240px'],
            content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
            btn: ['确认', '取消'],
            yes: function () {
                if (!fileFlag) {
                    layer.alert('请选择需要导入的数据文件!', {
                        icon: 2
                    });
                    return false;
                }
                $("#uploadStart").click();
            },
            btn2: function () {
                layer.closeAll();
            },
            success: function () {
                var addTpl = ` <form class="layui-form" lay-filter="uploadForm">
                                <div class="layui-form-item">
                                    <label class="fieldlabel layui-form-label">文件内容:</label>
                                    <div class="layui-input-block">
                                        <div class="layui-upload">
                                            <div id="uploadChoice">选择文件</div>
                                            <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" id="selectedFile" style="display: none;">
                                    <label class="fieldlabel layui-form-label">已选文件:</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="display:none;">
                                    <center>
                                        <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
                                        <button id="btn_cancel" class="layui-btn">取消</button>
                                    </center>
                                </div>
                            </form>`;
                $("#uploadContent").append(addTpl);
            }
        });
        form.render(null, 'uploadForm');
        var reqIdent = new Date().getTime();
        var uploader = WebUploader.create({
            // 选完文件后，是否自动上传。
            auto: false,
            // 文件接收服务端。
            server: getReqUrl("tree", "ImportBigZip"),
            // 选择文件的按钮。可选。
            pick: {
                id: '#uploadChoice',
                multiple: false // 设置multiple为false
            },
            timeout: 60 * 60 * 1000,//请求超时时间
            // 配置分片上传
            chunked: true,
            threads: 3, //上传并发数。允许同时最大上传进程数。
            chunkSize: 10 * 1024 * 1024,//分段大小为10Mb
            fileNumLimit: 1,
            formData: {
                reqIdent: reqIdent,
                extraData: JSON.stringify({
                    username: sessionStorage.getItem("username"),
                    tableId: treeNode['ID'],
                    tablePId: treeNode['PID'],
                    srcType: treeNode['TYPE']
                })
            }
        });
        uploader.on('uploadBeforeSend', function (object, data, headers) {

            console.log('uploadBeforeSend:' + loadIndex);
        });

        // 当有文件被添加进队列之前触发
        uploader.on('beforeFileQueued', function (file) {
            // 检查队列中是否已经有文件
            if (uploader.getFiles().length > 0) {
                // 如果有文件，先移除旧的文件
                uploader.removeFile(uploader.getFiles()[0], true);
            }
        });

        // 当有文件被添加进队列的时候
        uploader.on('fileQueued', function (file) {
            fileFlag = true;
            $("#selectedFile").show();
            $("#selectedFileName").text(file.name);
            log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导入数据包(" + file.name + ")";
        });

        uploader.on('uploadSuccess', function (file, res) {
            if (res.success) {
                log.reqResult = 1;
                layer.closeAll();
                reloadTree(treeNode['PID'], res['rootId']);
                layer.msg("导入成功");
            } else {
                log.reqResult = 0;
                layer.alert(res.msg, {icon: 2});
            }
        });

        // 文件上传失败，显示上传出错。
        uploader.on('uploadError', function (file) {
            log.reqResult = 0;
        });

        // 完成上传完毕，成功或者失败，先删除进度条。
        uploader.on('uploadComplete', function (file) {
            addConfirmLog(log);
        });

        // 当所有文件上传结束时触发
        uploader.on('uploadFinished', function () {
            layer.close(loadIndex);
        });

        $("#uploadStart").on('click', function () {
            loadIndex = layer.msg('导入中', {
                icon: 16,
                shade: 0.01,
                time: 0
            });
            uploader.upload(); // 手动触发上传操作
        });
    });
}

function reqGenerateFile(treeNode, type) {
    var log = {};
    if (type === 1) {
        log.operation = "导出PDF";
    } else if (type === 4) {
        log.operation = "导出数据包";
    }
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" + log.operation;
    log.reqResult = 1;
    addConfirmLog(log);
    postAjaxNoRes('tree', 'ReqGenerateFile', {
        tableId: treeNode['ID'],
        tablePId: treeNode['PID'],
        exportType: type,
        creator: sessionStorage.getItem("username")
    });
    layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");
}

/**
 * 移动节点 转移节点
 * @param treeNode
 */
function moveNode(treeNode) {
    var parentNode = treeNode.getParentNode();
    var moveZtreeObj;
    layer.open({
        title: "移动节点",
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false,
        area: ['500px', '600px'],
        content: '<div id="moveNodeContent" style="padding-top: 15px;padding-right: 15px;"><ul id="moveTree" class="ztree"></ul></div>',
        btn: ['确定', '取消'],
        yes: function () {
            var checkedNodes = moveZtreeObj.getCheckedNodes(true);
            if (checkedNodes.length == 0) {
                layer.alert("请选择目标节点！", {icon: 2});
            } else {
                var targetNode = checkedNodes[0];
                var targetId = targetNode['ID'];
                var log = {};
                log.operation = '移动节点';
                log.tablePid = treeNode['PID'];
                log.tableId = treeNode['ID'];
                log.content = "将节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】移动到节点【" + targetNode['NAME'] + "（" + targetId + "）】下";
                postAjax("tree", 'MoveNode', {
                        id: treeNode['ID'],
                        targetId: targetId
                    }, true,
                    function (res) {
                        layer.closeAll();
                        ztreeObj.reAsyncChildNodes(parentNode.getParentNode(), 'refresh', false,
                            function () {
                                var nodeByParam = ztreeObj.getNodeByParam("ID", targetNode.getParentNode()['ID'], null);
                                if (nodeByParam !== null) {
                                    ztreeObj.reAsyncChildNodes(nodeByParam, 'refresh', false,
                                        function () {
                                            loadTreeMenu();
                                        });
                                }
                            });

                        log.reqResult = 1;
                        addConfirmLog(log);
                    }, function () {
                        log.reqResult = 0;
                        addConfirmLog(log);
                    });
            }
        },
        btn2: function () {
            return true;
        },
        success: function (layero, index, that) {
            var cb_success = function (datas) {
                datas = dealMoveTreeData(datas, parentNode);
                treeSetting.check = {
                    enable: true,
                    chkStyle: "radio",
                    radioType: "all"
                };
                treeSetting.async.dataFilter = function (treeId, parentNode1, responseData) {
                    if (responseData.success) {
                        var datas = responseData.data;
                        if (datas.length > 0) {
                            datas = dealMoveTreeData(datas, parentNode);
                        }
                        return datas;
                    } else {
                        layer.alert(responseData.msg, {
                            icon: 2
                        });
                    }
                };
                treeSetting.edit.enable = false;
                treeSetting.callback.onExpand = function (event, treeId, treeNode) {
                };

                treeSetting.callback.onClick = function (event, treeId, treeNode) {
                };
                moveZtreeObj = $.fn.zTree.init($("#moveTree"), treeSetting, datas);
                var node = moveZtreeObj.getNodeByParam("LEVEL_NUM", 0, null);
                moveZtreeObj.expandNode(node, true, false, true);
            };
            //使用ajax进行异步加载Tree
            postAjax("tree", 'QueryTreeRoot', {}, false, cb_success);
        }
    });
}

function batchImportExcel(treeNode, type) {
    var typeInfo = getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level;
    var domHtml = document.getElementById('many-upload').innerHTML;
    layer.open({
        title: '批量导入' + nodeTypeNameLabel,
        type: 1,
        area: ['1100px', '570px'],
        content: domHtml,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        btn: ['上传', '关闭'],
        yes: function () {
            $("#manyUploadStart").click();
        },
        btn2: function () {
            table.cache["file-table"] = [];
            return true;
        },
        success: function () {

            var fileTable = table.render({
                elem: '#file-table',
                data: [],
                height: 375,
                limit: 999,
                cols: [
                    [{
                        field: '',
                        type: 'numbers',
                        width: 80
                    }, {
                        field: 'tableNum',
                        title: nodeTypeNameLabel + '序号',
                        templet: '<div>{{d.tableNum}}</div>',
                        edit: 'text',
                        width: 200
                    }, {
                        field: 'tableName',
                        title: nodeTypeNameLabel + '名称',
                        templet: '<div>{{d.tableName}}</div>',
                        edit: 'text'
                    }, {
                        field: 'fileSize',
                        title: '文件大小',
                        templet: '<div>{{d.fileSize}}</div>',
                        width: 100
                    }, {
                        field: 'operate',
                        title: '操作(双击)',
                        width: 100,
                        templet: `<div class="operate">
                                            <a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>
                                          </div>`
                    }]
                ],
                done: function (res, curr, count) {

                }
            });
            table.on('toolDouble(file-table)', function (obj) {
                var data = obj.data; // 得到当前行数据
                var layEvent = obj.event; // 获得元素对应的 lay-event 属性值
                if (layEvent === 'del') { //删除
                    layer.confirm('确定删除吗？', function (index) {
                        obj.del();
                        uploader.removeFile(data.file);
                        layer.close(index);
                    });
                }
            });

            var doneDatas = [];
            var uploader = WebUploader.create({
                // 选完文件后，是否自动上传。
                auto: false,
                // 文件接收服务端。
                server: getReqUrl('table', 'BatchImportTable'),
                // 选择文件的按钮。可选。
                pick: {
                    id: '#chooseFile',
                    multiple: true
                },
                accept: {
                    title: 'Excel文件',
                    extensions: 'xlsx',
                    mimeTypes: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                timeout: 10 * 60 * 1000,
                threads: 1,
                // 配置分片上传
                formData: {
                    extraData: JSON.stringify({})
                }
            });
            uploader.on('uploadBeforeSend', function (object, data, headers) {
                var uid = object.blob.uid;
                var tableData = table.cache["file-table"];
                var tableNum = "", tableName = "";
                for (var i = 0; i < tableData.length; i++) {
                    if (tableData[i].uid == uid) {
                        tableNum = tableData[i].tableNum;
                        tableName = tableData[i].tableName;
                        break;
                    }
                }
                data.extraData = JSON.stringify({
                    pid: treeNode['ID'],
                    type: type,
                    level: level,
                    tableNum: tableNum,
                    tableName: tableName,
                    saveUser: sessionStorage.getItem("username")
                });
            });


            // 当有文件被添加进队列之前触发
            uploader.on('beforeFileQueued', function (file) {

            });
            // 当有文件被添加进队列的时候
            uploader.on('fileQueued', function (file) {
                var fileName = file.name;
                var o = {};
                fileName = fileName.replace("：", ":");
                var tableNum = "";
                if (fileName.indexOf(":") > -1) {
                    tableNum = fileName.split(":")[0];
                    fileName = fileName.split(":")[1];
                }
                if (fileName.indexOf(".") > -1) {
                    fileName = fileName.split(".")[0];
                }
                o.tableNum = tableNum;
                o.tableName = fileName;
                o.fileSize = WebUploader.formatSize(file.size);
                o.uid = file.source.uid;
                o.file = file;
                table.cache["file-table"].push(o);
                table.renderData('file-table');
            });
            uploader.on('filesQueued', function (files) {

            });
            uploader.on('uploadProgress', function (file, percentage) {

            })


            uploader.on('uploadSuccess', function (file, res) {
                console.log(res);
            });

            // 文件上传失败，显示上传出错。
            uploader.on('uploadError', function (file) {

            });

            // 完成上传完毕，成功或者失败，先删除进度条。
            uploader.on('uploadComplete', function (file) {

            });

            // 当所有文件上传结束时触发
            uploader.on('uploadFinished', function () {
                reloadTree(treeNode.ID, treeNode.ID);
                //提示完成后，点击确定再刷新界面
                layer.closeAll();
                table.cache["file-table"] = [];
                layer.msg('导入成功');
            });

            $("#manyUploadStart").on('click', function () {
                var tableData = table.cache["file-table"];
                if (tableData.length > 0) {
                    var hasEmpty = false;
                    for (var i = 0; i < tableData.length; i++) {
                        if (tableData[i].tableNum == '' || tableData[i].tableName == '') {
                            hasEmpty = true;
                            break;
                        }
                    }
                    if (hasEmpty) {
                        layer.alert('表序号和表名称都不能为空！', {icon: 2});
                    } else {
                        layer.load();
                        uploader.upload(); // 手动触发上传操作
                    }
                } else {
                    layer.alert('请选择需要上传的Excel文件！', {icon: 2});
                }
            });
        }
    });
}

function selectWorkType(treeNode) {
    var addTpl = '<form class="layui-form" action="" lay-filter="selectWorkTypeForm">\
						<div class="layui-form-item">\
							<label class="layui-form-label">节点名称:</label>\
							<div class="layui-input-block">\
								<input type="text" readOnly="readOnly" value="' + treeNode['TABLE_NUM'] + ':' + treeNode['NAME'] + '" class="layui-input">\
							</div>\
						</div>\
						<div class="layui-form-item" style="">\
							<label class="layui-form-label">工作类型:</label>\
							<div class="layui-input-block">\
								<select name="workType" id="workType" lay-filter="workType">\
									<option value=""></option>\
								</select>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="selectWorkTypeSubmit" lay-submit="" lay-filter="selectWorkTypeSubmit">确定</button>\
								</div>\
							</div>\
						</div>\
					</form>';
    var log = {};
    log.operation = '选择工作类型';
    log.tablePid = treeNode['PID'];
    log.tableId = treeNode['ID'];

    var layerIndex = layer.open({
        title: '选择工作类型',
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false,
        area: ['500px', '220px'],
        content: '<div id="selectWorkTypeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确定', '取消'],
        yes: function () {
            $('#selectWorkTypeSubmit').click();
        },
        btn2: function () {
            return true;
        },
        success: function () {
            $("#selectWorkTypeContent").append(addTpl);
            $("#selectWorkTypeContent").parent().css("overflow", "visible");
            var datas = HotUtil.workTypes;
            for (var i = 0; i < datas.length; i++) {
                var workType = datas[i];
                var selWorkType = "";
                if (treeNode['SAVE_DATA']) {
                    selWorkType = JSON.parse(treeNode['SAVE_DATA']).workType;
                }
                if (workType === selWorkType) {
                    $("#workType").append('<option selected value="' + workType + '">' + workType + '</option>');
                } else {
                    $("#workType").append('<option value="' + workType + '">' + workType + '</option>');
                }
            }
        }
    });
    form.render(null, 'selectWorkTypeForm');
    form.on('submit(selectWorkTypeSubmit)',
        function (data) {
            var param = data.field;
            param.id = treeNode['ID'];
            var workType = $("#workType option:selected").text();
            log.content = "将节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】的工作类型调整为【" + workType + "】";
            postAjax("tree", 'SelectWorkType', param, true,
                function () {
                    reloadTree(treeNode['PID'], treeNode['ID']);
                    layer.close(layerIndex);
                    log.reqResult = 1;
                    addConfirmLog(log);
                }, function () {
                    log.reqResult = 0;
                    addConfirmLog(log);
                });
            return false;
        });
}

function getNodeMenu(treeNode) {
    var type = treeNode['TYPE'];
    var menus = [];

    var addFolderMenu = {
        text: "添加分类",
        icon: imgPath + 'add.png',
        callback: function () {
            addTableNode(treeNode, 'folder')
        }
    };

    var selectFolderMenu = {
        text: "选择分类",
        icon: imgPath + 'folder1.png',
        callback: function () {
            selectFolder(treeNode);
        }
    }
    var addModelMenu = {
        text: "添加型号",
        icon: imgPath + 'add.png',
        callback: function () {
            addTableNode(treeNode, 'model')
        }
    };

    var copyNodeMenu = {
        text: "复制节点",
        icon: imgPath + 'copy.png',
        callback: function () {
            addTableNode(treeNode, treeNode['TYPE'], true);
        }
    };

    var moveNodeMenu = {
        text: "移动节点",
        icon: imgPath + 'transfer.png',
        callback: function () {
            moveNode(treeNode);
        }
    };

    var addProjectMenu = {
        text: "添加工作项目",
        icon: imgPath + 'add.png',
        callback: function () {
            addTableNode(treeNode, 'project');
        }
    };

    var selectWorkTypeMenu = {
        text: "设置工作类型",
        icon: imgPath + 'workType.png',
        callback: function () {
            selectWorkType(treeNode);
        }
    };

    var addATable = {
        text: "添加A表",
        icon: imgPath + 'add.png',
        callback: function () {
            addTableNode(treeNode, 'a');
        }
    };

    var importATable = {
        text: "导入A表",
        icon: imgPath + 'excel-upload.png',
        callback: function () {
            importTableNode(treeNode, 'a');

        }
    };

    var batchImportATable = {
        text: "批量导入A表",
        icon: imgPath + 'excel-upload.png',
        callback: function () {
            batchImportExcel(treeNode, 'a');
        }
    };

    var addBTable = {
        text: "添加B表",
        icon: imgPath + 'add.png',
        callback: function () {
            addTableNode(treeNode, 'b');
        }
    };

    var importBTable = {
        text: "导入B表",
        icon: imgPath + 'excel-upload.png',
        callback: function () {
            importTableNode(treeNode, 'b');
        }
    };
    var batchImportBTable = {
        text: "批量导入B表",
        icon: imgPath + 'excel-upload.png',
        callback: function () {
            batchImportExcel(treeNode, 'b');
        }
    };

    var editNodeMenu = {
        text: "编辑节点",
        icon: imgPath + 'edit.png',
        callback: function () {
            editTableNode(treeNode);
        }
    };

    var deleteNodeMenu = {
        text: "删除节点",
        icon: imgPath + 'remove.png',
        callback: function () {
            deleteTableNode(treeNode);
        }
    };

    var pdfNodeMenu = {
        text: "导出PDF",
        icon: imgPath + 'pdf.png',
        callback: function () {
            // exportPdf(treeNode);
            reqGenerateFile(treeNode, 1);
        }

    };


    var pdfZipNodeMenu = {
        text: "导出PDF压缩包",
        icon: imgPath + 'pdf-zip.png',
        callback: function () {
            exportPdfZip(treeNode);
        }
    }

    var excelNodeMenu = {
        text: "导出Excel",
        icon: imgPath + 'excel.png',
        callback: function () {
            exportExcel(treeNode);
        }
    };

    var workHourMenu = {
        text: "统计工时",
        icon: imgPath + 'statistics.png',
        callback: function () {
            workHours(treeNode);
        }
    };

    var exportZipMenu = {
        text: "导出数据包",
        icon: imgPath + 'export-data.png',
        callback: function () {
            // exportZip(treeNode);
            reqGenerateFile(treeNode, 4);
        }
    };


    var importZipMenu = {
        text: "导入数据包",
        icon: imgPath + 'import-data.png',
        callback: function () {
            // importZip(treeNode);
            importBigZip(treeNode);
        }
    };


    if (type === 'root') {
        menus.push(addFolderMenu);
    }

    if (type === 'folder') {
        menus.push(addModelMenu);
        menus.push(editNodeMenu);
        if (!treeNode['ISPARENT']) {
            menus.push(deleteNodeMenu);
        }
    }

    if (type === 'model') {
        // menus.push(selectFolderMenu);
        menus.push(addProjectMenu);
        menus.push(editNodeMenu);
        menus.push(deleteNodeMenu);
        menus.push(copyNodeMenu);
        menus.push(moveNodeMenu);
        menus.push(pdfNodeMenu);
        menus.push(pdfZipNodeMenu);
        menus.push(excelNodeMenu);
        // menus.push(workHourMenu);
        menus.push(exportZipMenu);
        menus.push(importZipMenu);
    }
    if (type === 'project') {
        menus.push(addATable);
        menus.push(importATable);
        menus.push(batchImportATable);
        menus.push(editNodeMenu);
        menus.push(deleteNodeMenu);
        menus.push(copyNodeMenu);
        menus.push(moveNodeMenu);
        menus.push(pdfNodeMenu);
        menus.push(pdfZipNodeMenu);
        menus.push(excelNodeMenu);
        // menus.push(workHourMenu);
        menus.push(exportZipMenu);
        menus.push(importZipMenu);
    }
    if (type === 'a') {
        menus.push(addBTable);
        menus.push(importBTable);
        menus.push(batchImportBTable);
        menus.push(editNodeMenu);
        menus.push(deleteNodeMenu);
        menus.push(copyNodeMenu);
        menus.push(moveNodeMenu);
        menus.push(pdfNodeMenu);
        menus.push(pdfZipNodeMenu);
        menus.push(excelNodeMenu);
        // menus.push(workHourMenu);
        menus.push(exportZipMenu);
        menus.push(importZipMenu);
    }
    if (type === 'b') {
        menus.push(editNodeMenu);
        menus.push(deleteNodeMenu);
        menus.push(copyNodeMenu);
        // menus.push(selectWorkTypeMenu);
        menus.push(moveNodeMenu);
        // menus.push(workHourMenu);
        menus.push(exportZipMenu);
        menus.push(importZipMenu);
    }
    return menus;
}

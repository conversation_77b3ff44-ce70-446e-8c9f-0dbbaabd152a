!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro=e()}}((function(){return function e(t,n,r){function i(o,u){if(!n[o]){if(!t[o]){var c="function"==typeof require&&require;if(!u&&c)return c(o,!0);if(a)return a(o,!0);var s=new Error("Cannot find module '"+o+"'");throw s.code="MODULE_NOT_FOUND",s}var l=n[o]={exports:{}};t[o][0].call(l.exports,(function(e){return i(t[o][1][e]||e)}),l,l.exports,e,t,n,r)}return n[o].exports}for(var a="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){!function(e){"use strict";var n,r=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,i=Math.ceil,a=Math.floor,o="[BigNumber Error] ",u=o+"Number primitive has more than 15 significant digits: ",c=1e14,s=14,l=9007199254740991,f=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],p=1e7,g=1e9;function h(e){var t=0|e;return e>0||e===t?t:t-1}function d(e){for(var t,n,r=1,i=e.length,a=e[0]+"";r<i;){for(t=e[r++]+"",n=s-t.length;n--;t="0"+t);a+=t}for(i=a.length;48===a.charCodeAt(--i););return a.slice(0,i+1||1)}function v(e,t){var n,r,i=e.c,a=t.c,o=e.s,u=t.s,c=e.e,s=t.e;if(!o||!u)return null;if(n=i&&!i[0],r=a&&!a[0],n||r)return n?r?0:-u:o;if(o!=u)return o;if(n=o<0,r=c==s,!i||!a)return r?0:!i^n?1:-1;if(!r)return c>s^n?1:-1;for(u=(c=i.length)<(s=a.length)?c:s,o=0;o<u;o++)if(i[o]!=a[o])return i[o]>a[o]^n?1:-1;return c==s?0:c>s^n?1:-1}function m(e,t,n,r){if(e<t||e>n||e!==(e<0?i(e):a(e)))throw Error(o+(r||"Argument")+("number"==typeof e?e<t||e>n?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function y(e){var t=e.c.length-1;return h(e.e/s)==t&&e.c[t]%2!=0}function b(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function w(e,t,n){var r,i;if(t<0){for(i=n+".";++t;i+=n);e=i+e}else if(++t>(r=e.length)){for(i=n,t-=r;--t;i+=n);e+=i}else t<r&&(e=e.slice(0,t)+"."+e.slice(t));return e}n=function e(t){var n,O,x,S,N,M,B,D,A,E,F=V.prototype={constructor:V,toString:null,valueOf:null},k=new V(1),_=20,L=4,T=-7,P=21,U=-1e7,j=1e7,C=!1,R=1,I=0,$={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},G="0123456789abcdefghijklmnopqrstuvwxyz";function V(e,t){var n,i,o,c,f,p,g,h,d=this;if(!(d instanceof V))return new V(e,t);if(null==t){if(e instanceof V)return d.s=e.s,d.e=e.e,void(d.c=(e=e.c)?e.slice():e);if((p="number"==typeof e)&&0*e==0){if(d.s=1/e<0?(e=-e,-1):1,e===~~e){for(c=0,f=e;f>=10;f/=10,c++);return d.e=c,void(d.c=[e])}h=String(e)}else{if(h=String(e),!r.test(h))return x(d,h,p);d.s=45==h.charCodeAt(0)?(h=h.slice(1),-1):1}(c=h.indexOf("."))>-1&&(h=h.replace(".","")),(f=h.search(/e/i))>0?(c<0&&(c=f),c+=+h.slice(f+1),h=h.substring(0,f)):c<0&&(c=h.length)}else{if(m(t,2,G.length,"Base"),h=String(e),10==t)return W(d=new V(e instanceof V?e:h),_+d.e+1,L);if(p="number"==typeof e){if(0*e!=0)return x(d,h,p,t);if(d.s=1/e<0?(h=h.slice(1),-1):1,V.DEBUG&&h.replace(/^0\.0*|\./,"").length>15)throw Error(u+e);p=!1}else d.s=45===h.charCodeAt(0)?(h=h.slice(1),-1):1;for(n=G.slice(0,t),c=f=0,g=h.length;f<g;f++)if(n.indexOf(i=h.charAt(f))<0){if("."==i){if(f>c){c=g;continue}}else if(!o&&(h==h.toUpperCase()&&(h=h.toLowerCase())||h==h.toLowerCase()&&(h=h.toUpperCase()))){o=!0,f=-1,c=0;continue}return x(d,String(e),p,t)}(c=(h=O(h,t,10,d.s)).indexOf("."))>-1?h=h.replace(".",""):c=h.length}for(f=0;48===h.charCodeAt(f);f++);for(g=h.length;48===h.charCodeAt(--g););if(h=h.slice(f,++g)){if(g-=f,p&&V.DEBUG&&g>15&&(e>l||e!==a(e)))throw Error(u+d.s*e);if((c=c-f-1)>j)d.c=d.e=null;else if(c<U)d.c=[d.e=0];else{if(d.e=c,d.c=[],f=(c+1)%s,c<0&&(f+=s),f<g){for(f&&d.c.push(+h.slice(0,f)),g-=s;f<g;)d.c.push(+h.slice(f,f+=s));h=h.slice(f),f=s-h.length}else f-=g;for(;f--;h+="0");d.c.push(+h)}}else d.c=[d.e=0]}function q(e,t,n,r){var i,a,o,u,c;if(null==n?n=L:m(n,0,8),!e.c)return e.toString();if(i=e.c[0],o=e.e,null==t)c=d(e.c),c=1==r||2==r&&(o<=T||o>=P)?b(c,o):w(c,o,"0");else if(a=(e=W(new V(e),t,n)).e,u=(c=d(e.c)).length,1==r||2==r&&(t<=a||a<=T)){for(;u<t;c+="0",u++);c=b(c,a)}else if(t-=o,c=w(c,a,"0"),a+1>u){if(--t>0)for(c+=".";t--;c+="0");}else if((t+=a-u)>0)for(a+1==u&&(c+=".");t--;c+="0");return e.s<0&&i?"-"+c:c}function Z(e,t){for(var n,r=1,i=new V(e[0]);r<e.length;r++){if(!(n=new V(e[r])).s){i=n;break}t.call(i,n)&&(i=n)}return i}function z(e,t,n){for(var r=1,i=t.length;!t[--i];t.pop());for(i=t[0];i>=10;i/=10,r++);return(n=r+n*s-1)>j?e.c=e.e=null:n<U?e.c=[e.e=0]:(e.e=n,e.c=t),e}function W(e,t,n,r){var o,u,l,p,g,h,d,v=e.c,m=f;if(v){e:{for(o=1,p=v[0];p>=10;p/=10,o++);if((u=t-o)<0)u+=s,l=t,d=(g=v[h=0])/m[o-l-1]%10|0;else if((h=i((u+1)/s))>=v.length){if(!r)break e;for(;v.length<=h;v.push(0));g=d=0,o=1,l=(u%=s)-s+1}else{for(g=p=v[h],o=1;p>=10;p/=10,o++);d=(l=(u%=s)-s+o)<0?0:g/m[o-l-1]%10|0}if(r=r||t<0||null!=v[h+1]||(l<0?g:g%m[o-l-1]),r=n<4?(d||r)&&(0==n||n==(e.s<0?3:2)):d>5||5==d&&(4==n||r||6==n&&(u>0?l>0?g/m[o-l]:0:v[h-1])%10&1||n==(e.s<0?8:7)),t<1||!v[0])return v.length=0,r?(t-=e.e+1,v[0]=m[(s-t%s)%s],e.e=-t||0):v[0]=e.e=0,e;if(0==u?(v.length=h,p=1,h--):(v.length=h+1,p=m[s-u],v[h]=l>0?a(g/m[o-l]%m[l])*p:0),r)for(;;){if(0==h){for(u=1,l=v[0];l>=10;l/=10,u++);for(l=v[0]+=p,p=1;l>=10;l/=10,p++);u!=p&&(e.e++,v[0]==c&&(v[0]=1));break}if(v[h]+=p,v[h]!=c)break;v[h--]=0,p=1}for(u=v.length;0===v[--u];v.pop());}e.e>j?e.c=e.e=null:e.e<U&&(e.c=[e.e=0])}return e}function H(e){var t,n=e.e;return null===n?e.toString():(t=d(e.c),t=n<=T||n>=P?b(t,n):w(t,n,"0"),e.s<0?"-"+t:t)}return V.clone=e,V.ROUND_UP=0,V.ROUND_DOWN=1,V.ROUND_CEIL=2,V.ROUND_FLOOR=3,V.ROUND_HALF_UP=4,V.ROUND_HALF_DOWN=5,V.ROUND_HALF_EVEN=6,V.ROUND_HALF_CEIL=7,V.ROUND_HALF_FLOOR=8,V.EUCLID=9,V.config=V.set=function(e){var t,n;if(null!=e){if("object"!=typeof e)throw Error(o+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(m(n=e[t],0,g,t),_=n),e.hasOwnProperty(t="ROUNDING_MODE")&&(m(n=e[t],0,8,t),L=n),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((n=e[t])&&n.pop?(m(n[0],-g,0,t),m(n[1],0,g,t),T=n[0],P=n[1]):(m(n,-g,g,t),T=-(P=n<0?-n:n))),e.hasOwnProperty(t="RANGE"))if((n=e[t])&&n.pop)m(n[0],-g,-1,t),m(n[1],1,g,t),U=n[0],j=n[1];else{if(m(n,-g,g,t),!n)throw Error(o+t+" cannot be zero: "+n);U=-(j=n<0?-n:n)}if(e.hasOwnProperty(t="CRYPTO")){if((n=e[t])!==!!n)throw Error(o+t+" not true or false: "+n);if(n){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw C=!n,Error(o+"crypto unavailable");C=n}else C=n}if(e.hasOwnProperty(t="MODULO_MODE")&&(m(n=e[t],0,9,t),R=n),e.hasOwnProperty(t="POW_PRECISION")&&(m(n=e[t],0,g,t),I=n),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(n=e[t]))throw Error(o+t+" not an object: "+n);$=n}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(n=e[t])||/^.$|[+-.\s]|(.).*\1/.test(n))throw Error(o+t+" invalid: "+n);G=n}}return{DECIMAL_PLACES:_,ROUNDING_MODE:L,EXPONENTIAL_AT:[T,P],RANGE:[U,j],CRYPTO:C,MODULO_MODE:R,POW_PRECISION:I,FORMAT:$,ALPHABET:G}},V.isBigNumber=function(e){return e instanceof V||e&&!0===e._isBigNumber||!1},V.maximum=V.max=function(){return Z(arguments,F.lt)},V.minimum=V.min=function(){return Z(arguments,F.gt)},V.random=(S=9007199254740992,N=Math.random()*S&2097151?function(){return a(Math.random()*S)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,n,r,u,c,l=0,p=[],h=new V(k);if(null==e?e=_:m(e,0,g),u=i(e/s),C)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(u*=2));l<u;)(c=131072*t[l]+(t[l+1]>>>11))>=9e15?(n=crypto.getRandomValues(new Uint32Array(2)),t[l]=n[0],t[l+1]=n[1]):(p.push(c%1e14),l+=2);l=u/2}else{if(!crypto.randomBytes)throw C=!1,Error(o+"crypto unavailable");for(t=crypto.randomBytes(u*=7);l<u;)(c=281474976710656*(31&t[l])+1099511627776*t[l+1]+4294967296*t[l+2]+16777216*t[l+3]+(t[l+4]<<16)+(t[l+5]<<8)+t[l+6])>=9e15?crypto.randomBytes(7).copy(t,l):(p.push(c%1e14),l+=7);l=u/7}if(!C)for(;l<u;)(c=N())<9e15&&(p[l++]=c%1e14);for(u=p[--l],e%=s,u&&e&&(c=f[s-e],p[l]=a(u/c)*c);0===p[l];p.pop(),l--);if(l<0)p=[r=0];else{for(r=-1;0===p[0];p.splice(0,1),r-=s);for(l=1,c=p[0];c>=10;c/=10,l++);l<s&&(r-=s-l)}return h.e=r,h.c=p,h}),V.sum=function(){for(var e=1,t=arguments,n=new V(t[0]);e<t.length;)n=n.plus(t[e++]);return n},O=function(){var e="0123456789";function t(e,t,n,r){for(var i,a,o=[0],u=0,c=e.length;u<c;){for(a=o.length;a--;o[a]*=t);for(o[0]+=r.indexOf(e.charAt(u++)),i=0;i<o.length;i++)o[i]>n-1&&(null==o[i+1]&&(o[i+1]=0),o[i+1]+=o[i]/n|0,o[i]%=n)}return o.reverse()}return function(r,i,a,o,u){var c,s,l,f,p,g,h,v,m=r.indexOf("."),y=_,b=L;for(m>=0&&(f=I,I=0,r=r.replace(".",""),g=(v=new V(i)).pow(r.length-m),I=f,v.c=t(w(d(g.c),g.e,"0"),10,a,e),v.e=v.c.length),l=f=(h=t(r,i,a,u?(c=G,e):(c=e,G))).length;0==h[--f];h.pop());if(!h[0])return c.charAt(0);if(m<0?--l:(g.c=h,g.e=l,g.s=o,h=(g=n(g,v,y,b,a)).c,p=g.r,l=g.e),m=h[s=l+y+1],f=a/2,p=p||s<0||null!=h[s+1],p=b<4?(null!=m||p)&&(0==b||b==(g.s<0?3:2)):m>f||m==f&&(4==b||p||6==b&&1&h[s-1]||b==(g.s<0?8:7)),s<1||!h[0])r=p?w(c.charAt(1),-y,c.charAt(0)):c.charAt(0);else{if(h.length=s,p)for(--a;++h[--s]>a;)h[s]=0,s||(++l,h=[1].concat(h));for(f=h.length;!h[--f];);for(m=0,r="";m<=f;r+=c.charAt(h[m++]));r=w(r,l,c.charAt(0))}return r}}(),n=function(){function e(e,t,n){var r,i,a,o,u=0,c=e.length,s=t%p,l=t/p|0;for(e=e.slice();c--;)u=((i=s*(a=e[c]%p)+(r=l*a+(o=e[c]/p|0)*s)%p*p+u)/n|0)+(r/p|0)+l*o,e[c]=i%n;return u&&(e=[u].concat(e)),e}function t(e,t,n,r){var i,a;if(n!=r)a=n>r?1:-1;else for(i=a=0;i<n;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function n(e,t,n,r){for(var i=0;n--;)e[n]-=i,i=e[n]<t[n]?1:0,e[n]=i*r+e[n]-t[n];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(r,i,o,u,l){var f,p,g,d,v,m,y,b,w,O,x,S,N,M,B,D,A,E=r.s==i.s?1:-1,F=r.c,k=i.c;if(!(F&&F[0]&&k&&k[0]))return new V(r.s&&i.s&&(F?!k||F[0]!=k[0]:k)?F&&0==F[0]||!k?0*E:E/0:NaN);for(w=(b=new V(E)).c=[],E=o+(p=r.e-i.e)+1,l||(l=c,p=h(r.e/s)-h(i.e/s),E=E/s|0),g=0;k[g]==(F[g]||0);g++);if(k[g]>(F[g]||0)&&p--,E<0)w.push(1),d=!0;else{for(M=F.length,D=k.length,g=0,E+=2,(v=a(l/(k[0]+1)))>1&&(k=e(k,v,l),F=e(F,v,l),D=k.length,M=F.length),N=D,x=(O=F.slice(0,D)).length;x<D;O[x++]=0);A=k.slice(),A=[0].concat(A),B=k[0],k[1]>=l/2&&B++;do{if(v=0,(f=t(k,O,D,x))<0){if(S=O[0],D!=x&&(S=S*l+(O[1]||0)),(v=a(S/B))>1)for(v>=l&&(v=l-1),y=(m=e(k,v,l)).length,x=O.length;1==t(m,O,y,x);)v--,n(m,D<y?A:k,y,l),y=m.length,f=1;else 0==v&&(f=v=1),y=(m=k.slice()).length;if(y<x&&(m=[0].concat(m)),n(O,m,x,l),x=O.length,-1==f)for(;t(k,O,D,x)<1;)v++,n(O,D<x?A:k,x,l),x=O.length}else 0===f&&(v++,O=[0]);w[g++]=v,O[0]?O[x++]=F[N]||0:(O=[F[N]],x=1)}while((N++<M||null!=O[0])&&E--);d=null!=O[0],w[0]||w.splice(0,1)}if(l==c){for(g=1,E=w[0];E>=10;E/=10,g++);W(b,o+(b.e=g+p*s-1)+1,u,d)}else b.e=p,b.r=+d;return b}}(),M=/^(-?)0([xbo])(?=\w[\w.]*$)/i,B=/^([^.]+)\.$/,D=/^\.([^.]+)$/,A=/^-?(Infinity|NaN)$/,E=/^\s*\+(?=[\w.])|^\s+|\s+$/g,x=function(e,t,n,r){var i,a=n?t:t.replace(E,"");if(A.test(a))e.s=isNaN(a)?null:a<0?-1:1,e.c=e.e=null;else{if(!n&&(a=a.replace(M,(function(e,t,n){return i="x"==(n=n.toLowerCase())?16:"b"==n?2:8,r&&r!=i?e:t})),r&&(i=r,a=a.replace(B,"$1").replace(D,"0.$1")),t!=a))return new V(a,i);if(V.DEBUG)throw Error(o+"Not a"+(r?" base "+r:"")+" number: "+t);e.c=e.e=e.s=null}},F.absoluteValue=F.abs=function(){var e=new V(this);return e.s<0&&(e.s=1),e},F.comparedTo=function(e,t){return v(this,new V(e,t))},F.decimalPlaces=F.dp=function(e,t){var n,r,i,a=this;if(null!=e)return m(e,0,g),null==t?t=L:m(t,0,8),W(new V(a),e+a.e+1,t);if(!(n=a.c))return null;if(r=((i=n.length-1)-h(this.e/s))*s,i=n[i])for(;i%10==0;i/=10,r--);return r<0&&(r=0),r},F.dividedBy=F.div=function(e,t){return n(this,new V(e,t),_,L)},F.dividedToIntegerBy=F.idiv=function(e,t){return n(this,new V(e,t),0,1)},F.exponentiatedBy=F.pow=function(e,t){var n,r,u,c,l,f,p,g,h=this;if((e=new V(e)).c&&!e.isInteger())throw Error(o+"Exponent not an integer: "+H(e));if(null!=t&&(t=new V(t)),l=e.e>14,!h.c||!h.c[0]||1==h.c[0]&&!h.e&&1==h.c.length||!e.c||!e.c[0])return g=new V(Math.pow(+H(h),l?2-y(e):+H(e))),t?g.mod(t):g;if(f=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new V(NaN);(r=!f&&h.isInteger()&&t.isInteger())&&(h=h.mod(t))}else{if(e.e>9&&(h.e>0||h.e<-1||(0==h.e?h.c[0]>1||l&&h.c[1]>=24e7:h.c[0]<8e13||l&&h.c[0]<=9999975e7)))return c=h.s<0&&y(e)?-0:0,h.e>-1&&(c=1/c),new V(f?1/c:c);I&&(c=i(I/s+2))}for(l?(n=new V(.5),f&&(e.s=1),p=y(e)):p=(u=Math.abs(+H(e)))%2,g=new V(k);;){if(p){if(!(g=g.times(h)).c)break;c?g.c.length>c&&(g.c.length=c):r&&(g=g.mod(t))}if(u){if(0===(u=a(u/2)))break;p=u%2}else if(W(e=e.times(n),e.e+1,1),e.e>14)p=y(e);else{if(0===(u=+H(e)))break;p=u%2}h=h.times(h),c?h.c&&h.c.length>c&&(h.c.length=c):r&&(h=h.mod(t))}return r?g:(f&&(g=k.div(g)),t?g.mod(t):c?W(g,I,L,undefined):g)},F.integerValue=function(e){var t=new V(this);return null==e?e=L:m(e,0,8),W(t,t.e+1,e)},F.isEqualTo=F.eq=function(e,t){return 0===v(this,new V(e,t))},F.isFinite=function(){return!!this.c},F.isGreaterThan=F.gt=function(e,t){return v(this,new V(e,t))>0},F.isGreaterThanOrEqualTo=F.gte=function(e,t){return 1===(t=v(this,new V(e,t)))||0===t},F.isInteger=function(){return!!this.c&&h(this.e/s)>this.c.length-2},F.isLessThan=F.lt=function(e,t){return v(this,new V(e,t))<0},F.isLessThanOrEqualTo=F.lte=function(e,t){return-1===(t=v(this,new V(e,t)))||0===t},F.isNaN=function(){return!this.s},F.isNegative=function(){return this.s<0},F.isPositive=function(){return this.s>0},F.isZero=function(){return!!this.c&&0==this.c[0]},F.minus=function(e,t){var n,r,i,a,o=this,u=o.s;if(t=(e=new V(e,t)).s,!u||!t)return new V(NaN);if(u!=t)return e.s=-t,o.plus(e);var l=o.e/s,f=e.e/s,p=o.c,g=e.c;if(!l||!f){if(!p||!g)return p?(e.s=-t,e):new V(g?o:NaN);if(!p[0]||!g[0])return g[0]?(e.s=-t,e):new V(p[0]?o:3==L?-0:0)}if(l=h(l),f=h(f),p=p.slice(),u=l-f){for((a=u<0)?(u=-u,i=p):(f=l,i=g),i.reverse(),t=u;t--;i.push(0));i.reverse()}else for(r=(a=(u=p.length)<(t=g.length))?u:t,u=t=0;t<r;t++)if(p[t]!=g[t]){a=p[t]<g[t];break}if(a&&(i=p,p=g,g=i,e.s=-e.s),(t=(r=g.length)-(n=p.length))>0)for(;t--;p[n++]=0);for(t=c-1;r>u;){if(p[--r]<g[r]){for(n=r;n&&!p[--n];p[n]=t);--p[n],p[r]+=c}p[r]-=g[r]}for(;0==p[0];p.splice(0,1),--f);return p[0]?z(e,p,f):(e.s=3==L?-1:1,e.c=[e.e=0],e)},F.modulo=F.mod=function(e,t){var r,i,a=this;return e=new V(e,t),!a.c||!e.s||e.c&&!e.c[0]?new V(NaN):!e.c||a.c&&!a.c[0]?new V(a):(9==R?(i=e.s,e.s=1,r=n(a,e,0,3),e.s=i,r.s*=i):r=n(a,e,0,R),(e=a.minus(r.times(e))).c[0]||1!=R||(e.s=a.s),e)},F.multipliedBy=F.times=function(e,t){var n,r,i,a,o,u,l,f,g,d,v,m,y,b,w,O=this,x=O.c,S=(e=new V(e,t)).c;if(!(x&&S&&x[0]&&S[0]))return!O.s||!e.s||x&&!x[0]&&!S||S&&!S[0]&&!x?e.c=e.e=e.s=null:(e.s*=O.s,x&&S?(e.c=[0],e.e=0):e.c=e.e=null),e;for(r=h(O.e/s)+h(e.e/s),e.s*=O.s,(l=x.length)<(d=S.length)&&(y=x,x=S,S=y,i=l,l=d,d=i),i=l+d,y=[];i--;y.push(0));for(b=c,w=p,i=d;--i>=0;){for(n=0,v=S[i]%w,m=S[i]/w|0,a=i+(o=l);a>i;)n=((f=v*(f=x[--o]%w)+(u=m*f+(g=x[o]/w|0)*v)%w*w+y[a]+n)/b|0)+(u/w|0)+m*g,y[a--]=f%b;y[a]=n}return n?++r:y.splice(0,1),z(e,y,r)},F.negated=function(){var e=new V(this);return e.s=-e.s||null,e},F.plus=function(e,t){var n,r=this,i=r.s;if(t=(e=new V(e,t)).s,!i||!t)return new V(NaN);if(i!=t)return e.s=-t,r.minus(e);var a=r.e/s,o=e.e/s,u=r.c,l=e.c;if(!a||!o){if(!u||!l)return new V(i/0);if(!u[0]||!l[0])return l[0]?e:new V(u[0]?r:0*i)}if(a=h(a),o=h(o),u=u.slice(),i=a-o){for(i>0?(o=a,n=l):(i=-i,n=u),n.reverse();i--;n.push(0));n.reverse()}for((i=u.length)-(t=l.length)<0&&(n=l,l=u,u=n,t=i),i=0;t;)i=(u[--t]=u[t]+l[t]+i)/c|0,u[t]=c===u[t]?0:u[t]%c;return i&&(u=[i].concat(u),++o),z(e,u,o)},F.precision=F.sd=function(e,t){var n,r,i,a=this;if(null!=e&&e!==!!e)return m(e,1,g),null==t?t=L:m(t,0,8),W(new V(a),e,t);if(!(n=a.c))return null;if(r=(i=n.length-1)*s+1,i=n[i]){for(;i%10==0;i/=10,r--);for(i=n[0];i>=10;i/=10,r++);}return e&&a.e+1>r&&(r=a.e+1),r},F.shiftedBy=function(e){return m(e,-9007199254740991,l),this.times("1e"+e)},F.squareRoot=F.sqrt=function(){var e,t,r,i,a,o=this,u=o.c,c=o.s,s=o.e,l=_+4,f=new V("0.5");if(1!==c||!u||!u[0])return new V(!c||c<0&&(!u||u[0])?NaN:u?o:1/0);if(0==(c=Math.sqrt(+H(o)))||c==1/0?(((t=d(u)).length+s)%2==0&&(t+="0"),c=Math.sqrt(+t),s=h((s+1)/2)-(s<0||s%2),r=new V(t=c==1/0?"1e"+s:(t=c.toExponential()).slice(0,t.indexOf("e")+1)+s)):r=new V(c+""),r.c[0])for((c=(s=r.e)+l)<3&&(c=0);;)if(a=r,r=f.times(a.plus(n(o,a,l,1))),d(a.c).slice(0,c)===(t=d(r.c)).slice(0,c)){if(r.e<s&&--c,"9999"!=(t=t.slice(c-3,c+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(W(r,r.e+_+2,1),e=!r.times(r).eq(o));break}if(!i&&(W(a,a.e+_+2,0),a.times(a).eq(o))){r=a;break}l+=4,c+=4,i=1}return W(r,r.e+_+1,L,e)},F.toExponential=function(e,t){return null!=e&&(m(e,0,g),e++),q(this,e,t,1)},F.toFixed=function(e,t){return null!=e&&(m(e,0,g),e=e+this.e+1),q(this,e,t)},F.toFormat=function(e,t,n){var r,i=this;if(null==n)null!=e&&t&&"object"==typeof t?(n=t,t=null):e&&"object"==typeof e?(n=e,e=t=null):n=$;else if("object"!=typeof n)throw Error(o+"Argument not an object: "+n);if(r=i.toFixed(e,t),i.c){var a,u=r.split("."),c=+n.groupSize,s=+n.secondaryGroupSize,l=n.groupSeparator||"",f=u[0],p=u[1],g=i.s<0,h=g?f.slice(1):f,d=h.length;if(s&&(a=c,c=s,s=a,d-=a),c>0&&d>0){for(a=d%c||c,f=h.substr(0,a);a<d;a+=c)f+=l+h.substr(a,c);s>0&&(f+=l+h.slice(a)),g&&(f="-"+f)}r=p?f+(n.decimalSeparator||"")+((s=+n.fractionGroupSize)?p.replace(new RegExp("\\d{"+s+"}\\B","g"),"$&"+(n.fractionGroupSeparator||"")):p):f}return(n.prefix||"")+r+(n.suffix||"")},F.toFraction=function(e){var t,r,i,a,u,c,l,p,g,h,v,m,y=this,b=y.c;if(null!=e&&(!(l=new V(e)).isInteger()&&(l.c||1!==l.s)||l.lt(k)))throw Error(o+"Argument "+(l.isInteger()?"out of range: ":"not an integer: ")+H(l));if(!b)return new V(y);for(t=new V(k),g=r=new V(k),i=p=new V(k),m=d(b),u=t.e=m.length-y.e-1,t.c[0]=f[(c=u%s)<0?s+c:c],e=!e||l.comparedTo(t)>0?u>0?t:g:l,c=j,j=1/0,l=new V(m),p.c[0]=0;h=n(l,t,0,1),1!=(a=r.plus(h.times(i))).comparedTo(e);)r=i,i=a,g=p.plus(h.times(a=g)),p=a,t=l.minus(h.times(a=t)),l=a;return a=n(e.minus(r),i,0,1),p=p.plus(a.times(g)),r=r.plus(a.times(i)),p.s=g.s=y.s,v=n(g,i,u*=2,L).minus(y).abs().comparedTo(n(p,r,u,L).minus(y).abs())<1?[g,i]:[p,r],j=c,v},F.toNumber=function(){return+H(this)},F.toPrecision=function(e,t){return null!=e&&m(e,1,g),q(this,e,t,2)},F.toString=function(e){var t,n=this,r=n.s,i=n.e;return null===i?r?(t="Infinity",r<0&&(t="-"+t)):t="NaN":(null==e?t=i<=T||i>=P?b(d(n.c),i):w(d(n.c),i,"0"):10===e?t=w(d((n=W(new V(n),_+i+1,L)).c),n.e,"0"):(m(e,2,G.length,"Base"),t=O(w(d(n.c),i,"0"),10,e,r,!0)),r<0&&n.c[0]&&(t="-"+t)),t},F.valueOf=F.toJSON=function(){return H(this)},F._isBigNumber=!0,"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator&&(F[Symbol.toStringTag]="BigNumber",F[Symbol.for("nodejs.util.inspect.custom")]=F.valueOf),null!=t&&V.set(t),V}(),n.default=n.BigNumber=n,void 0!==t&&t.exports?t.exports=n:(e||(e="undefined"!=typeof self&&self?self:window),e.BigNumber=n)}(this)},{}],2:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */t.exports={languageTag:"en-US",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},spaceSeparated:!1,ordinal:function(e){var t=e%10;return 1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"$",position:"prefix",code:"USD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0},fullWithTwoDecimals:{output:"currency",thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{thousandSeparated:!0,mantissa:2},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}],3:[function(e,t,n){"use strict";function r(e,t){return function(e){if(Array.isArray(e))return e}
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */(e)||function(e,t){var n=[],r=!0,i=!1,a=void 0;try{for(var o,u=e[Symbol.iterator]();!(r=(o=u.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(e){i=!0,a=e}finally{try{r||null==u.return||u.return()}finally{if(i)throw a}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var i=e("./globalState"),a=e("./validating"),o=e("./parsing"),u=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],c={general:{scale:1024,suffixes:u,marker:"bd"},binary:{scale:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"],marker:"b"},decimal:{scale:1e3,suffixes:u,marker:"d"}},s={totalLength:0,characteristic:0,forceAverage:!1,average:!1,mantissa:-1,optionalMantissa:!0,thousandSeparated:!1,spaceSeparated:!1,negative:"sign",forceSign:!1};function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;if("string"==typeof t&&(t=o.parseFormat(t)),!a.validateFormat(t))return"ERROR: invalid format";var r=t.prefix||"",u=t.postfix||"",l=function(e,t,n){switch(t.output){case"currency":return function(e,t,n){var r=n.currentCurrency(),i=Object.assign({},s,t),a=void 0,o="",u=!!i.totalLength||!!i.forceAverage||i.average,c=t.currencyPosition||r.position,l=t.currencySymbol||r.symbol;i.spaceSeparated&&(o=" ");"infix"===c&&(a=o+l+o);var f=m({instance:e,providedFormat:t,state:n,decimalSeparator:a});"prefix"===c&&(f=e._value<0&&"sign"===i.negative?"-".concat(o).concat(l).concat(f.slice(1)):l+o+f);c&&"postfix"!==c||(f=f+(o=u?"":o)+l);return f}(e,t=y(t,i.currentCurrencyDefaultFormat()),i);case"percent":return function(e,t,n,r){var i=t.prefixSymbol,a=m({instance:r(100*e._value),providedFormat:t,state:n}),o=Object.assign({},s,t);if(i)return"%".concat(o.spaceSeparated?" ":"").concat(a);return"".concat(a).concat(o.spaceSeparated?" ":"","%")}(e,t=y(t,i.currentPercentageDefaultFormat()),i,n);case"byte":return function(e,t,n,r){var i=t.base||"binary",a=c[i],o=h(e._value,a.suffixes,a.scale),u=o.value,s=o.suffix,l=m({instance:r(u),providedFormat:t,state:n,defaults:n.currentByteDefaultFormat()}),f=n.currentAbbreviations();return"".concat(l).concat(f.spaced?" ":"").concat(s)}(e,t=y(t,i.currentByteDefaultFormat()),i,n);case"time":return t=y(t,i.currentTimeDefaultFormat()),function(e){var t=Math.floor(e._value/60/60),n=Math.floor((e._value-60*t*60)/60),r=Math.round(e._value-60*t*60-60*n);return"".concat(t,":").concat(n<10?"0":"").concat(n,":").concat(r<10?"0":"").concat(r)}(e);case"ordinal":return function(e,t,n){var r=n.currentOrdinal(),i=Object.assign({},s,t),a=m({instance:e,providedFormat:t,state:n}),o=r(e._value);return"".concat(a).concat(i.spaceSeparated?" ":"").concat(o)}(e,t=y(t,i.currentOrdinalDefaultFormat()),i);default:return m({instance:e,providedFormat:t,numbro:n})}}(e,t,n);return l=function(e,t){return t+e}(l,r),l=function(e,t){return e+t}(l,u),l}function f(e){var t=c.decimal;return h(e._value,t.suffixes,t.scale).suffix}function p(e){var t=c.binary;return h(e._value,t.suffixes,t.scale).suffix}function g(e){var t=c.general;return h(e._value,t.suffixes,t.scale).suffix}function h(e,t,n){var r=t[0],i=Math.abs(e);if(i>=n){for(var a=1;a<t.length;++a){var o=Math.pow(n,a),u=Math.pow(n,a+1);if(i>=o&&i<u){r=t[a],e/=o;break}}r===t[0]&&(e/=Math.pow(n,t.length-1),r=t[t.length-1])}return{value:e,suffix:r}}function d(e){for(var t="",n=0;n<e;n++)t+="0";return t}function v(e,t){return-1!==e.toString().indexOf("e")?function(e,t){var n=e.toString(),i=r(n.split("e"),2),a=i[0],o=i[1],u=r(a.split("."),2),c=u[0],s=u[1],l=void 0===s?"":s;if(+o>0)n=c+l+d(o-l.length);else{var f=".";f=+c<0?"-0".concat(f):"0".concat(f);var p=(d(-o-1)+Math.abs(c)+l).substr(0,t);p.length<t&&(p+=d(t-p.length)),n=f+p}return+o>0&&t>0&&(n+=".".concat(d(t))),n}(e,t):(Math.round(+"".concat(e,"e+").concat(t))/Math.pow(10,t)).toFixed(t)}function m(e){var t=e.instance,n=e.providedFormat,a=e.state,o=void 0===a?i:a,u=e.decimalSeparator,c=e.defaults,l=void 0===c?o.currentDefaults():c,f=t._value;if(0===f&&o.hasZeroFormat())return o.getZeroFormat();if(!isFinite(f))return f.toString();var p=Object.assign({},s,l,n),g=p.totalLength,h=g?0:p.characteristic,d=p.optionalCharacteristic,m=p.forceAverage,y=!!g||!!m||p.average,b=g?-1:y&&void 0===n.mantissa?0:p.mantissa,w=!g&&(void 0===n.optionalMantissa?-1===b:p.optionalMantissa),O=p.trimMantissa,x=p.thousandSeparated,S=p.spaceSeparated,N=p.negative,M=p.forceSign,B=p.exponential,D="";if(y){var A=function(e){var t=e.value,n=e.forceAverage,r=e.abbreviations,i=e.spaceSeparated,a=void 0!==i&&i,o=e.totalLength,u=void 0===o?0:o,c="",s=Math.abs(t),l=-1;if(s>=Math.pow(10,12)&&!n||"trillion"===n?(c=r.trillion,t/=Math.pow(10,12)):s<Math.pow(10,12)&&s>=Math.pow(10,9)&&!n||"billion"===n?(c=r.billion,t/=Math.pow(10,9)):s<Math.pow(10,9)&&s>=Math.pow(10,6)&&!n||"million"===n?(c=r.million,t/=Math.pow(10,6)):(s<Math.pow(10,6)&&s>=Math.pow(10,3)&&!n||"thousand"===n)&&(c=r.thousand,t/=Math.pow(10,3)),c&&(c=(a?" ":"")+c),u){var f=t.toString().split(".")[0];l=Math.max(u-f.length,0)}return{value:t,abbreviation:c,mantissaPrecision:l}}({value:f,forceAverage:m,abbreviations:o.currentAbbreviations(),spaceSeparated:S,totalLength:g});f=A.value,D+=A.abbreviation,g&&(b=A.mantissaPrecision)}if(B){var E=function(e){var t=e.value,n=e.characteristicPrecision,i=void 0===n?0:n,a=r(t.toExponential().split("e"),2),o=a[0],u=a[1],c=+o;return i?(1<i&&(c*=Math.pow(10,i-1),u=(u=+u-(i-1))>=0?"+".concat(u):u),{value:c,abbreviation:"e".concat(u)}):{value:c,abbreviation:"e".concat(u)}}({value:f,characteristicPrecision:h});f=E.value,D=E.abbreviation+D}var F=function(e,t,n,i,a){if(-1===i)return e;var o=v(t,i),u=r(o.toString().split("."),2),c=u[0],s=u[1],l=void 0===s?"":s;if(l.match(/^0+$/)&&(n||a))return c;var f=l.match(/0+$/);return a&&f?"".concat(c,".").concat(l.toString().slice(0,f.index)):o.toString()}(f.toString(),f,w,b,O);return F=function(e,t,n,i){var a=e,o=r(a.toString().split("."),2),u=o[0],c=o[1];if(u.match(/^-?0$/)&&n)return c?"".concat(u.replace("0",""),".").concat(c):u.replace("0","");if(u.length<i)for(var s=i-u.length,l=0;l<s;l++)a="0".concat(a);return a.toString()}(F,0,d,h),F=function(e,t,n,r,i){var a=r.currentDelimiters(),o=a.thousands;i=i||a.decimal;var u=a.thousandsSize||3,c=e.toString(),s=c.split(".")[0],l=c.split(".")[1];if(n){t<0&&(s=s.slice(1));var f=function(e,t){for(var n=[],r=0,i=e;i>0;i--)r===t&&(n.unshift(i),r=0),r++;return n}(s.length,u);f.forEach((function(e,t){s=s.slice(0,e+t)+o+s.slice(e+t)})),t<0&&(s="-".concat(s))}return l?s+i+l:s}(F,f,x,o,u),(y||B)&&(F=function(e,t){return e+t}(F,D)),(M||f<0)&&(F=function(e,t,n){return 0===t?e:0==+e?e.replace("-",""):t>0?"+".concat(e):"sign"===n?e:"(".concat(e.replace("-",""),")")}(F,f,N)),F}function y(e,t){if(!e)return t;var n=Object.keys(e);return 1===n.length&&"output"===n[0]?t:e}t.exports=function(e){return{format:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return l.apply(void 0,n.concat([e]))},getByteUnit:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return g.apply(void 0,n.concat([e]))},getBinaryByteUnit:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return p.apply(void 0,n.concat([e]))},getDecimalByteUnit:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return f.apply(void 0,n.concat([e]))},formatOrDefault:y}}},{"./globalState":4,"./parsing":8,"./validating":10}],4:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */var r=e("./en-US"),i=e("./validating"),a=e("./parsing"),o={},u=void 0,c={},s=null,l={};function f(e){u=e}function p(){return c[u]}o.languages=function(){return Object.assign({},c)},o.currentLanguage=function(){return u},o.currentCurrency=function(){return p().currency},o.currentAbbreviations=function(){return p().abbreviations},o.currentDelimiters=function(){return p().delimiters},o.currentOrdinal=function(){return p().ordinal},o.currentDefaults=function(){return Object.assign({},p().defaults,l)},o.currentOrdinalDefaultFormat=function(){return Object.assign({},o.currentDefaults(),p().ordinalFormat)},o.currentByteDefaultFormat=function(){return Object.assign({},o.currentDefaults(),p().byteFormat)},o.currentPercentageDefaultFormat=function(){return Object.assign({},o.currentDefaults(),p().percentageFormat)},o.currentCurrencyDefaultFormat=function(){return Object.assign({},o.currentDefaults(),p().currencyFormat)},o.currentTimeDefaultFormat=function(){return Object.assign({},o.currentDefaults(),p().timeFormat)},o.setDefaults=function(e){e=a.parseFormat(e),i.validateFormat(e)&&(l=e)},o.getZeroFormat=function(){return s},o.setZeroFormat=function(e){return s="string"==typeof e?e:null},o.hasZeroFormat=function(){return null!==s},o.languageData=function(e){if(e){if(c[e])return c[e];throw new Error('Unknown tag "'.concat(e,'"'))}return p()},o.registerLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!i.validateLanguage(e))throw new Error("Invalid language data");c[e.languageTag]=e,t&&f(e.languageTag)},o.setLanguage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.languageTag;if(!c[e]){var n=e.split("-")[0],i=Object.keys(c).find((function(e){return e.split("-")[0]===n}));return c[i]?void f(i):void f(t)}f(e)},o.registerLanguage(r),u=r.languageTag,t.exports=o},{"./en-US":2,"./parsing":8,"./validating":10}],5:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */t.exports=function(t){return{loadLanguagesInNode:function(n){return function(t,n){t.forEach((function(t){var r=void 0;try{r=e("../languages/".concat(t))}catch(e){console.error('Unable to load "'.concat(t,'". No matching language file found.'))}r&&n.registerLanguage(r)}))}(n,t)}}}},{}],6:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */var r=e("bignumber.js");function i(e,t,n){var i=new r(e._value),a=t;return n.isNumbro(t)&&(a=t._value),a=new r(a),e._value=i.minus(a).toNumber(),e}t.exports=function(e){return{add:function(t,n){return function(e,t,n){var i=new r(e._value),a=t;return n.isNumbro(t)&&(a=t._value),a=new r(a),e._value=i.plus(a).toNumber(),e}(t,n,e)},subtract:function(t,n){return i(t,n,e)},multiply:function(t,n){return function(e,t,n){var i=new r(e._value),a=t;return n.isNumbro(t)&&(a=t._value),a=new r(a),e._value=i.times(a).toNumber(),e}(t,n,e)},divide:function(t,n){return function(e,t,n){var i=new r(e._value),a=t;return n.isNumbro(t)&&(a=t._value),a=new r(a),e._value=i.dividedBy(a).toNumber(),e}(t,n,e)},set:function(t,n){return function(e,t,n){var r=t;return n.isNumbro(t)&&(r=t._value),e._value=r,e}(t,n,e)},difference:function(t,n){return function(e,t,n){var r=n(e._value);return i(r,t,n),Math.abs(r._value)}(t,n,e)}}}},{"bignumber.js":1}],7:[function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
var i=e("./globalState"),a=e("./validating"),o=e("./loading")(g),u=e("./unformatting"),c=e("./formatting")(g),s=e("./manipulating")(g),l=e("./parsing"),f=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._value=t}var t,n,a;return t=e,n=[{key:"clone",value:function(){return g(this._value)}},{key:"format",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c.format(this,e)}},{key:"formatCurrency",value:function(e){return"string"==typeof e&&(e=l.parseFormat(e)),(e=c.formatOrDefault(e,i.currentCurrencyDefaultFormat())).output="currency",c.format(this,e)}},{key:"formatTime",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.output="time",c.format(this,e)}},{key:"binaryByteUnits",value:function(){return c.getBinaryByteUnit(this)}},{key:"decimalByteUnits",value:function(){return c.getDecimalByteUnit(this)}},{key:"byteUnits",value:function(){return c.getByteUnit(this)}},{key:"difference",value:function(e){return s.difference(this,e)}},{key:"add",value:function(e){return s.add(this,e)}},{key:"subtract",value:function(e){return s.subtract(this,e)}},{key:"multiply",value:function(e){return s.multiply(this,e)}},{key:"divide",value:function(e){return s.divide(this,e)}},{key:"set",value:function(e){return s.set(this,p(e))}},{key:"value",value:function(){return this._value}},{key:"valueOf",value:function(){return this._value}}],n&&r(t.prototype,n),a&&r(t,a),e}();function p(e){var t=e;return g.isNumbro(e)?t=e._value:"string"==typeof e?t=g.unformat(e):isNaN(e)&&(t=NaN),t}function g(e){return new f(p(e))}g.version="2.1.2",g.isNumbro=function(e){return e instanceof f},g.language=i.currentLanguage,g.registerLanguage=i.registerLanguage,g.setLanguage=i.setLanguage,g.languages=i.languages,g.languageData=i.languageData,g.zeroFormat=i.setZeroFormat,g.defaultFormat=i.currentDefaults,g.setDefaults=i.setDefaults,g.defaultCurrencyFormat=i.currentCurrencyDefaultFormat,g.validate=a.validate,g.loadLanguagesInNode=o.loadLanguagesInNode,g.unformat=u.unformat,t.exports=g},{"./formatting":3,"./globalState":4,"./loading":5,"./manipulating":6,"./parsing":8,"./unformatting":9,"./validating":10}],8:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */t.exports={parseFormat:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"string"!=typeof e?e:(function(e,t){if(-1===e.indexOf("$")){if(-1===e.indexOf("%"))return-1!==e.indexOf("bd")?(t.output="byte",void(t.base="general")):-1!==e.indexOf("b")?(t.output="byte",void(t.base="binary")):-1!==e.indexOf("d")?(t.output="byte",void(t.base="decimal")):void(-1===e.indexOf(":")?-1!==e.indexOf("o")&&(t.output="ordinal"):t.output="time");t.output="percent"}else t.output="currency"}(e=function(e,t){var n=e.match(/{([^}]*)}$/);return n?(t.postfix=n[1],e.slice(0,-n[0].length)):e}(e=function(e,t){var n=e.match(/^{([^}]*)}/);return n?(t.prefix=n[1],e.slice(n[0].length)):e}(e,t),t),t),function(e,t){var n=e.match(/[1-9]+[0-9]*/);n&&(t.totalLength=+n[0])}(e,t),function(e,t){var n=e.split(".")[0].match(/0+/);n&&(t.characteristic=n[0].length)}(e,t),function(e,t){if(-1!==e.indexOf(".")){var n=e.split(".")[0];t.optionalCharacteristic=-1===n.indexOf("0")}}(e,t),function(e,t){-1!==e.indexOf("a")&&(t.average=!0)}(e,t),function(e,t){-1!==e.indexOf("K")?t.forceAverage="thousand":-1!==e.indexOf("M")?t.forceAverage="million":-1!==e.indexOf("B")?t.forceAverage="billion":-1!==e.indexOf("T")&&(t.forceAverage="trillion")}(e,t),function(e,t){var n=e.split(".")[1];if(n){var r=n.match(/0+/);r&&(t.mantissa=r[0].length)}}(e,t),function(e,t){e.match(/\[\.]/)?t.optionalMantissa=!0:e.match(/\./)&&(t.optionalMantissa=!1)}(e,t),function(e,t){-1!==e.indexOf(",")&&(t.thousandSeparated=!0)}(e,t),function(e,t){-1!==e.indexOf(" ")&&(t.spaceSeparated=!0)}(e,t),function(e,t){e.match(/^\+?\([^)]*\)$/)&&(t.negative="parenthesis"),e.match(/^\+?-/)&&(t.negative="sign")}(e,t),function(e,t){e.match(/^\+/)&&(t.forceSign=!0)}(e,t),t)}}},{}],9:[function(e,t,n){"use strict";
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */var r=[{key:"ZiB",factor:Math.pow(1024,7)},{key:"ZB",factor:Math.pow(1e3,7)},{key:"YiB",factor:Math.pow(1024,8)},{key:"YB",factor:Math.pow(1e3,8)},{key:"TiB",factor:Math.pow(1024,4)},{key:"TB",factor:Math.pow(1e3,4)},{key:"PiB",factor:Math.pow(1024,5)},{key:"PB",factor:Math.pow(1e3,5)},{key:"MiB",factor:Math.pow(1024,2)},{key:"MB",factor:Math.pow(1e3,2)},{key:"KiB",factor:Math.pow(1024,1)},{key:"KB",factor:Math.pow(1e3,1)},{key:"GiB",factor:Math.pow(1024,3)},{key:"GB",factor:Math.pow(1e3,3)},{key:"EiB",factor:Math.pow(1024,6)},{key:"EB",factor:Math.pow(1e3,6)},{key:"B",factor:1}];function i(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3?arguments[3]:void 0,u=arguments.length>4?arguments[4]:void 0,c=arguments.length>5?arguments[5]:void 0,s=arguments.length>6?arguments[6]:void 0;if(!isNaN(+e))return+e;var l="",f=e.replace(/(^[^(]*)\((.*)\)([^)]*$)/,"$1$2$3");if(f!==e)return-1*a(f,t,n,o,u,c,s);for(var p=0;p<r.length;p++){var g=r[p];if((l=e.replace(g.key,""))!==e)return a(l,t,n,o,u,c,s)*g.factor}if((l=e.replace("%",""))!==e)return a(l,t,n,o,u,c,s)/100;var h=parseFloat(e);if(!isNaN(h)){var d=o(h);if(d&&"."!==d&&(l=e.replace(new RegExp("".concat(i(d),"$")),""))!==e)return a(l,t,n,o,u,c,s);var v={};Object.keys(c).forEach((function(e){v[c[e]]=e}));for(var m=Object.keys(v).sort().reverse(),y=m.length,b=0;b<y;b++){var w=m[b],O=v[w];if((l=e.replace(w,""))!==e){var x=void 0;switch(O){case"thousand":x=Math.pow(10,3);break;case"million":x=Math.pow(10,6);break;case"billion":x=Math.pow(10,9);break;case"trillion":x=Math.pow(10,12)}return a(l,t,n,o,u,c,s)*x}}}}function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,u=arguments.length>5?arguments[5]:void 0,c=arguments.length>6?arguments[6]:void 0;if(""!==e){if(e===o)return 0;var s=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=e.replace(n,"");return(r=r.replace(new RegExp("([0-9])".concat(i(t.thousands),"([0-9])"),"g"),"$1$2")).replace(t.decimal,".")}(e,t,n);return a(s,t,n,r,o,u,c)}}t.exports={unformat:function(t,n){var r=e("./globalState"),i=r.currentDelimiters(),a=r.currentCurrency().symbol,u=r.currentOrdinal(),c=r.getZeroFormat(),s=r.currentAbbreviations(),l=void 0;if("string"==typeof t)l=function(e,t){if(!e.indexOf(":")||":"===t.thousands)return!1;var n=e.split(":");if(3!==n.length)return!1;var r=+n[0],i=+n[1],a=+n[2];return!isNaN(r)&&!isNaN(i)&&!isNaN(a)}(t,i)?function(e){var t=e.split(":"),n=+t[0],r=+t[1];return+t[2]+60*r+3600*n}(t):o(t,i,a,u,c,s,n);else{if("number"!=typeof t)return;l=t}if(void 0!==l)return l}}},{"./globalState":4}],10:[function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}
/*!
 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */var a=e("./unformatting"),o=/^[a-z]{2,3}(-[a-zA-Z]{4})?(-([A-Z]{2}|[0-9]{3}))?$/,u={output:{type:"string",validValues:["currency","percent","byte","time","ordinal","number"]},base:{type:"string",validValues:["decimal","binary","general"],restriction:function(e,t){return"byte"===t.output},message:"`base` must be provided only when the output is `byte`",mandatory:function(e){return"byte"===e.output}},characteristic:{type:"number",restriction:function(e){return e>=0},message:"value must be positive"},prefix:"string",postfix:"string",forceAverage:{type:"string",validValues:["trillion","billion","million","thousand"]},average:"boolean",currencyPosition:{type:"string",validValues:["prefix","infix","postfix"]},currencySymbol:"string",totalLength:{type:"number",restrictions:[{restriction:function(e){return e>=0},message:"value must be positive"},{restriction:function(e,t){return!t.exponential},message:"`totalLength` is incompatible with `exponential`"}]},mantissa:{type:"number",restriction:function(e){return e>=0},message:"value must be positive"},optionalMantissa:"boolean",trimMantissa:"boolean",optionalCharacteristic:"boolean",thousandSeparated:"boolean",spaceSeparated:"boolean",abbreviations:{type:"object",children:{thousand:"string",million:"string",billion:"string",trillion:"string"}},negative:{type:"string",validValues:["sign","parenthesis"]},forceSign:"boolean",exponential:{type:"boolean"},prefixSymbol:{type:"boolean",restriction:function(e,t){return"percent"===t.output},message:"`prefixSymbol` can be provided only when the output is `percent`"}},c={languageTag:{type:"string",mandatory:!0,restriction:function(e){return e.match(o)},message:"the language tag must follow the BCP 47 specification (see https://tools.ieft.org/html/bcp47)"},delimiters:{type:"object",children:{thousands:"string",decimal:"string",thousandsSize:"number"},mandatory:!0},abbreviations:{type:"object",children:{thousand:{type:"string",mandatory:!0},million:{type:"string",mandatory:!0},billion:{type:"string",mandatory:!0},trillion:{type:"string",mandatory:!0}},mandatory:!0},spaceSeparated:"boolean",ordinal:{type:"function",mandatory:!0},currency:{type:"object",children:{symbol:"string",position:"string",code:"string"},mandatory:!0},defaults:"format",ordinalFormat:"format",byteFormat:"format",percentageFormat:"format",currencyFormat:"format",timeDefaults:"format",formats:{type:"object",children:{fourDigits:{type:"format",mandatory:!0},fullWithTwoDecimals:{type:"format",mandatory:!0},fullWithTwoDecimalsNoCurrency:{type:"format",mandatory:!0},fullWithNoDecimals:{type:"format",mandatory:!0}}}};function s(e){return!!a.unformat(e)}function l(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=Object.keys(e).map((function(r){if(!t[r])return console.error("".concat(n," Invalid key: ").concat(r)),!1;var a=e[r],o=t[r];if("string"==typeof o&&(o={type:o}),"format"===o.type){if(!l(a,u,"[Validate ".concat(r,"]"),!0))return!1}else if(i(a)!==o.type)return console.error("".concat(n," ").concat(r,' type mismatched: "').concat(o.type,'" expected, "').concat(i(a),'" provided')),!1;if(o.restrictions&&o.restrictions.length)for(var c=o.restrictions.length,s=0;s<c;s++){var f=o.restrictions[s],p=f.restriction,g=f.message;if(!p(a,e))return console.error("".concat(n," ").concat(r," invalid value: ").concat(g)),!1}if(o.restriction&&!o.restriction(a,e))return console.error("".concat(n," ").concat(r," invalid value: ").concat(o.message)),!1;if(o.validValues&&-1===o.validValues.indexOf(a))return console.error("".concat(n," ").concat(r," invalid value: must be among ").concat(JSON.stringify(o.validValues),', "').concat(a,'" provided')),!1;if(o.children&&!l(a,o.children,"[Validate ".concat(r,"]")))return!1;return!0}));return a||o.push.apply(o,r(Object.keys(t).map((function(r){var i=t[r];if("string"==typeof i&&(i={type:i}),i.mandatory){var a=i.mandatory;if("function"==typeof a&&(a=a(e)),a&&void 0===e[r])return console.error("".concat(n,' Missing mandatory key "').concat(r,'"')),!1}return!0})))),o.reduce((function(e,t){return e&&t}),!0)}function f(e){return l(e,u,"[Validate format]")}t.exports={validate:function(e,t){var n=s(e),r=f(t);return n&&r},validateFormat:f,validateInput:s,validateLanguage:function(e){return l(e,c,"[Validate language]")}}},{"./unformatting":9}]},{},[7])(7)}));
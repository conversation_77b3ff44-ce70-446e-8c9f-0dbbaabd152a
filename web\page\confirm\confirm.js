function initPage() {
    var windowHeight = $(window).height();
    //36是card高度  20是padding 高度
    var treeCardBodyHeight = windowHeight - 36 - 20;
    $("#dpTree").parent().css("height", treeCardBodyHeight + "px");

    var tbrHeight = $("#my-tbr").height();
    var tableHeight = windowHeight - tbrHeight;
    $("#my-table").css("height", tableHeight + "px");
}

/**
 * 注册事件
 */
function registerEvent() {
    $(window).on('resize', function (event) {
        initPage();
    });
    $('#toggle-tree').unbind("click").bind('click', function () {
        var status = $(this).attr('status');
        var $i = $(this).find('i');
        var $card = $(".col-tree .layui-card");
        if (status == 'show') {
            $card.css({
                "background-color": "#f3f3f3"
            });
            $(".contract").hide();
            $('.col-tree').css({
                "width": "25px",
                "overflow": "hidden"
            });
            $(".col-table").css({
                'width': 'calc(100% - 25px)'
            });
            $(this).attr('status', 'hide');
            $i.removeClass("layui-icon-prev").addClass("layui-icon-next");
        } else {
            $card.css({
                "background-color": "white"
            });
            $(".contract").show();
            $('.col-tree').css({
                "width": "25%",
                "overflow": "auto"
            });
            $(".col-table").css({
                'width': '75%'
            });
            $(this).attr('status', 'show');
            $i.removeClass("layui-icon-next").addClass("layui-icon-prev");
        }
    });

    $('#download-list').unbind("click").bind('click', function () {
        var options = {};
        options.seachFormHtml = `<form class="layui-form search-form" lay-filter="download-table-form">
								<div class="layui-form-item">
									<div class="layui-inline">
										<label class="layui-form-label">分类</label>
										<div class="layui-input-inline">
											<select id="s_folder" name="s_folder" lay-filter="s_folder" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">型号</label>
										<div class="layui-input-inline">
											<select id="s_model" name="s_model" lay-filter="s_model" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">项目</label>
										<div class="layui-input-inline">
											<select id="s_project" name="s_project" lay-filter="s_project" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">A表</label>
										<div class="layui-input-inline">
											<input type="text" name="s_a" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-inline">
										<button class="layui-btn layui-btn-sm" lay-submit lay-filter="download-table-search">搜索</button>
										<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
									</div>
								</div>
							</form>`;
        options.cols = [{
            field: 'PROJECT',
            width: 200,
            title: '项目',
            templet: function (d) {
                if (d['PROJECT']) {
                    return d['PROJECT'] === '-' ? "" : d['PROJECT'];
                }
                return "";
            }
        }];
        options.tableHeight = 500;
        options.tableWidth = 1200;

        function reloadSelect(successFn, s_folder = '', s_model = '', s_project = '') {
            postAjax("tree", 'QueryDownloadSearch', {
                creator: sessionStorage.getItem("username"),
                s_folder: s_folder,
                s_model: s_model,
                s_project: s_project
            }, false, function (resData) {
                var folders = resData['folders'];
                var models = resData['models'];
                var projects = resData['projects'];
                if (s_folder === '') {
                    $("#s_folder").empty().append('<option value=""></option>');
                    for (var i = 0; i < folders.length; i++) {
                        $("#s_folder").append('<option value="' + folders[i] + '">' + folders[i] + '</option>');
                    }
                }

                if (s_model === '') {
                    $("#s_model").empty().append('<option value=""></option>');
                    for (var i = 0; i < models.length; i++) {
                        $("#s_model").append('<option value="' + models[i] + '">' + models[i] + '</option>');
                    }
                }

                if (s_project === '') {
                    $("#s_project").empty().append('<option value=""></option>');
                    for (var i = 0; i < projects.length; i++) {
                        if ('-' !== projects[i]) {
                            $("#s_project").append('<option value="' + projects[i] + '">' + projects[i] + '</option>');
                        }
                    }
                }
                form.render(null, 'download-table-form');
                successFn();
            });
        }

        /**
         * 加载搜索表单
         */
        function renderSearchForm() {
            reloadSelect(function () {
                form.on('select(s_folder)', function (data) {
                    var value = data.value;
                    reloadSelect(function () {
                    }, value);
                });

                form.on('select(s_model)', function (data) {
                    reloadSelect(function () {
                    }, $("#s_folder").val(), data.value);
                });

                form.on('submit(download-table-search)', function (data) {
                    var field = data.field;
                    field.creator = sessionStorage.getItem("username");
                    table.reload('download-table', {
                        page: {
                            curr: 1 // 重新从第 1 页开始
                        },
                        where: field
                    });
                    return false;
                });
            });
        }

        function ejectDownloadTable(o) {
            layer.open({
                title: "文件下载列表",
                type: 1,
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                shadeClose: false,
                maxmin: true,
                resize: false,
                area: ['' + o.tableWidth + 'px', '605px'],
                scrollbar: false,
                content: o.seachFormHtml + '<div id="download-table"></div>',
                success: function () {
                    renderSearchForm();
                    renderDownloadTable(o.cols, o.tableHeight);
                }
            });
        }

        function renderDownloadTable(cols, tableHeight) {
            // 创建渲染实例
            table.render({
                elem: '#download-table',
                id: 'download-table',
                url: getReqUrl('tree', 'QueryDownloadTable'),
                where: {
                    creator: sessionStorage.getItem("username")
                },
                height: tableHeight,
                cellMinWidth: 80,
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
                },
                cols: [
                    [{
                        title: '序号',
                        type: "numbers",
                        width: 60
                    },
                    {
                        field: 'DWONLOAD_ID',
                        hide: true
                    },
                    {
                        field: 'FOLDER',
                        width: 102,
                        title: '分类'
                    },
                    {
                        field: 'MODEL',
                        title: '型号',
                        width: 85
                    }
                    ].concat(cols).concat([{
                        field: 'A_TABLE',
                        width: 300,
                        title: 'A表',
                        templet: function (d) {
                            if (d['A_TABLE']) {
                                return d['A_TABLE'] === '：' ? "" : d['A_TABLE'];
                            } else {
                                return "";
                            }

                        },
                    },
                    {
                        field: 'EXPORT_TYPE',
                        title: '文件类型',
                        width: 85,
                        templet: function (d) {
                            var html = "";
                            if (d['EXPORT_TYPE'] === 1) {
                                html = '<span class="layui-badge layui-bg-blue">Pdf</span>';
                            } else if (d['EXPORT_TYPE'] === 2) {
                                html = '<span class="layui-badge layui-bg-green">Pdf压缩包</span>';
                            } else if (d['EXPORT_TYPE'] === 3) {
                                html = '<span class="layui-badge layui-bg-red">Excel压缩包</span>';
                            } else if (d['EXPORT_TYPE'] === 4) {
                                html = '<span class="layui-badge layui-bg-red">数据包</span>';
                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        field: 'START_TIME',
                        width: 152,
                        title: '提交时间',
                        align: 'center'
                    },
                    {
                        field: 'FILE_SIZE',
                        width: 100,
                        title: '文件大小',
                        align: 'center',
                        templet: function (d) {
                            var html = "";
                            if (d['FILE_SIZE']) {
                                if (d['FILE_SIZE'] !== 'undefined' && d['FILE_SIZE'] !== '') {
                                    html = d['FILE_SIZE'];
                                }
                            }
                            return html;
                        }
                    },
                    {
                        field: 'END_TIME',
                        title: '完成时间',
                        width: 152,
                        align: 'center'
                    },
                    {
                        field: 'IS_DOWNLOAD',
                        title: '是否下载',
                        width: 85,
                        templet: function (d) {
                            var html = "";
                            if (d['IS_DOWNLOAD'] === 0) {
                                html = '<span class="layui-badge layui-bg-blue">未下载</span>';
                            } else if (d['IS_DOWNLOAD'] === 1) {
                                html = '<span class="layui-badge layui-bg-green">已下载</span>';
                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        field: 'IS_COMPLETE',
                        title: '是否完成',
                        fixed: 'right',
                        width: 90,
                        minWidth: 90,
                        templet: function (d) {
                            var html = "";
                            if (d['IS_COMPLETE'] === 0) {
                                html = '<span class="layui-badge layui-bg-blue">进行中</span>';
                            } else if (d['IS_COMPLETE'] === 1) {
                                html = '<span class="layui-badge layui-bg-green">已完成</span>';
                            } else if (d['IS_COMPLETE'] === 2) {
                                html = '<span class="layui-badge layui-bg-red show-msg" title="点击查看原因" msg="' + d['MSG'] + '">生成失败</span>';
                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        fixed: 'right',
                        title: '操作',
                        width: 130,
                        minWidth: 130,
                        toolbar: `<div class="layui-clear-space">
									<a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
									<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
								</div>`,
                        align: 'left'
                    }
                    ])
                ],
                done: function () {
                    $(".show-msg").off('click').on('click', function () {
                        layer.alert($(this).attr("msg"));
                    });
                },
                error: function (res, msg) {
                    console.log(res, msg)
                }
            });

            // 工具栏事件
            table.on('tool(download-table)', function (obj) {
                var data = obj.data; // 获得当前行数据
                if (obj.event === 'download') {
                    if (data['FILE_PATH']) {
                        var filePath = "//" + data['FILE_PATH'];
                        filePath = filePath.replace(/\\/g, "/");
                        var fileName = data['FILE_NAME'];
                        var export_type = data['EXPORT_TYPE'];
                        postAjax('table', 'RecordDownloadFile', {
                            downloadId: data['ID']
                        }, false, function () {
                            if (data['EXPORT_TYPE'] === 4) {
                                fileName = fileName + ".dat";
                            }
                            HotUtil.downloadFile(fileName, filePath);
                            table.reload('download-table');
                        });
                    } else {
                        layer.alert("文件还未生成，请稍后再试！", {
                            icon: 2
                        });
                    }
                } else if (obj.event === 'delete') {
                    layer.confirm('确定要删除这个下载记录吗？', {
                        icon: 3,
                        title: '删除确认'
                    }, function (index) {
                        layer.close(index); // 关闭确认对话框
                        // 确认删除
                        postAjax('tree', 'DeleteDownloadRecord', {
                            downloadId: data['ID']
                        }, false, function () {
                            layer.msg('删除成功', { icon: 1 });
                            table.reload('download-table', {
                                page: {
                                    curr: 1 // 重新从第 1 页开始
                                }
                            }); // 重新加载表格
                        }, function (error) {
                            layer.close(index); // 关闭确认对话框
                            layer.msg('删除失败，请稍后重试', { icon: 2 });
                        });
                    });
                }
            });
        }

        ejectDownloadTable(options);
    });

}

/**
 * 自动初始化性能索引
 */
function autoInitPerformanceIndexes() {
    // 异步调用，不阻塞页面加载
    postAjax("tree", 'CreatePerformanceIndexes', {}, false, function (resData) {
        console.log("性能索引初始化结果:", resData);
    }, function (error) {
        console.warn("性能索引初始化失败:", error);
    });
}

$(function () {
    initConstant();
    initPage();
    registerEvent();

    // 自动初始化性能索引（异步执行，不影响页面加载）
    setTimeout(function () {
        autoInitPerformanceIndexes();
    }, 1000); // 延迟1秒执行，确保页面已完全加载

    loadTree();
});
package util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.db.Entity;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import common.Result;
import java.io.File;
import java.util.function.Function;


public class TableUtil {


	/**
	 * 将表格数据转换为html
	 *
	 * @param dataStr
	 * @return
	 */
	public static String TableData2Html(String dataStr) {
		// 使用StringBuilder替代String拼接
		StringBuilder html = new StringBuilder(dataStr.length() * 2);
		html.append("<table class=\"layui-table\">");
		
		JSONObject data = JSONUtil.parseObj(dataStr);
		// 预先处理和缓存数据，避免重复解析
		JSONArray tableData = JSONUtil.parseArray(StrUtil.replace(data.getStr("tableData"), null, ""));
		JSONArray mergeds = data.getJSONArray("merged");
		JSONArray metas = data.getJSONArray("meta");
		
		// 预处理单元格数据，创建一个二维数组来存储处理后的数据
		int rows = tableData.size();
		int cols = rows > 0 ? tableData.getJSONArray(0).size() : 0;
		JSONObject[][] processedCells = new JSONObject[rows][cols];
		
		// 初始化单元格数据
		for (int i = 0; i < rows; i++) {
			JSONArray row = tableData.getJSONArray(i);
			for (int j = 0; j < row.size(); j++) {
					JSONObject cell = new JSONObject(16, false); // 预设容量，关闭排序
					cell.set("className", "")
						.set("rowspan", 1)
						.set("colspan", 1)
						.set("row", i)
						.set("col", j)
						.set("value", row.getStr(j))
						.set("eles", new JSONArray());
					processedCells[i][j] = cell;
			}
		}
		
		// 处理meta数据
		if (ObjectUtil.isNotNull(metas)) {
			for (int m = 0; m < metas.size(); m++) {
				JSONObject meta = metas.getJSONObject(m);
				if (ObjectUtil.isNotNull(meta)) {
					int row = meta.getInt("row");
					int col = meta.getInt("col");
					if (row < rows && col < cols) {
						JSONObject cell = processedCells[row][col];
						cell.set("className", meta.getStr("className", "htMiddle htCenter"))
							.set("readOnly", meta.getBool("readOnly", false));
						
						JSONArray eles = meta.getJSONArray("eles");
						if (ObjectUtil.isNotNull(eles)) {
							cell.set("eles", eles);
						}
						cell.set("comment", meta.get("comment"));
					}
				}
			}
		}
		
		// 处理合并单元格
		if (ObjectUtil.isNotNull(mergeds)) {
			for (int m = 0; m < mergeds.size(); m++) {
				JSONObject merged = mergeds.getJSONObject(m);
				int row = merged.getInt("row");
				int col = merged.getInt("col");
				if (row < rows && col < cols) {
					JSONObject cell = processedCells[row][col];
					int rowspan = merged.getInt("rowspan");
					int colspan = merged.getInt("colspan");
					cell.set("rowspan", rowspan)
						.set("colspan", colspan);
					
					// 标记被合并的单元格
					for (int r = row; r < Math.min(row + rowspan, rows); r++) {
						for (int c = col; c < Math.min(col + colspan, cols); c++) {
							if (!(r == row && c == col)) {
								processedCells[r][c].set("value", "del");
							}
						}
					}
				}
			}
		}
		
		// 生成HTML
		for (int i = 0; i < rows; i++) {
			html.append("<tr>");
			for (int j = 0; j < cols; j++) {
				JSONObject cell = processedCells[i][j];
				String value = cell.getStr("value", "");
				if (!StrUtil.equals(value, "del")) {
					appendCellHtml(html, cell);
				}
			}
			html.append("</tr>");
		}
		
		html.append("</table>");
		return html.toString();
	}

	// 抽取单元格HTML生成逻辑为独立方法
	private static void appendCellHtml(StringBuilder html, JSONObject cell) {
		String className = cell.getStr("className");
		boolean readOnly = cell.getBool("readOnly");
		String value = cell.getStr("value", "");
		
		JSONArray eles = cell.getJSONArray("eles");
		StringBuilder valueBuilder = new StringBuilder(value);
		
		// 处理元素
		if (ObjectUtil.isNotNull(eles)) {
			for (int e = 0; e < eles.size(); e++) {
				JSONObject ele = eles.getJSONObject(e);
				valueBuilder.append("<br>");
				String eleType = ele.getStr("type", "");
				String eleSrc = ele.getStr("src", "");
				String eleDate = ele.getStr("date", "");
				
				if (StrUtil.equals(eleType, "sign")) {
					appendSignElement(valueBuilder, ele, eleSrc, eleDate);
				} else if (StrUtil.equals(eleType, "photo")) {
					appendPhotoElement(valueBuilder, ele, eleSrc, eleDate);
				}
				valueBuilder.append("<br><span>").append(eleDate).append("</span>");
			}
		}
		
		// 处理注释
		Object comment = cell.get("comment");
		String commentValue = "";
		if (ObjectUtil.isNotNull(comment) && comment instanceof JSONObject) {
			className = className + " htCommentCell";
			commentValue = ((JSONObject) comment).getStr("value", "");
		}
		
		// 生成单元格HTML
		html.append("<td rowspan=\"").append(cell.getInt("rowspan"))
			.append("\" colspan=\"").append(cell.getInt("colspan"))
			.append("\" class=\"").append(className)
			.append("\" comment=\"").append(commentValue)
			.append("\" lock=\"").append(readOnly)
			.append("\" row=\"").append(cell.getInt("row"))
			.append("\" col=\"").append(cell.getInt("col"))
			.append("\">").append(valueBuilder)
			.append("</td>");
	}

	// 处理签名元素
	private static void appendSignElement(StringBuilder valueBuilder, JSONObject ele, String eleSrc, String eleDate) {
		if (StrUtil.contains(eleSrc, "data:")) {
			valueBuilder.append("<img type=\"sign\" src=\"").append(eleSrc)
					   .append("\" class=\"sign-img\" date=\"").append(eleDate).append("\">");
		} else {
			String eleClass = ele.getStr("class", "sign-img");
			if (StrUtil.contains(eleClass, "test-sign")) {
				eleClass = "sign-img test-sign";
			}
			valueBuilder.append("<img type=\"sign\" src=\"/File").append(eleSrc)
					   .append("\" class=\"").append(eleClass)
					   .append("\" date=\"").append(eleDate).append("\">");
		}
	}

	// 处理照片元素
	private static void appendPhotoElement(StringBuilder valueBuilder, JSONObject ele, String eleSrc, String eleDate) {
		valueBuilder.append("<img type=\"photo\" ")
				   .append("id=\"").append(ele.getStr("id", "")).append("\" ")
				   .append("photoname=\"").append(ele.getStr("photoName", "")).append("\" ")
				   .append("photoshownum=\"").append(ele.getStr("photoShowNum", "")).append("\" ")
				   .append("photoformat=\"").append(ele.getStr("photoFormat", "")).append("\" ")
				   .append("src=\"").append(eleSrc).append("\" ")
				   .append("class=\"sign-img photo\" date=\"").append(eleDate).append("\">");
	}

	public static String getInsertOnePhotoSql() {
		String insertSql = "insert into LAUNCH_PHOTO(LAUNCH_ID, PHOTO_NAME, PHOTO_PATH," +
				"PHOTO_FORMAT, PHOTO_NUMBER, PHOTO_SIZE,CREATOR,CREATE_TIME)values({}, '{}', '{}'," +
				"'{}', '{}', '{}','{}','{}')";
		return insertSql;
	}

	/**
	 * 插入一张照片
	 *
	 * @param data
	 */
	public static void insertOnePhoto(JSONObject data) {
		String insertSql = getInsertOnePhotoSql();
		String nowTime = DateUtil.now();
		insertSql = StrUtil.format(insertSql, data.getStr("id"), data.getStr("photoName", ""), data.getStr("photoPath", ""),
				data.getStr("photoFormat", ""), data.getStr("photoNumber", ""), data.getStr("photoSize", ""),
				data.getStr("creator", ""), nowTime);
		SqliteUtil.executeCommand(insertSql);
	}

	public static String getInsertOneSignSql() {
		return "insert into LAUNCH_SIGN( LAUNCH_ID, IMG, CREATOR, CREATE_TIME)values({}, '{}', '{}','{}')";
	}

	/**
	 * 插入一个签名
	 *
	 * @param data
	 */
	public static void insertOneSign(JSONObject data) {
		String insertSql = getInsertOneSignSql();
		String nowTime = DateUtil.now();
		insertSql = StrUtil.format(insertSql, data.getStr("id"), data.getStr("img"), data.getStr("creator"), nowTime);
		SqliteUtil.executeCommand(insertSql);
	}

	public static String getInsertOneLogSql() {
		return "insert into CONFIRM_LOG (USERNAME, LOG_TIME, " +
				"MODULE_TYPE, TABLE_NAME, TABLE_ID, " +
				"TABLE_PID, MODEL_ID, OPERATION," +
				"CONTENT, REQ_RESULT, MY_IP)values('{}','{}'," +
				"'{}','{}','{}'," +
				"'{}','{}','{}'," +
				"'{}','{}','{}')";
	}

	/**
	 * 获取最新照片序号
	 *
	 * @param id
	 * @return
	 */
	public static int GetNewPhotoNum(String id) {
		int num = 1;
		String sql = "select PHOTO_NUMBER from LAUNCH_PHOTO where LAUNCH_ID=" + id + " order by PHOTO_NUMBER desc";
		JSONArray nodes = SqliteUtil.executeQuery(sql);
		if (!nodes.isEmpty()) {
			num = nodes.getJSONObject(0).getInt("PHOTO_NUMBER") + 1;
		}
		return num;
	}

	/**
	 * 公共方法来处理JSON数据的更新和保存
	 *
	 * @param response       HttpServletResponse
	 * @param id             业务ID
	 * @param updateFunction 执行数据更新的函数
	 * @return 处理结果或消息
	 */
	public static void updateAndSaveJSONData(HttpServletResponse response, String id, Function<JSONObject, String> updateFunction) {
		Result.execute(() -> {
			// 执行SQL查询以获取现有的JSON数据
			JSONArray nodes = SqliteUtil.executeQuery("select * from LAUNCH_CONFIRM where ID=" + id);
			JSONObject object = nodes.getJSONObject(0);
			JSONObject saveData = dealObjNull(object.getStr("SAVE_DATA"));

			// 使用提供的函数来更新JSON数据
			String result = updateFunction.apply(saveData);

			// 将新的JSON数据保存回数据库
			String saveDataString = saveData.toString();
			String htmlData = TableUtil.TableData2Html(saveDataString);


			Entity entity = Entity.create("LAUNCH_CONFIRM")
					.set("SAVE_DATA", saveDataString)
					.set("HTML_DATA", htmlData);
			Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
			SqliteUtil.executeUpdateTransaction(entity, where);
			// 返回结果或消息
			return result;
		}, response);
	}

	/**
	 * 将对象中为空的属性替换为空字符串
	 *
	 * @param objStr
	 * @return
	 */
	public static JSONObject dealObjNull(String objStr) {
		return JSONUtil.parseObj(StrUtil.replace(objStr, "null", "\"\""));
	}

	public static void updateTableStatus(HttpServletResponse response, String id, String status) {
		updateAndSaveJSONData(response, id, saveData -> {
			JSONArray metas = saveData.getJSONArray("meta");
			if (ObjectUtil.isNotNull(metas)) {
				for (int m = 0; m < metas.size(); m++) {
					JSONObject meta = metas.getJSONObject(m);
					if (ObjectUtil.isNotNull(meta)) {
						meta.set("readOnly", false);
						meta.set("comment", "");
						if (status.equals("edit")) {
							//清除全部签名信息
							JSONArray eles = meta.getJSONArray("eles");
							if (ObjectUtil.isNotNull(eles)) {
								JSONArray newEles = new JSONArray();
								for (int i = 0; i < eles.size(); i++) {
									JSONObject ele = eles.getJSONObject(i);
									if (!ele.getStr("type", "").equals("sign")) {
										newEles.add(ele);
									}
								}
								meta.set("eles", newEles);
							}
						}
					}
				}
			}
			String nowTime = DateUtil.now();
			String sql = "update LAUNCH_CONFIRM set SAVE_TIME='" + nowTime + "',table_status='" + status + "' where id=" + id;
			SqliteUtil.executeCommand(sql);
			return "更新表格状态成功！";
		});
	}

	/**
	 * 导入Excel
	 */
	public static JSONObject importExcel(HttpServletRequest request, Function<String, JSONObject> updateFunction) {
		return Util.getUploadFiles(request, Util.getFileTempPath(), files -> {
			JSONObject file = files.getJSONObject(0);
			String fullPath = file.getStr("fullPath");
			return importExcel(fullPath, updateFunction);
		});
	}

	public static JSONObject importExcel(String fullPath, Function<String, JSONObject> updateFunction) {
		JSONObject res = ExcelUtil.excel2HandsonTable(fullPath);
		if (res.getBool("success")) {
			String tableData = res.getJSONObject("data").toString();
			if (PdfUtil.isExportPdf(tableData)) {
				res = updateFunction.apply(tableData);
			} else {
				res.set("success", false);
				res.set("msg", "上传的excel存在多余的合并单元格，请调整后重新上传！");
			}
		}
		return res;
	}

	/**
	 * 根据节点ID查询节点
	 *
	 * @param id
	 * @return
	 */
	public static JSONObject QueryNodeById(String id) {
		JSONObject res = new JSONObject();
		String sql = "select * " + TreeUtil.getSecuritySql() +
				"from LAUNCH_CONFIRM " +
				"where ID = " + id;
		JSONArray nodes = SqliteUtil.executeQuery(sql);
		if (nodes.isEmpty()) {
			res.set("success", false);
			res.set("msg", "节点不存在，请刷新后重试！");
		} else {
			JSONObject object = nodes.getJSONObject(0);
			String htmlStr = object.getStr("HTML_DATA");
			JSONObject tdImgsRes = PdfUtil.getTdImgs(htmlStr);
			object.set("html", tdImgsRes.getStr("html"));
			object.set("tdImgs", tdImgsRes.getStr("tdImgs"));
			res.set("success", true);
			res.set("data", object);
		}
		return res;
	}

	public static void saveTableData(String tableData, String id, String saveUser) {
		String nowTime = DateUtil.now();
		//将英文单引号替换为′
		tableData = StrUtil.replace(StrUtil.replace(tableData, "'", "′"), "？", "");
		String htmlData = TableUtil.TableData2Html(tableData);
		TimeInterval t = new TimeInterval();

		Entity entity = Entity.create("LAUNCH_CONFIRM")
				.set("SAVE_TIME", nowTime)
				.set("SAVE_USER", saveUser)
				.set("SAVE_DATA", tableData)
				.set("TABLE_STATUS", "edit")
				.set("HTML_DATA", htmlData);
		Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
		SqliteUtil.executeUpdateTransaction(entity, where);
	}

	public static File tableData2ImgZip(JSONObject data, String tempPath) {
		String saveDataStr = data.getStr("SAVE_DATA");
		String securityName = data.getStr("SECURITY_NAME");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(data.getStr("TABLE_NUM"))) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		String folderPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(folderPath);
		String zipFilePath = tempPath + File.separator + tableName + "照片（" + securityName + "）.zip";
		JSONObject saveData = dealObjNull(saveDataStr);
		JSONArray metas = saveData.getJSONArray("meta");
		String uploadPath = Util.getFileUploadPath();
		if (ObjectUtil.isNotNull(metas)) {
			for (int i = 0; i < metas.size(); i++) {
				JSONObject meta = metas.getJSONObject(i);
				JSONArray eles = meta.getJSONArray("eles");
				if (ObjectUtil.isNotNull(eles)) {
					for (int j = 0; j < eles.size(); j++) {
						JSONObject ele = eles.getJSONObject(j);
						String type = ele.getStr("type");
						if ("photo".equals(type)) {
							String photoPath = uploadPath + ele.getStr("photoPath");
							String photoShowNum = ele.getStr("photoShowNum").replace("/", "_").replace("\\", "_");
							String photoFormat = ele.getStr("photoFormat");
							if (FileUtil.exist(photoPath)) {
								try {
									if (FileUtil.isFile(photoPath)) {
										FileUtil.copy(photoPath, folderPath + photoShowNum + "（" + securityName + "）." + photoFormat, true);
									}
								} catch (Exception e) {
									System.out.println("tableData2ImgZip:" + e);
								}
							}
						}
					}
				}
			}
		}
		File zipFile = ZipUtil.zip(folderPath, zipFilePath);
		return zipFile;
	}


	public static void main(String[] args) {

	}
}

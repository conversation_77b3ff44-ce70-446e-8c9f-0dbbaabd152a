!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).deAT=e()}}(function(){return function i(f,u,d){function l(n,e){if(!u[n]){if(!f[n]){var o="function"==typeof require&&require;if(!e&&o)return o(n,!0);if(s)return s(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var t=u[n]={exports:{}};f[n][0].call(t.exports,function(e){return l(f[n][1][e]||e)},t,t.exports,i,f,u,d)}return u[n].exports}for(var s="function"==typeof require&&require,e=0;e<d.length;e++)l(d[e]);return l}({1:[function(e,n,o){"use strict";n.exports={languageTag:"de-AT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=de-AT.min.js.map

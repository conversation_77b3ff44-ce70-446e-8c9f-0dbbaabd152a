---
description: 数据库操作规范和SQLite使用指南
---

# 数据库操作规范

## 数据库架构
- **数据库**: SQLite (identifier.sqlite)
- **工具类**: [SqliteUtil.java](mdc:src/main/java/util/SqliteUtil.java)
- **主要表**: LAUNCH_CONFIRM (表格确认数据)

## 操作规范

### 1. 查询操作
```java
// 基础查询
JSONArray nodes = SqliteUtil.executeQuery("SELECT * FROM LAUNCH_CONFIRM WHERE id = " + id);

// 带参数查询 (推荐使用PreparedStatement避免SQL注入)
String sql = "SELECT * FROM LAUNCH_CONFIRM WHERE status = ? AND user = ?";
JSONArray result = SqliteUtil.executeQuery(sql, status, user);
```

### 2. 更新操作
```java
// 更新数据
String updateSql = "UPDATE LAUNCH_CONFIRM SET status = ?, updated_time = ? WHERE id = ?";
SqliteUtil.executeUpdate(updateSql, newStatus, currentTime, id);

// 插入数据
String insertSql = "INSERT INTO LAUNCH_CONFIRM (id, data, user, created_time) VALUES (?, ?, ?, ?)";
SqliteUtil.executeUpdate(insertSql, id, jsonData, user, createdTime);
```

### 3. 事务处理
```java
// 对于复杂操作，使用事务确保数据一致性
SqliteUtil.beginTransaction();
try {
    // 执行多个SQL操作
    SqliteUtil.executeUpdate(sql1, params1);
    SqliteUtil.executeUpdate(sql2, params2);
    SqliteUtil.commit();
} catch (Exception e) {
    SqliteUtil.rollback();
    throw new RuntimeException("操作失败", e);
}
```

## 数据表结构约定

### LAUNCH_CONFIRM表字段
- `id`: 主键ID
- `data`: JSON格式的表格数据
- `user`: 当前编辑用户
- `status`: 状态 (editing/completed等)
- `created_time`: 创建时间
- `updated_time`: 更新时间

## 性能优化建议

### 1. 索引使用
- 在经常查询的字段上创建索引
- 避免在小表上创建过多索引

### 2. 查询优化
- 使用LIMIT限制返回结果数量
- 避免SELECT *，明确指定需要的字段
- 合理使用WHERE条件过滤数据

### 3. 批量操作
```java
// 批量插入时使用事务
SqliteUtil.beginTransaction();
try {
    for (Data data : dataList) {
        SqliteUtil.executeUpdate(insertSql, data.getId(), data.getValue());
    }
    SqliteUtil.commit();
} catch (Exception e) {
    SqliteUtil.rollback();
}
```

## 注意事项
- 所有数据库操作必须通过SqliteUtil进行，禁止直接操作Connection
- 重要数据变更前先进行备份
- 定期清理过期数据，避免数据库文件过大
- 敏感数据存储前需要进行适当的加密处理
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Pikaday=t()}(this,(function(){"use strict";
/*!
   * Pikaday
   *
   * Copyright © 2014 <PERSON> | BSD & MIT license | https://github.com/Pikaday/Pikaday
   */const e=()=>window,t=()=>window.document,n=(e,t)=>window.setTimeout(e,t),i=function(e,t,n,i){e.addEventListener(t,n,!!i)},a=function(e,t,n,i){e.removeEventListener(t,n,!!i)},s=function(e,t){return-1!==(" "+e.className+" ").indexOf(" "+t+" ")},o=function(e,t){s(e,t)||(e.className=""===e.className?t:e.className+" "+t)},r=function(e,t){var n;e.className=(n=(" "+e.className+" ").replace(" "+t+" "," ")).trim?n.trim():n.replace(/^\s+|\s+$/g,"")},l=function(e){return/Array/.test(Object.prototype.toString.call(e))},h=function(e){return/Date/.test(Object.prototype.toString.call(e))&&!isNaN(e.getTime())},d=function(e){let t=e.getDay();return 0===t||6===t},u=function(e){return e%4==0&&e%100!=0||e%400==0},c=function(e,t){return[31,u(e)?29:28,31,30,31,30,31,31,30,31,30,31][t]},f=function(e){h(e)&&e.setHours(0,0,0,0)},m=function(e,t){return e.getTime()===t.getTime()},g=function(e,t,n){let i,a;for(i in t)a=void 0!==e[i],a&&"object"==typeof t[i]&&null!==t[i]&&void 0===t[i].nodeName?h(t[i])?n&&(e[i]=new Date(t[i].getTime())):l(t[i])?n&&(e[i]=t[i].slice(0)):e[i]=g({},t[i],n):!n&&a||(e[i]=t[i]);return e},p=function(e,n,i){let a=t().createEvent("HTMLEvents");a.initEvent(n,!0,!1),a=g(a,i),e.dispatchEvent(a)},y=function(e){return e.month<0&&(e.year-=Math.ceil(Math.abs(e.month)/12),e.month+=12),e.month>11&&(e.year+=Math.floor(Math.abs(e.month)/12),e.month-=12),e},D={field:null,bound:void 0,ariaLabel:"Use the arrow keys to pick a date",position:"bottom left",reposition:!0,format:"YYYY-MM-DD",toString:null,parse:null,defaultDate:null,setDefaultDate:!1,firstDay:0,firstWeekOfYearMinDays:4,formatStrict:!1,minDate:null,maxDate:null,yearRange:10,showWeekNumber:!1,pickWholeWeek:!1,minYear:0,maxYear:9999,minMonth:void 0,maxMonth:void 0,startRange:null,endRange:null,isRTL:!1,yearSuffix:"",showMonthAfterYear:!1,showDaysInNextAndPreviousMonths:!1,enableSelectionDaysInNextAndPreviousMonths:!1,numberOfMonths:1,mainCalendar:"left",container:void 0,blurFieldOnSelect:!0,i18n:{previousMonth:"Previous Month",nextMonth:"Next Month",months:["January","February","March","April","May","June","July","August","September","October","November","December"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},theme:null,events:[],onSelect:null,onOpen:null,onClose:null,onDraw:null,keyboardInput:!0},b=function(e,t,n){for(t+=e.firstDay;t>=7;)t-=7;return n?e.i18n.weekdaysShort[t]:e.i18n.weekdays[t]},_=function(e){let t=[],n="false";if(e.isEmpty){if(!e.showDaysInNextAndPreviousMonths)return'<td class="is-empty"></td>';t.push("is-outside-current-month"),e.enableSelectionDaysInNextAndPreviousMonths||t.push("is-selection-disabled")}return e.isDisabled&&t.push("is-disabled"),e.isToday&&t.push("is-today"),e.isSelected&&(t.push("is-selected"),n="true"),e.hasEvent&&t.push("has-event"),e.isInRange&&t.push("is-inrange"),e.isStartRange&&t.push("is-startrange"),e.isEndRange&&t.push("is-endrange"),'<td data-day="'+e.day+'" class="'+t.join(" ")+'" aria-selected="'+n+'"><button class="pika-button pika-day" type="button" data-pika-year="'+e.year+'" data-pika-month="'+e.month+'" data-pika-day="'+e.day+'">'+e.day+"</button></td>"},M=function(e,t,n,i,a){let s=new Date(i,n,t),o=e.hasMoment?e.moment(s).isoWeek():function(e,t){e.setHours(0,0,0,0);let n=e.getDate(),i=e.getDay(),a=t,s=a-1,o=function(e){return(e+7-1)%7};e.setDate(n+s-o(i));let r=new Date(e.getFullYear(),0,a),l=(e.getTime()-r.getTime())/864e5;return 1+Math.round((l-s+o(r.getDay()))/7)}(s,a);return'<td class="pika-week">'+o+"</td>"},k=function(e,t,n,i){return'<tr class="pika-row'+(n?" pick-whole-week":"")+(i?" is-selected":"")+'">'+(t?e.reverse():e).join("")+"</tr>"},v=function(e,t,n,i,a,s){let o,r,h,d,u,c=e._o,f=n===c.minYear,m=n===c.maxYear,g='<div id="'+s+'" class="pika-title" role="heading" aria-live="polite">',p=!0,y=!0;for(h=[],o=0;o<12;o++)h.push('<option value="'+(n===a?o-t:12+o-t)+'"'+(o===i?' selected="selected"':"")+(f&&o<c.minMonth||m&&o>c.maxMonth?' disabled="disabled"':"")+">"+c.i18n.months[o]+"</option>");for(d='<div class="pika-label">'+c.i18n.months[i]+'<select class="pika-select pika-select-month" tabindex="-1">'+h.join("")+"</select></div>",l(c.yearRange)?(o=c.yearRange[0],r=c.yearRange[1]+1):(o=n-c.yearRange,r=1+n+c.yearRange),h=[];o<r&&o<=c.maxYear;o++)o>=c.minYear&&h.push('<option value="'+o+'"'+(o===n?' selected="selected"':"")+">"+o+"</option>");return u='<div class="pika-label">'+n+c.yearSuffix+'<select class="pika-select pika-select-year" tabindex="-1">'+h.join("")+"</select></div>",c.showMonthAfterYear?g+=u+d:g+=d+u,f&&(0===i||c.minMonth>=i)&&(p=!1),m&&(11===i||c.maxMonth<=i)&&(y=!1),0===t&&(g+='<button class="pika-prev'+(p?"":" is-disabled")+'" type="button">'+c.i18n.previousMonth+"</button>"),t===e._o.numberOfMonths-1&&(g+='<button class="pika-next'+(y?"":" is-disabled")+'" type="button">'+c.i18n.nextMonth+"</button>"),g+"</div>"},w=function(e,t,n){return'<table cellpadding="0" cellspacing="0" class="pika-table" role="grid" aria-labelledby="'+n+'">'+function(e){let t,n=[];for(e.showWeekNumber&&n.push("<th></th>"),t=0;t<7;t++)n.push('<th scope="col"><abbr title="'+b(e,t)+'">'+b(e,t,!0)+"</abbr></th>");return"<thead><tr>"+(e.isRTL?n.reverse():n).join("")+"</tr></thead>"}(e)+("<tbody>"+t.join("")+"</tbody></table>")};function x(a){let o=this,r=o.config(a);o._onMouseDown=function(t){if(!o._v)return;let i=(t=t||e().event).target||t.srcElement;if(i)if(s(i,"is-disabled")||(!s(i,"pika-button")||s(i,"is-empty")||s(i.parentNode,"is-disabled")?s(i,"pika-prev")?o.prevMonth():s(i,"pika-next")&&o.nextMonth():(o.setDate(new Date(i.getAttribute("data-pika-year"),i.getAttribute("data-pika-month"),i.getAttribute("data-pika-day"))),r.bound&&n((function(){o.hide(),r.blurFieldOnSelect&&r.field&&r.field.blur()}),100))),s(i,"pika-select"))o._c=!0;else{if(!t.preventDefault)return t.returnValue=!1,!1;t.preventDefault()}},o._onChange=function(t){let n=(t=t||e().event).target||t.srcElement;n&&(s(n,"pika-select-month")?o.gotoMonth(n.value):s(n,"pika-select-year")&&o.gotoYear(n.value))},o._onKeyChange=function(t){if(t=t||e().event,o.isVisible())switch(t.keyCode){case 13:case 27:r.field&&r.field.blur();break;case 37:o.adjustDate("subtract",1);break;case 38:o.adjustDate("subtract",7);break;case 39:o.adjustDate("add",1);break;case 40:o.adjustDate("add",7);break;case 8:case 46:o.setDate(null)}},o._parseFieldValue=function(){if(r.parse)return r.parse(r.field.value,r.format);if(this.hasMoment){let e=this.moment(r.field.value,r.format,r.formatStrict);return e&&e.isValid()?e.toDate():null}return new Date(Date.parse(r.field.value))},o._onInputChange=function(e){let t;e.firedBy!==o&&(t=o._parseFieldValue(),h(t)&&o.setDate(t),o._v||o.show())},o._onInputFocus=function(){o.show()},o._onInputClick=function(){o.show()},o._onInputBlur=function(){let e=t().activeElement;do{if(s(e,"pika-single"))return}while(e=e.parentNode);o._c||(o._b=n((function(){o.hide()}),50)),o._c=!1},o._onClick=function(t){let n=(t=t||e().event).target||t.srcElement,a=n;if(n){!hasEventListeners&&s(n,"pika-select")&&(n.onchange||(n.setAttribute("onchange","return;"),i(n,"change",o._onChange)));do{if(s(a,"pika-single")||a===r.trigger)return}while(a=a.parentNode);o._v&&n!==r.trigger&&a!==r.trigger&&o.hide()}},o.el=t().createElement("div"),o.el.className="pika-single"+(r.isRTL?" is-rtl":"")+(r.theme?" "+r.theme:""),i(o.el,"mousedown",o._onMouseDown,!0),i(o.el,"touchend",o._onMouseDown,!0),i(o.el,"change",o._onChange),r.keyboardInput&&i(t(),"keydown",o._onKeyChange),r.field&&(r.container?r.container.appendChild(o.el):r.bound?t().body.appendChild(o.el):r.field.parentNode.insertBefore(o.el,r.field.nextSibling),i(r.field,"change",o._onInputChange),r.defaultDate||(r.defaultDate=o._parseFieldValue(),r.setDefaultDate=!0));let l=r.defaultDate;h(l)?r.setDefaultDate?o.setDate(l,!0):o.gotoDate(l):o.gotoDate(new Date),r.bound?(this.hide(),o.el.className+=" is-bound",i(r.trigger,"click",o._onInputClick),i(r.trigger,"focus",o._onInputFocus),i(r.trigger,"blur",o._onInputBlur)):this.show()}return x.prototype={config:function(e){this._o||(this._o=g({},D,!0));let t=g(this._o,e,!0);t.isRTL=!!t.isRTL,t.field=t.field&&t.field.nodeName?t.field:null,t.theme="string"==typeof t.theme&&t.theme?t.theme:null,t.bound=!!(void 0!==t.bound?t.field&&t.bound:t.field),t.trigger=t.trigger&&t.trigger.nodeName?t.trigger:t.field,t.disableWeekends=!!t.disableWeekends,t.disableDayFn="function"==typeof t.disableDayFn?t.disableDayFn:null;let n=parseInt(t.numberOfMonths,10)||1;if(t.numberOfMonths=n>4?4:n,h(t.minDate)||(t.minDate=!1),h(t.maxDate)||(t.maxDate=!1),t.minDate&&t.maxDate&&t.maxDate<t.minDate&&(t.maxDate=t.minDate=!1),t.minDate&&this.setMinDate(t.minDate),t.maxDate&&this.setMaxDate(t.maxDate),l(t.yearRange)){let e=(new Date).getFullYear()-10;t.yearRange[0]=parseInt(t.yearRange[0],10)||e,t.yearRange[1]=parseInt(t.yearRange[1],10)||e}else t.yearRange=Math.abs(parseInt(t.yearRange,10))||D.yearRange,t.yearRange>100&&(t.yearRange=100);return t},toString:function(e){return e=e||this._o.format,h(this._d)?this._o.toString?this._o.toString(this._d,e):this.hasMoment?this.moment(this._d).format(e):this._d.toDateString():""},getMoment:function(){return this.hasMoment?this.moment(this._d):null},setMoment:function(e,t){this.hasMoment&&this.moment.isMoment(e)&&this.setDate(e.toDate(),t)},useMoment:function(e){this.hasMoment=!0,this.moment=e},getDate:function(){return h(this._d)?new Date(this._d.getTime()):null},setDate:function(e,t){if(!e)return this._d=null,this._o.field&&(this._o.field.value="",p(this._o.field,"change",{firedBy:this})),this.draw();if("string"==typeof e&&(e=new Date(Date.parse(e))),!h(e))return;let n=this._o.minDate,i=this._o.maxDate;h(n)&&e<n?e=n:h(i)&&e>i&&(e=i),this._d=new Date(e.getTime()),f(this._d),this.gotoDate(this._d),this._o.field&&(this._o.field.value=this.toString(),p(this._o.field,"change",{firedBy:this})),t||"function"!=typeof this._o.onSelect||this._o.onSelect.call(this,this.getDate())},clear:function(){this.setDate(null)},gotoDate:function(e){let t=!0;if(h(e)){if(this.calendars){let n=new Date(this.calendars[0].year,this.calendars[0].month,1),i=new Date(this.calendars[this.calendars.length-1].year,this.calendars[this.calendars.length-1].month,1),a=e.getTime();i.setMonth(i.getMonth()+1),i.setDate(i.getDate()-1),t=a<n.getTime()||i.getTime()<a}t&&(this.calendars=[{month:e.getMonth(),year:e.getFullYear()}],"right"===this._o.mainCalendar&&(this.calendars[0].month+=1-this._o.numberOfMonths)),this.adjustCalendars()}},adjustDate:function(e,t){let n,i=this.getDate()||new Date,a=24*parseInt(t)*60*60*1e3;"add"===e?n=new Date(i.valueOf()+a):"subtract"===e&&(n=new Date(i.valueOf()-a)),this.setDate(n)},adjustCalendars:function(){this.calendars[0]=y(this.calendars[0]);for(let e=1;e<this._o.numberOfMonths;e++)this.calendars[e]=y({month:this.calendars[0].month+e,year:this.calendars[0].year});this.draw()},gotoToday:function(){this.gotoDate(new Date)},gotoMonth:function(e){isNaN(e)||(this.calendars[0].month=parseInt(e,10),this.adjustCalendars())},nextMonth:function(){this.calendars[0].month++,this.adjustCalendars()},prevMonth:function(){this.calendars[0].month--,this.adjustCalendars()},gotoYear:function(e){isNaN(e)||(this.calendars[0].year=parseInt(e,10),this.adjustCalendars())},setMinDate:function(e){e instanceof Date?(f(e),this._o.minDate=e,this._o.minYear=e.getFullYear(),this._o.minMonth=e.getMonth()):(this._o.minDate=D.minDate,this._o.minYear=D.minYear,this._o.minMonth=D.minMonth,this._o.startRange=D.startRange),this.draw()},setMaxDate:function(e){e instanceof Date?(f(e),this._o.maxDate=e,this._o.maxYear=e.getFullYear(),this._o.maxMonth=e.getMonth()):(this._o.maxDate=D.maxDate,this._o.maxYear=D.maxYear,this._o.maxMonth=D.maxMonth,this._o.endRange=D.endRange),this.draw()},setStartRange:function(e){this._o.startRange=e},setEndRange:function(e){this._o.endRange=e},draw:function(e){if(!this._v&&!e)return;let t,i=this._o,a=i.minYear,s=i.maxYear,o=i.minMonth,r=i.maxMonth,l="";this._y<=a&&(this._y=a,!isNaN(o)&&this._m<o&&(this._m=o)),this._y>=s&&(this._y=s,!isNaN(r)&&this._m>r&&(this._m=r));for(let e=0;e<i.numberOfMonths;e++)t="pika-title-"+Math.random().toString(36).replace(/[^a-z]+/g,"").substr(0,2),l+='<div class="pika-lendar">'+v(this,e,this.calendars[e].year,this.calendars[e].month,this.calendars[0].year,t)+this.render(this.calendars[e].year,this.calendars[e].month,t)+"</div>";this.el.innerHTML=l,i.bound&&"hidden"!==i.field.type&&n((function(){i.trigger.focus()}),1),"function"==typeof this._o.onDraw&&this._o.onDraw(this),i.bound&&i.field.setAttribute("aria-label",i.ariaLabel)},adjustPosition:function(){let n,i,a,s,l,h,d,u,c,f,m;this._o.container||(this.el.style.position="absolute",n=this._o.trigger,i=this.el.offsetWidth,a=this.el.offsetHeight,s=e().innerWidth||t().documentElement.clientWidth,l=e().innerHeight||t().documentElement.clientHeight,h=t().body.scrollTop||t().documentElement.scrollTop,f=!0,m=!0,c=n.getBoundingClientRect(),d=c.left+e().pageXOffset,u=c.bottom+e().pageYOffset,(this._o.reposition&&d+i>s||this._o.position.indexOf("right")>-1&&d-i+n.offsetWidth>0)&&(d=d-i+n.offsetWidth,f=!1),(this._o.reposition&&u+a>l+h||this._o.position.indexOf("top")>-1&&u-a-n.offsetHeight>0)&&(u=u-a-n.offsetHeight,m=!1),d<0&&(d=0),u<0&&(u=0),this.el.style.left=d+"px",this.el.style.top=u+"px",o(this.el,f?"left-aligned":"right-aligned"),o(this.el,m?"bottom-aligned":"top-aligned"),r(this.el,f?"right-aligned":"left-aligned"),r(this.el,m?"top-aligned":"bottom-aligned"))},render:function(e,t,n){let i=this._o,a=new Date,s=c(e,t),o=new Date(e,t,1).getDay(),r=[],l=[];f(a),i.firstDay>0&&(o-=i.firstDay,o<0&&(o+=7));let u=0===t?11:t-1,g=11===t?0:t+1,p=0===t?e-1:e,y=11===t?e+1:e,D=c(p,u),b=s+o,v=b;for(;v>7;)v-=7;b+=7-v;let x=!1;for(let n=0,c=0;n<b;n++){let f=new Date(e,t,n-o+1),b=!!h(this._d)&&m(f,this._d),v=m(f,a),w=-1!==i.events.indexOf(f.toDateString()),R=n<o||n>=s+o,N=n-o+1,S=t,C=e,I=i.startRange&&m(i.startRange,f),T=i.endRange&&m(i.endRange,f),Y=i.startRange&&i.endRange&&i.startRange<f&&f<i.endRange;R&&(n<o?(N=D+N,S=u,C=p):(N-=s,S=g,C=y));let O={day:N,month:S,year:C,hasEvent:w,isSelected:b,isToday:v,isDisabled:i.minDate&&f<i.minDate||i.maxDate&&f>i.maxDate||i.disableWeekends&&d(f)||i.disableDayFn&&i.disableDayFn(f),isEmpty:R,isStartRange:I,isEndRange:T,isInRange:Y,showDaysInNextAndPreviousMonths:i.showDaysInNextAndPreviousMonths,enableSelectionDaysInNextAndPreviousMonths:i.enableSelectionDaysInNextAndPreviousMonths};i.pickWholeWeek&&b&&(x=!0),l.push(_(O)),7==++c&&(i.showWeekNumber&&l.unshift(M(this,n-o,t,e,i.firstWeekOfYearMinDays)),r.push(k(l,i.isRTL,i.pickWholeWeek,x)),l=[],c=0,x=!1)}return w(i,r,n)},isVisible:function(){return this._v},show:function(){this.isVisible()||(this._v=!0,this.draw(),r(this.el,"is-hidden"),this._o.bound&&(i(t(),"click",this._onClick),this.adjustPosition()),"function"==typeof this._o.onOpen&&this._o.onOpen.call(this))},hide:function(){let e=this._v;!1!==e&&(this._o.bound&&a(t(),"click",this._onClick),this._o.container||(this.el.style.position="static",this.el.style.left="auto",this.el.style.top="auto"),o(this.el,"is-hidden"),this._v=!1,void 0!==e&&"function"==typeof this._o.onClose&&this._o.onClose.call(this))},destroy:function(){let e=this._o;this.hide(),a(this.el,"mousedown",this._onMouseDown,!0),a(this.el,"touchend",this._onMouseDown,!0),a(this.el,"change",this._onChange),e.keyboardInput&&a(t(),"keydown",this._onKeyChange),e.field&&(a(e.field,"change",this._onInputChange),e.bound&&(a(e.trigger,"click",this._onInputClick),a(e.trigger,"focus",this._onInputFocus),a(e.trigger,"blur",this._onInputBlur))),this.el.parentNode&&this.el.parentNode.removeChild(this.el)}},x}));
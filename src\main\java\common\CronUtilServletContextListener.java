package common;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import cn.hutool.cron.CronUtil;
import util.Util;
import util.TreeUtil;

@WebListener
public class CronUtilServletContextListener implements ServletContextListener {

	@Override
	public void contextInitialized(ServletContextEvent sce) {
		System.out.println("系统启动中，正在初始化...");
		
		// 自动创建性能优化索引
		TreeUtil.autoInitPerformanceIndexes();
		
		Util.backupDb();
		Util.backupStoreFile();
		Util.deleteTempFile();
		Util.clearExpiredDownloadFiles();
		Util.clearExpiredFiles();
		CronUtil.setMatchSecond(true);
		// Tomcat启动时执行
		CronUtil.start();
		
		System.out.println("系统启动完成！");
	}

	@Override
	public void contextDestroyed(ServletContextEvent sce) {
		Util.backupDb();
		Util.backupStoreFile();
		Util.deleteTempFile();
		Util.clearExpiredDownloadFiles();
		Util.clearExpiredFiles();
		CronUtil.stop();
	}
}

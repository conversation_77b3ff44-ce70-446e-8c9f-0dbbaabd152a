package servlet;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.db.Entity;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import common.BaseServlet;
import common.Result;
import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import util.*;


@WebServlet("/tree")
public class TreeServlet extends BaseServlet {

	/**
	 * 查询结构树的根节点，并返回根节点及其子节点的信息。
	 */
	public void QueryTreeRoot(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String nodeSql = "SELECT * " + "FROM LAUNCH_CONFIRM " +
					"WHERE TYPE IN ('root', 'folder', 'model') " + "ORDER BY LEVEL_NUM, SORT";
			return TreeUtil.dealNodesIsParent(SqliteUtil.executeQuery(nodeSql));
		}, response);
	}

	/**
	 * 查询结构树节点
	 */
	public void QueryTreeNodeByPid(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String pId = Util.getPostRequestParam(request, "ID");
			String nodeSql = "select ID,PID,NAME,TABLE_NUM,TYPE,SORT,FILE_FORMAT," +
					"FILE_PATH,LEVEL_NUM,TABLE_STATUS,SECURITY " +
					"from LAUNCH_CONFIRM where PID=" + pId + " ORDER BY LEVEL_NUM, SORT";
			return TreeUtil.dealNodesIsParent(SqliteUtil.executeQuery(nodeSql));
		}, response);
	}

	/**
	 * 拖动节点来改变节点的排序
	 */
	public void UpdateNodeSort(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String str = Util.getPostRequestParam(request, "str");
			List<String> strArr = StrUtil.split(str, ",");
			for (String s : strArr) {
				List<String> tempArr = StrUtil.split(s, ":");
				String treeId = tempArr.get(0);
				String sort = tempArr.get(1);
				Entity entity = Entity.create("LAUNCH_CONFIRM")
						.set("SORT", sort);
				Entity where = Entity.create("LAUNCH_CONFIRM").set("ID", treeId);
				SqliteUtil.executeUpdateTransaction(entity, where);
			}
			return "更新成功！";
		}, response);
	}

	/**
	 * 添加节点
	 */
	public void AddTableNode(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String nextId = TreeUtil.AddTableNode(params);
			return JSONUtil.createObj().set("id", nextId);
		}, response);
	}

	/**
	 * 复制节点
	 */
	public void CopyNode(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String pid = params.getStr("pid");
			String name = params.getStr("name");
			String tableNum = params.getStr("tableNum");
			String creator = params.getStr("creator");
			String security = params.getStr("security");
			String newId = TreeUtil.getNewTreeId();
			int lastSort = TreeUtil.getLastSort(pid);
			//查询出所有需要复制的数据
			String queryAllChildSql = TreeUtil.getQueryAllChildSql(id);
			JSONArray srcData = SqliteUtil.executeQuery(queryAllChildSql);

			TreeUtil.copyNode(srcData, newId, true, creator, security);
			Entity entity = Entity.create("LAUNCH_CONFIRM")
					.set("NAME", name)
					.set("TABLE_NUM", tableNum)
					.set("PID", pid)
					.set("SORT", lastSort);
			Entity where = Entity.create("LAUNCH_CONFIRM").set("ID", newId);
			SqliteUtil.executeUpdateTransaction(entity, where);
			return JSONUtil.createObj().set("id", newId);
		}, response);
	}

	/**
	 * 更新节点名称或者序号
	 */
	public void UpdateTableNode(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String name = params.getStr("name");
			String tableNum = params.getStr("tableNum");
			String hasLockEdit = params.getStr("hasLockEdit");
			String security = params.getStr("security");

			JSONArray nodes = SqliteUtil.executeQuery("SELECT TABLE_STATUS,TYPE from LAUNCH_CONFIRM WHERE ID=" + id);
			JSONObject res = JSONUtil.createObj();
			if (!nodes.isEmpty()) {
				if (StrUtil.equals(nodes.getJSONObject(0).getStr("TABLE_STATUS"), "sign")) {
					res.set("success", false);
					res.set("msg", "该节点已锁定，不能修改！");
				} else {
					if (StrUtil.equals(hasLockEdit, "true")) {
						String sql = "update LAUNCH_CONFIRM set TABLE_NUM='" + tableNum + "'," + "NAME='" + name +
								"',SECURITY='" + security + "' where ID=" + id;
						SqliteUtil.executeCommand(sql);
						//如果是a表的话需要将下面的b表的密级一同修改
						if (StrUtil.equals(nodes.getJSONObject(0).getStr("TYPE"), "a")) {
							String childrenSql = "update LAUNCH_CONFIRM set SECURITY='" + security +
									"' where PID=" + id;
							SqliteUtil.executeCommand(childrenSql);
						}
						// 清除缓存，因为节点信息发生了变化
						TreeUtil.clearSearchDropdownCache();
						res.set("success", true);
						res.set("msg", "修改成功！");
					} else {
						res.set("success", false);
						res.set("msg", "无权限编辑！");
					}
				}
			} else {
				res.set("success", false);
				res.set("msg", "节点丢失，刷新后重试！");
			}
			return res;
		}, response);
	}

	/**
	 * 删除节点
	 */
	public void DeleteNode(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject res = new JSONObject();
			String id = Util.getPostRequestParam(request, "id");
			String selSql = TreeUtil.getQueryAllChildSql(id);
			Console.log(DateUtil.now(), "selSql:", selSql);
			JSONArray nodes = SqliteUtil.executeQuery(selSql);
			List<String> signTableList = new ArrayList<>();
			List<String> rowSignList = new ArrayList<>();
			List<String> idList = new ArrayList<>();
			for (int i = 0; i < nodes.size(); i++) {
				JSONObject node = nodes.getJSONObject(i);
				idList.add(node.getStr("ID"));
				String text = node.getStr("TABLE_NUM") + ":" + node.getStr("NAME");
				if (StrUtil.equals("sign", node.getStr("TABLE_STATUS"))) {
					signTableList.add(text);
				} else {
					String htmlData = node.getStr("HTML_DATA", "");
					if (StrUtil.contains(htmlData, "lock-\"true\"")) {
						rowSignList.add(text);
					}
				}
			}
			if (signTableList.isEmpty() && rowSignList.isEmpty()) {
				String ids = StrUtil.join(",", idList);
				SqliteUtil.executeCommand("delete from LAUNCH_CONFIRM where ID in (" + ids + ")");
				// 清除缓存，因为树结构发生了变化
				TreeUtil.clearSearchDropdownCache();
				res.set("success", true);
				res.set("msg", "删除成功！");
			} else {
				String msg = "";
				if (!signTableList.isEmpty()) {
					msg += "节点[" + StrUtil.join(",", signTableList) + "]已经锁定；";
				}
				if (!rowSignList.isEmpty()) {
					msg += "节点[" + StrUtil.join(",", rowSignList) + "]存在部分锁定；";
				}
				res.set("success", false);
				res.set("msg", msg + "不允许删除！");
			}
			return res;
		}, response);
	}

	/**
	 * 查询所有型号分类
	 */
	public void QueryFolder(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String sql = "select * from LAUNCH_CONFIRM where TYPE='folder'";
			return SqliteUtil.executeQuery(sql);
		}, response);
	}

	/**
	 * 移动节点
	 */
	public void MoveNode(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String targetId = params.getStr("targetId");
			String sql = "update LAUNCH_CONFIRM set PID = " + targetId + " where ID =" + id;
			SqliteUtil.executeCommand(sql);
			return "移动节点成功！";
		}, response);
	}

	/**
	 * 选择型号分类
	 */
	public void SelectFolder(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String folderId = params.getStr("folderId");
			String sql = "update LAUNCH_CONFIRM set PID = " + folderId + " where ID =" + id;
			SqliteUtil.executeCommand(sql);
			return "更新成功！";
		}, response);
	}

	/**
	 * 查询节点的所有父节点
	 */
	public void QueryAllPId(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String sql = TreeUtil.getQueryAllParentSql(id, "", "order by LEVEL_NUM");
			return SqliteUtil.executeQuery(sql);
		}, response);
	}

	/**
	 * 查询A表下的所有B表信息
	 */
	public void QueryModelNodesById(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String sql = "select NAME, TABLE_NUM, ID  " +
					"from LAUNCH_CONFIRM  " +
					"where pid =   " + id +
					" order by LENGTH(TRIM(TABLE_NUM)) desc, TABLE_NUM";
			return SqliteUtil.executeQuery(sql);
		}, response);
	}

	/**
	 * 导出树形Excel
	 */

	public void ExportMoreExcel(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String id = request.getParameter("id");
			String pid = request.getParameter("pid");
			JSONArray nodes = TreeUtil.buildTree(id, pid);
			String fileTempPath = Util.getFileTempPath();
			ExcelUtil.exportTreeToExcel(fileTempPath, nodes);
			File resFile = ZipUtil.zip(fileTempPath, Charset.forName("GBK"));
			if (!nodes.isEmpty()) {
				resFile = PdfUtil.renameRootName(nodes, resFile);
			}
			return resFile;
		}, response);
	}

	/**
	 * 导出树形Pdf（使用内存优化方式）
	 */

	public void ExportMorePdf(HttpServletRequest request, HttpServletResponse response) {
		String id = request.getParameter("id");
		String pid = request.getParameter("pid");
		System.out.println(DateUtil.now() + " - [ExportMorePdf] 开始PDF导出，节点ID: " + id + ", PID: " + pid);

		try {
			// 使用PDF优化树形结构构建，包含表格内容但避免内存溢出
			JSONArray nodes = TreeUtil.buildTreeForPdf(id, pid);
			System.out.println(DateUtil.now() + " - [ExportMorePdf] PDF优化树形结构构建完成");

			JSONObject res = PdfUtil.exportTreeToPdf(nodes, Util.getFileTempPath());

			// 及时清理nodes对象
			nodes.clear();
			nodes = null;
			System.gc(); // 建议GC回收

			System.out.println(DateUtil.now() + " - [ExportMorePdf] PDF导出完成");
			Result.outFileRes(response, res);
		} catch (OutOfMemoryError e) {
			System.out.println(DateUtil.now() + " - [ExportMorePdf] 内存溢出错误: " + e.getMessage());
			JSONObject errorRes = new JSONObject();
			errorRes.set("success", false);
			errorRes.set("msg", "PDF导出失败，内存不足：" + e.getMessage());
			Result.outFileRes(response, errorRes);
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - [ExportMorePdf] 其他错误: " + e.getMessage());
			e.printStackTrace();
			JSONObject errorRes = new JSONObject();
			errorRes.set("success", false);
			errorRes.set("msg", "PDF导出失败：" + e.getMessage());
			Result.outFileRes(response, errorRes);
		}
	}

	public void ReqGenerateFile(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String tableId = params.getStr("tableId");
			String tablePId = params.getStr("tablePId");
			int exportType = params.getInt("exportType");
			String creator = params.getStr("creator");
			TreeUtil.generateFile(tableId, tablePId, exportType, creator);
			return "请求成功！";
		}, response);

	}

	/**
	 * 导出pdf压缩包
	 */

	public void ExportPdfZip(HttpServletRequest request, HttpServletResponse response) {
		String id = request.getParameter("id");
		JSONObject res = PdfUtil.exportPdfZip(id);
		Result.outFileRes(response, res);
	}

	/**
	 * 导出数据压缩包
	 */

	public void ExportZip(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String id = request.getParameter("id");
			try {
				return TreeUtil.exportZip(id);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}, response);
	}

	/**
	 * 导入压缩包
	 */
	public void ImportZip(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String id = request.getParameter("id");
			String pid = request.getParameter("pid");
			String type = request.getParameter("type");
			String fileTempPath = Util.getFileTempPath();
			return Util.getUploadFiles(request, fileTempPath, files -> {
				JSONObject file = files.getJSONObject(0);
				String fullPath = file.getStr("fullPath");
				return TreeUtil.importZip(fullPath, fileTempPath, id, pid, type);
			});
		}, response);
	}

	/**
	 * @param request
	 * @param response
	 */
	public void ImportBigZip(HttpServletRequest request, HttpServletResponse response) {
		JSONObject res = Util.getUploadBigFile(request);
		if (res.getBool("success") && res.getBool("isAll", false)) {
			JSONObject extraData = res.getJSONObject("extraData");
			res = TreeUtil.importZip(res.getStr("file"), Util.getFileTempPath(),
					extraData.getStr("tableId"), extraData.getStr("tablePId"), extraData.getStr("srcType"));
		}
		response.setCharacterEncoding("UTF-8");
		ServletUtil.write(response, res.toString(), "application/json");
	}

	public void QueryDownloadSearch(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String username = params.getStr("creator");
			String s_folder = params.getStr("s_folder");
			String s_model = params.getStr("s_model");
			String s_project = params.getStr("s_project");
			return TreeUtil.queryDownloadSearch(username, s_folder, s_model, s_project);
		}, response);
	}

	public void QueryDownloadTable(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String creator = request.getParameter("creator");
			String s_folder = request.getParameter("s_folder");
			String s_model = request.getParameter("s_model");
			String s_project = request.getParameter("s_project");
			String s_a = request.getParameter("s_a");
			int pageNumber = Convert.toInt(request.getParameter("page"));
			int limit = Convert.toInt(request.getParameter("limit"));
			int offset = limit * (pageNumber - 1);
			
			// 使用优化后的查询方法
			String baseSql = TreeUtil.getDownloadTableSqlOptimized(creator);
			
			// 构建WHERE条件
			StringBuilder whereBuilder = new StringBuilder(" WHERE 1=1");
			if (StrUtil.isNotBlank(s_folder)) {
				whereBuilder.append(" AND FOLDER LIKE '%").append(s_folder).append("%'");
			}
			if (StrUtil.isNotBlank(s_model)) {
				whereBuilder.append(" AND MODEL LIKE '%").append(s_model).append("%'");
			}
			if (StrUtil.isNotBlank(s_project)) {
				whereBuilder.append(" AND PROJECT LIKE '%").append(s_project).append("%'");
			}
			if (StrUtil.isNotBlank(s_a)) {
				whereBuilder.append(" AND A_TABLE LIKE '%").append(s_a).append("%'");
			}
			
			String whereClause = whereBuilder.toString();
			
			// 优化分页查询：无筛选时走快速计数路径
			boolean noFilters = StrUtil.isBlank(s_folder) && StrUtil.isBlank(s_model) && StrUtil.isBlank(s_project) && StrUtil.isBlank(s_a);
			int count;
			if (noFilters) {
				String fastCount = "SELECT COUNT(*) as COUNT FROM CONFIRM_DOWNLOAD WHERE CREATOR='" + creator + "'";
				count = SqliteUtil.executeQuery(fastCount).getJSONObject(0).getInt("COUNT");
			} else {
				String countSql = "SELECT COUNT(*) as COUNT FROM (" + baseSql + ") t" + whereClause;
				count = SqliteUtil.executeQuery(countSql).getJSONObject(0).getInt("COUNT");
			}
			
			JSONArray nodes = new JSONArray();
			if (count > 0) {
				String pageSql = "SELECT * FROM (" + baseSql + ") t" + whereClause + 
					" ORDER BY t.START_TIME DESC LIMIT " + limit + " OFFSET " + offset;
				nodes = SqliteUtil.executeQuery(pageSql);
			}
			
			JSONObject res = new JSONObject();
			res.set("success", true);
			res.set("data", nodes);
			res.set("code", 0);
			res.set("count", count);
			res.set("msg", "");
			return res;
		}, response);
	}

	public void GetWorkType(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(Util::getWorkType, response);
	}

	/**
	 * 选择工作类型
	 */
	public void SelectWorkType(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String workType = params.getStr("workType");
			TreeUtil.selectWorkType(id, workType);
			return "更新成功！";
		}, response);
	}


	public void GetPostType(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(Util::getPostType, response);
	}

	public void ExportWorkHours(HttpServletRequest request, HttpServletResponse response) {
		String id = request.getParameter("id");
		String name = request.getParameter("name");
		JSONObject res = TreeUtil.exportWorkHours(id,name);
		Result.outFileRes(response, res);
	}

	/**
	 * 创建性能优化索引
	 */
	public void CreatePerformanceIndexes(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			TreeUtil.createPerformanceIndexes();
			return "性能优化索引创建完成";
		}, response);
	}

	/**
	 * 删除下载记录
	 */
	public void DeleteDownloadRecord(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String downloadId = params.getStr("downloadId");
			
			if (StrUtil.isBlank(downloadId)) {
				JSONObject res = new JSONObject();
				res.set("success", false);
				res.set("msg", "下载记录ID不能为空");
				return res;
			}
			
			// 查询下载记录信息，获取文件路径
			String querySql = "SELECT FILE_PATH FROM CONFIRM_DOWNLOAD WHERE ID = " + downloadId;
			JSONArray records = SqliteUtil.executeQuery(querySql);
			
			if (records.isEmpty()) {
				JSONObject res = new JSONObject();
				res.set("success", false);
				res.set("msg", "下载记录不存在");
				return res;
			}
			
			JSONObject record = records.getJSONObject(0);
			String filePath = record.getStr("FILE_PATH");
			
			// 删除数据库记录
			String deleteSql = "DELETE FROM CONFIRM_DOWNLOAD WHERE ID = " + downloadId;
			SqliteUtil.executeCommand(deleteSql);
			
			// 删除相关文件（如果文件存在）
			if (StrUtil.isNotBlank(filePath)) {
				try {
					File file = new File(filePath);
					if (file.exists()) {
						boolean deleted = file.delete();
						Console.log(DateUtil.now(), "删除文件:", filePath, "结果:", deleted);
					}
				} catch (Exception e) {
					Console.log(DateUtil.now(), "删除文件失败:", filePath, "错误:", e.getMessage());
					// 文件删除失败不影响数据库记录删除
				}
			}
			
			JSONObject res = new JSONObject();
			res.set("success", true);
			res.set("msg", "删除成功");
			return res;
		}, response);
	}
}

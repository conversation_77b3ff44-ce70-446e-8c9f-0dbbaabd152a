---
globs: *.js,*.css,*.html
description: 前端开发规范和组件使用指南
---

# 前端开发规范

## 技术栈
- **jQuery 3.7.1**: DOM操作和Ajax请求
- **Handsontable**: 专业表格编辑组件
- **Layui**: UI框架
- **WebUploader**: 文件上传组件

## 页面结构规范

### HTML结构
```html
<!-- 主要页面布局 -->
<div class="layui-fluid">
    <div class="layui-row">
        <div class="col-tree layui-col-md3">
            <!-- 树形导航区域 -->
        </div>
        <div class="col-table layui-col-md9">
            <!-- 表格编辑区域 -->
        </div>
    </div>
</div>
```

### JavaScript开发规范

#### 1. 初始化函数
```javascript
function initPage() {
    // 页面初始化逻辑
    // 计算布局尺寸，绑定事件等
}

function registerEvent() {
    // 事件注册函数
    // 统一管理页面事件绑定
}
```

#### 2. Ajax请求规范
```javascript
// 使用jQuery.post发送请求
$.post('/table', {
    act: 'methodName',
    param1: 'value1'
}, function(result) {
    if (result.success) {
        // 处理成功逻辑
    } else {
        layui.use('layer', function() {
            layer.msg(result.msg);
        });
    }
}, 'json');
```

#### 3. Handsontable配置
```javascript
var hot = new Handsontable(container, {
    data: data,
    colHeaders: true,
    rowHeaders: true,
    contextMenu: true,
    manualColumnResize: true,
    manualRowResize: true,
    // 其他配置...
});
```

## CSS规范
- 使用Layui的Grid系统进行布局
- 自定义样式放在static/css目录下
- 避免使用内联样式，统一在CSS文件中管理

## 文件组织
```
web/page/confirm/
├── confirm.html      # 主页面
├── confirm.js        # 主要业务逻辑
├── confirm.css       # 页面样式
└── js/              # 子模块JS文件

web/plugins/         # 第三方组件
├── handsontable/    # 表格组件
├── layui/          # UI框架
├── webuploader/    # 文件上传
└── ztree/          # 树形组件
```

## 注意事项
- 所有用户提示信息使用Layui的layer组件
- 表格数据编辑统一使用Handsontable
- 文件上传功能使用WebUploader组件
- 避免全局变量污染，使用命名空间或模块化
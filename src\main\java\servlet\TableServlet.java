package servlet;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static util.TableUtil.updateAndSaveJSONData;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import common.BaseServlet;
import common.Result;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import util.*;

@WebServlet("/table")
public class TableServlet extends BaseServlet {

	/**
	 * 使表格进入编辑状态，并且记录当前编辑的人员，如果表格正在编辑的话返回编辑人员的名称
	 *
	 * @param request
	 * @param response
	 */
	public void OpenEdit(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String user = params.getStr("user");
			String id = params.getStr("id");
			int isUpdate = params.getInt("isUpdate");

			JSONObject res = new JSONObject();

			//首先查看当前表格是否处于编辑状态
			JSONArray nodes = SqliteUtil.executeQuery("select * from LAUNCH_CONFIRM where id = " + id);
			if (nodes.isEmpty()) {
				res.set("success", false);
				res.set("msg", "节点不存在,请刷新后重试！");
			} else {
				String currentEditor = nodes.getJSONObject(0).getStr("CURRENT_EDITOR", "no_user");
				if (currentEditor.equals("no_user")) {
					res.set("success", true);
					if (isUpdate == 1) {
						String nowTime = DateUtil.now();
						String updateSql = "update LAUNCH_CONFIRM SET CURRENT_EDITOR='" + user + "',EDIT_TIME='" + nowTime + "' where id=" + id;
						Entity entity = Entity.create("LAUNCH_CONFIRM")
								.set("CURRENT_EDITOR", user)
								.set("EDIT_TIME", nowTime);
						Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
						SqliteUtil.executeUpdateTransaction(entity, where);
					}
				} else {
					if (StrUtil.equals(user, currentEditor)) {
						res.set("success", true);
					} else {
						res.set("success", false);
						res.set("msg", "当前表格正在被" + currentEditor + "编辑中，请稍后重试！");
					}
				}
			}
			return res;
		}, response);
	}

	public void CloseEdit(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			Entity entity = Entity.create("LAUNCH_CONFIRM")
					.set("CURRENT_EDITOR", "no_user")
					.set("EDIT_TIME", null);
			Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
			SqliteUtil.executeUpdateTransaction(entity, where);
			return "退出编辑成功！";
		}, response);
	}

	public void QueryNodeById(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			return TableUtil.QueryNodeById(id);
		}, response);
	}

	/**
	 * 保存表格数据
	 *
	 * @param request
	 * @param response
	 */
	public void SaveTableData(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			String tableData = params.getStr("tableData");
			String saveUser = params.getStr("saveUser");
			TableUtil.saveTableData(tableData, id, saveUser);
			return "保存成功！";
		}, response);
	}

	public void UpdateTableHeader(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			int header = params.getInt("header");
			Entity entity = Entity.create("LAUNCH_CONFIRM")
					.set("TABLE_HEADER", header);
			Entity where = Entity.create("LAUNCH_CONFIRM").set("id", id);
			SqliteUtil.executeUpdateTransaction(entity, where);
			return "更新表头行数成功！";
		}, response);
	}

	/**
	 * 获取该表格的最新图号
	 *
	 * @param request
	 * @param response
	 */
	public void GetNewPhotoNum(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			String id = params.getStr("id");
			return TableUtil.GetNewPhotoNum(id);
		}, response);
	}

	/**
	 * 只添加到图片数据库中 不写入到确认表中 用于在编辑状态下的上传图片
	 *
	 * @param request
	 * @param response
	 */
	public void AddOnePhoto(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			JSONObject data = params.getJSONObject("data");
			TableUtil.insertOnePhoto(data);
			return "添加成功！";

		}, response);
	}

	public void AddPhotos(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		updateAndSaveJSONData(response, id, saveData -> {
			Console.log(DateUtil.now(), "1.1:");
			JSONArray datas = params.getJSONArray("datas");
			String nowDate = DateUtil.format(new Date(), "yyyy-MM-dd");
			JSONArray metas = saveData.getJSONArray("meta");
			if (ObjectUtil.isNull(metas)) {
				metas = new JSONArray();
			}
			StringBuilder insertAllSql = new StringBuilder("insert into LAUNCH_PHOTO(LAUNCH_ID, PHOTO_NAME, PHOTO_PATH," +
					"PHOTO_FORMAT, PHOTO_NUMBER, PHOTO_SIZE,CREATOR,CREATE_TIME)values");

			for (int i = 0; i < datas.size(); i++) {
				JSONObject data = datas.getJSONObject(i);
				String nowTime = DateUtil.now();
				String insertSql = " (" + id + ",'" + data.getStr("photoName", "") + "','" + data.getStr("photoPath", "") + "'," +
						"'" + data.getStr("photoFormat", "") + "'," + data.getInt("photoNumber", 1) + ",'" + data.getStr("photoSize", "") + "'," +
						"'" + data.getStr("creator", "adm") + "','" + nowTime + "')";
				if (i < datas.size() - 1) {
					insertSql += ",";
				}
				insertAllSql.append(insertSql);
				// 标记是否找到对应的meta对象
				boolean foundMeta = false;

				// 遍历meta数组查找对应的单元格
				for (int m = 0; m < metas.size(); m++) {
					JSONObject meta = metas.getJSONObject(m);
					if (ObjectUtil.isNotNull(meta)) {
						if (Objects.equals(meta.getInt("row"), data.getInt("row")) && Objects.equals(meta.getInt("col"), data.getInt("col"))) {
							foundMeta = true;
							JSONArray eles = meta.getJSONArray("eles");
							if (ObjectUtil.isNull(eles)) {
								eles = new JSONArray();
							}
							JSONObject photo = new JSONObject();
							photo.set("id", data.getStr("index"))
									.set("type", "photo")
									.set("src", "/File" + data.getStr("photoPath"))
									.set("date", nowDate)
									.set("photoPath", data.getStr("photoPath"))
									.set("photoName", data.getStr("photoName", ""))
									.set("photoShowNum", data.getStr("photoShowNum", ""))
									.set("photoFormat", data.getStr("photoFormat", ""))
									.set("class", "sign-img photo");
							eles.add(photo);
							meta.set("eles", eles);
							break; // 找到对应的meta后跳出循环
						}
					}
				}

				// 如果没有找到对应的meta对象，则创建一个新的
				if (!foundMeta) {
					JSONObject newMeta = new JSONObject();
					newMeta.set("row", data.getInt("row"));
					newMeta.set("col", data.getInt("col"));
					newMeta.set("className", "htMiddle htCenter");

					JSONArray eles = new JSONArray();
					JSONObject photo = new JSONObject();
					photo.set("id", data.getStr("index"))
							.set("type", "photo")
							.set("src", "/File" + data.getStr("photoPath"))
							.set("date", nowDate)
							.set("photoPath", data.getStr("photoPath"))
							.set("photoName", data.getStr("photoName", ""))
							.set("photoShowNum", data.getStr("photoShowNum", ""))
							.set("photoFormat", data.getStr("photoFormat", ""))
							.set("class", "sign-img photo");
					eles.add(photo);
					newMeta.set("eles", eles);

					// 将新创建的meta添加到metas数组中
					metas.add(newMeta);
				}
			}
			Console.log(DateUtil.now(), "1.2:");
			SqliteUtil.executeCommand(insertAllSql.toString());
			Console.log(DateUtil.now(), "1.3:");
			return "成功上传" + datas.size() + "张图片！";
		});
	}

	public void DeletePhoto(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		updateAndSaveJSONData(response, id, saveData -> {
			String src = params.getStr("src");
			int row = params.getInt("row");
			int col = params.getInt("col");
			JSONArray metas = saveData.getJSONArray("meta");

			if (ObjectUtil.isNotNull(metas)) {
				for (int m = 0; m < metas.size(); m++) {
					JSONObject meta = metas.getJSONObject(m);
					if (ObjectUtil.isNotNull(meta)) {
						if (meta.getInt("row") == row && meta.getInt("col") == col) {
							JSONArray eles = meta.getJSONArray("eles");
							JSONArray newEles = new JSONArray();
							if (ObjectUtil.isNotNull(eles)) {
								for (int i = 0; i < eles.size(); i++) {
									JSONObject ele = eles.getJSONObject(i);
									if (!ele.getStr("src").equals(src)) {
										newEles.add(ele);
									}
								}
							}
							meta.set("eles", newEles);
						}
					}
				}
			}

			String photoPath = StrUtil.split(src, "/File").get(1);
			//删除照片表中的数据
			String delSql = "delete from LAUNCH_PHOTO where PHOTO_PATH='" + photoPath + "'";
			SqliteUtil.executeCommand(delSql);
			return "删除成功！";
		});
	}

	public void AddSign(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		updateAndSaveJSONData(response, id, saveData -> {
			JSONArray nodes = SqliteUtil.executeQuery("select * from LAUNCH_CONFIRM where ID=" + id);
			JSONObject object = nodes.getJSONObject(0);
			// 实现AddSign的数据更新逻辑
			int row = params.getInt("row");
			int col = params.getInt("col");
			String creator = params.getStr("creator");
			String date = params.getStr("date");
			String signName = params.getStr("signName");
			String type = params.getStr("type");
			String nowDate = DateUtil.formatDate(new Date());
			if ((creator.equals("adm") || creator.equals("admin")) && type.equals("upload")) {
				nowDate = date;
			}
			String img = params.getStr("img");
			if (!type.equals("photo") && !type.equals("upload")) {
				img = Util.base64ToImg(img);
			}
			JSONArray metas = saveData.getJSONArray("meta");
			if (ObjectUtil.isNotNull(metas)) {
				for (int m = 0; m < metas.size(); m++) {
					JSONObject meta = metas.getJSONObject(m);
					if (ObjectUtil.isNotNull(meta)) {
						if (meta.getInt("row") == row && meta.getInt("col") == col) {
							JSONArray eles = meta.getJSONArray("eles");
							if (ObjectUtil.isNull(eles)) {
								eles = new JSONArray();
							}
							JSONObject photo = new JSONObject();
							if (type.equals("photo")) {
								String tempStr = object.getStr("TABLE_NUM", object.getStr("NAME"));
								int photoNumber = TableUtil.GetNewPhotoNum(id);
								String photoShowNum = tempStr + "-图" + photoNumber;
								photo.set("type", "photo")
										.set("src", "/File" + img)
										.set("date", nowDate)
										.set("photoPath", img)
										.set("photoName", "camera")
										.set("photoShowNum", photoShowNum)
										.set("photoFormat", "png")
										.set("class", "sign-img photo");

								TableUtil.insertOnePhoto(
										JSONUtil.createObj()
												.set("id", id)
												.set("photoName", "camera")
												.set("photoPath", img)
												.set("photoFormat", "png")
												.set("creator", creator)
												.set("photoNumber", photoNumber));
							} else {
								photo.set("type", "sign")
										.set("src", img)
										.set("signName", signName)
										.set("date", nowDate);
								TableUtil.insertOneSign(JSONUtil.createObj()
										.set("id", id).set("creator", creator).set("img", img));
							}
							eles.add(photo);
							meta.set("eles", eles);
						}
					}
				}
			}
			return "请求成功！";
		});
	}


	/**
	 * @param request
	 * @param response
	 */
	public void AddOneSign(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			JSONObject data = params.getJSONObject("data");
			TableUtil.insertOneSign(data);
			return "添加成功！";
		}, response);
	}


	/**
	 * 上传图片
	 *
	 * @param request
	 * @param response
	 */
	public void UploadImg(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			return Util.getWebUploaderFile(request, Util.getFileUploadPath(), file -> {
				return null;
			});
		}, response);
	}

	/**
	 * 上传pdf
	 *
	 * @param request
	 * @param response
	 */
	public void ImportPdf(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String id = request.getParameter("id");
			String saveUser = request.getParameter("saveUser");
			return Util.getUploadFiles(request, Util.getFileUploadPath(), files -> {
				JSONObject file = files.getJSONObject(0);
				String nowTime = DateUtil.now();
				Entity entity = Entity.create("LAUNCH_CONFIRM")
						.set("FILE_NAME", file.getStr("name"))
						.set("FILE_PATH", file.getStr("path"))
						.set("FILE_FORMAT", file.getStr("format"))
						.set("SAVE_USER", saveUser)
						.set("SAVE_TIME", nowTime);
				Entity where = Entity.create("LAUNCH_CONFIRM").set("ID", id);
				SqliteUtil.executeUpdateTransaction(entity, where);
				return null;
			});
		}, response);
	}

	/**
	 * 导出Excel
	 *
	 * @param request
	 * @param response
	 */

	public void ExportExcel(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String id = request.getParameter("id");
			JSONObject node = TableUtil.QueryNodeById(id);
			JSONObject data = node.getJSONObject("data");
			String fileTempPath = Util.getFileTempPath();
			File file = ExcelUtil.tableData2Excel(data, fileTempPath);
			return file;
		}, response);
	}

	/**
	 * 导出PDF
	 *
	 * @param request
	 * @param response
	 */
	public void ExportPdf(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String id = request.getParameter("id");
			JSONObject node = TableUtil.QueryNodeById(id);
			JSONObject data = node.getJSONObject("data");
			File file = null;
			try {
				file = PdfUtil.tableData2Pdf(data, Util.getFileTempPath());
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
			return file;
		}, response);
	}

	/**
	 * 导出所有图片
	 *
	 * @param request
	 * @param response
	 */

	public void ExportImg(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String id = request.getParameter("id");
			JSONObject node = TableUtil.QueryNodeById(id);
			JSONObject data = node.getJSONObject("data");
			String fileTempPath = Util.getFileTempPath();
			return TableUtil.tableData2ImgZip(data, fileTempPath);
		}, response);
	}

	/**
	 * 下载文件
	 *
	 * @param request
	 * @param response
	 */

	public void DownloadFile(HttpServletRequest request, HttpServletResponse response) {
		Result.executeOutStream(() -> {
			String fileName = request.getParameter("fileName");
			String fileFormat = request.getParameter("fileFormat");
			if (StrUtil.isNotBlank(fileFormat)) {
				fileName = fileName + "." + fileFormat;
			}
			String filePath = request.getParameter("filePath");
			String fileUploadPath = Util.getFileUploadPath();
			String fileTempPath = Util.getFileTempPath() + fileName;
			return FileUtil.copy(fileUploadPath + filePath, fileTempPath, true);
		}, response);
	}


	/**
	 * 上传excel
	 *
	 * @param request
	 * @param response
	 */
	public void ImportExcel(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String id = request.getParameter("id");
			String saveUser = request.getParameter("saveUser");
			return TableUtil.importExcel(request, data -> {
				TableUtil.saveTableData(data, id, saveUser);
				return JSONUtil.createObj().set("success", true).set("msg", "导入成功！");
			});
		}, response);
	}

	/**
	 * 导入Excel表格
	 *
	 * @param request
	 * @param response
	 */
	public void ImportTable(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			String saveUser = request.getParameter("saveUser");
			JSONObject params = new JSONObject();
			params.set("pid", request.getParameter("pid"));
			params.set("name", request.getParameter("tableName"));
			params.set("tableNum", request.getParameter("tableNum"));
			params.set("type", request.getParameter("type"));
			params.set("creator", saveUser);
			params.set("level", request.getParameter("level"));
			return TableUtil.importExcel(request, data -> {
				String nextId = TreeUtil.AddTableNode(params);
				TableUtil.saveTableData(data, nextId, saveUser);
				return JSONUtil.createObj()
						.set("success", true)
						.set("msg", "导入成功！")
						.set("data",
								JSONUtil.createObj()
										.set("id", nextId));
			});
		}, response);
	}

	/**
	 * 批量导入表格
	 *
	 * @param request
	 * @param response
	 */
	public void BatchImportTable(HttpServletRequest request, HttpServletResponse response) {
		JSONObject res = Util.getUploadBigFile(request);
		if (res.getBool("success") && res.getBool("isAll", false)) {
			String file = res.getStr("file");
			JSONObject extraData = res.getJSONObject("extraData");
			String saveUser = extraData.getStr("saveUser");
			JSONObject params = new JSONObject();
			params.set("pid", extraData.getStr("pid"));
			params.set("name", extraData.getStr("tableName"));
			params.set("tableNum", extraData.getStr("tableNum"));
			params.set("type", extraData.getStr("type"));
			params.set("creator", saveUser);
			params.set("level", extraData.getStr("level"));
			res = TableUtil.importExcel(file, data -> {
				String nextId = TreeUtil.AddTableNode(params);
				TableUtil.saveTableData(data, nextId, saveUser);
				return JSONUtil.createObj()
						.set("success", true)
						.set("msg", "导入成功！")
						.set("data",
								JSONUtil.createObj()
										.set("id", nextId));
			});
		}
		ServletUtil.write(response, res.toString(), "application/json");
	}

	/**
	 * 锁定表
	 *
	 * @param request
	 * @param response
	 */
	public void LockTable(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		TableUtil.updateTableStatus(response, id, "sign");
	}

	/**
	 * 解锁表
	 *
	 * @param request
	 * @param response
	 */
	public void UnLockTable(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		TableUtil.updateTableStatus(response, id, "edit");
	}


	public void RecordDownloadFile(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject params = Util.getPostRequestParams(request);
			int downloadId = params.getInt("downloadId");
			String sql = "update CONFIRM_DOWNLOAD set is_download=1 where id=" + downloadId;
			SqliteUtil.executeCommand(sql);
			return "记录下载列表中的文件成功";
		}, response);
	}

	public void UpdateLockRow(HttpServletRequest request, HttpServletResponse response) {
		JSONObject params = Util.getPostRequestParams(request);
		String id = params.getStr("id");
		String userName = params.getStr("userName");
		String type = params.getStr("type");
		JSONArray lockTds = params.getJSONArray("lockRow");
		boolean readOnly = "lock".equals(type);
		String typeStr = readOnly ? "锁定行" : "解锁行";
		String nowTime = DateUtil.now();

		updateAndSaveJSONData(response, id, saveData -> {
			JSONArray metas = saveData.getJSONArray("meta");
			for (int j = 0; j < lockTds.size(); j++) {
				JSONObject lockTd = lockTds.getJSONObject(j);
				boolean isExist = false;
				for (int i = 0; i < metas.size(); i++) {
					JSONObject meta = metas.getJSONObject(i);
					if (Objects.equals(meta.getInt("row"), lockTd.getInt("row"))
							&& Objects.equals(meta.getInt("col"), lockTd.getInt("col"))) {
						isExist = true;
						meta.set("readOnly", readOnly);
						if (!readOnly) {
							//解锁行 不清除除签名外的其他元素
							JSONArray eles = meta.getJSONArray("eles");
							if (ObjectUtil.isNull(eles)) {
								eles = new JSONArray();
							}
							JSONArray newEles = new JSONArray();
							for (int k = 0; k < eles.size(); k++) {
								JSONObject ele = eles.getJSONObject(k);
								if (!StrUtil.equals("sign", ele.getStr("type"))) {
									newEles.add(ele);
								}
							}
							meta.set("eles", newEles);
						} else {
							//锁定行 清除批注
							meta.set("comment", "");
						}
						break;
					}
				}
				if (!isExist) {
					JSONObject meta = new JSONObject();
					meta.set("row", lockTd.getInt("row"))
							.set("col", lockTd.getInt("col"))
							.set("readOnly", readOnly)
							.set("eles", new JSONArray());
					metas.add(meta);
				}
			}
			return typeStr + "成功";
		});
	}

	public static void main(String[] args) {
		JSONObject node = TableUtil.QueryNodeById("348");
		JSONObject data = node.getJSONObject("data");
		File file = null;
		try {
			file = PdfUtil.tableData2Pdf(data, Util.getFileTempPath());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		System.out.println("file.getAbsolutePath() = " + file.getAbsolutePath());
	}
}

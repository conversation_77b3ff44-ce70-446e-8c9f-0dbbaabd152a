!function(E,T){if("object"==typeof exports&&"object"==typeof module)module.exports=T(require("handsontable"));else if("function"==typeof define&&define.amd)define(["handsontable"],T);else{var _="object"==typeof exports?T(require("handsontable")):T(E.Handsontable);for(var e in _)("object"==typeof exports?exports:E)[e]=_[e]}}("undefined"!=typeof self?self:this,(function(E){return function(E){var T={};function _(e){if(T[e])return T[e].exports;var N=T[e]={i:e,l:!1,exports:{}};return E[e].call(N.exports,N,N.exports,_),N.l=!0,N.exports}return _.m=E,_.c=T,_.d=function(E,T,e){_.o(E,T)||Object.defineProperty(E,T,{enumerable:!0,get:e})},_.r=function(E){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(E,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(E,"__esModule",{value:!0})},_.t=function(E,T){if(1&T&&(E=_(E)),8&T)return E;if(4&T&&"object"==typeof E&&E&&E.__esModule)return E;var e=Object.create(null);if(_.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:E}),2&T&&"string"!=typeof E)for(var N in E)_.d(e,N,function(T){return E[T]}.bind(null,N));return e},_.n=function(E){var T=E&&E.__esModule?function(){return E.default}:function(){return E};return _.d(T,"a",T),T},_.o=function(E,T){return Object.prototype.hasOwnProperty.call(E,T)},_.p="",_(_.s=17)}({0:function(E,T){E.exports=function(E){return E&&E.__esModule?E:{default:E}},E.exports.__esModule=!0,E.exports.default=E.exports},1:function(E,T){E.exports=function(E,T,_){return T in E?Object.defineProperty(E,T,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[T]=_,E},E.exports.__esModule=!0,E.exports.default=E.exports},17:function(E,T,_){"use strict";var e=_(0);T.__esModule=!0,T.default=void 0;var N,t=e(_(1)),O=e(_(2)),I=O.default.languages.dictionaryKeys,u=(N={languageCode:"zh-CN"},(0,t.default)(N,I.CONTEXTMENU_ITEMS_ROW_ABOVE,"上方插入行"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ROW_BELOW,"下方插入行"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_INSERT_LEFT,"左方插入列"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_INSERT_RIGHT,"右方插入列"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_REMOVE_ROW,["移除该行","移除多行"]),(0,t.default)(N,I.CONTEXTMENU_ITEMS_REMOVE_COLUMN,["移除该列","移除多列"]),(0,t.default)(N,I.CONTEXTMENU_ITEMS_UNDO,"撤销"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_REDO,"恢复"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_READ_ONLY,"只读"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_CLEAR_COLUMN,"清空该列"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT,"对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_LEFT,"左对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_CENTER,"水平居中"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT,"右对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY,"两端对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_TOP,"顶端对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE,"垂直居中"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM,"底端对齐"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_FREEZE_COLUMN,"冻结该列"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_UNFREEZE_COLUMN,"取消冻结"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_BORDERS,"边框"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_BORDERS_TOP,"上"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_BORDERS_RIGHT,"右"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_BORDERS_BOTTOM,"下"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_BORDERS_LEFT,"左"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_REMOVE_BORDERS,"移除边框"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_ADD_COMMENT,"插入批注"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_EDIT_COMMENT,"编辑批注"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_REMOVE_COMMENT,"删除批注"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_READ_ONLY_COMMENT,"只读批注"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_MERGE_CELLS,"合并"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_UNMERGE_CELLS,"取消合并"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_COPY,"复制"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_CUT,"剪切"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD,"插入子行"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD,"与母行分离"),(0,t.default)(N,I.CONTEXTMENU_ITEMS_HIDE_COLUMN,["隐藏该列","隐藏多列"]),(0,t.default)(N,I.CONTEXTMENU_ITEMS_SHOW_COLUMN,["显示该列","显示多列"]),(0,t.default)(N,I.CONTEXTMENU_ITEMS_HIDE_ROW,["隐藏该行","隐藏多行"]),(0,t.default)(N,I.CONTEXTMENU_ITEMS_SHOW_ROW,["显示该行","显示多行"]),(0,t.default)(N,I.FILTERS_CONDITIONS_NONE,"无"),(0,t.default)(N,I.FILTERS_CONDITIONS_EMPTY,"为空"),(0,t.default)(N,I.FILTERS_CONDITIONS_NOT_EMPTY,"不为空"),(0,t.default)(N,I.FILTERS_CONDITIONS_EQUAL,"等于"),(0,t.default)(N,I.FILTERS_CONDITIONS_NOT_EQUAL,"不等于"),(0,t.default)(N,I.FILTERS_CONDITIONS_BEGINS_WITH,"开头是"),(0,t.default)(N,I.FILTERS_CONDITIONS_ENDS_WITH,"结尾是"),(0,t.default)(N,I.FILTERS_CONDITIONS_CONTAINS,"包含"),(0,t.default)(N,I.FILTERS_CONDITIONS_NOT_CONTAIN,"不包含"),(0,t.default)(N,I.FILTERS_CONDITIONS_GREATER_THAN,"大于"),(0,t.default)(N,I.FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL,"大于或等于"),(0,t.default)(N,I.FILTERS_CONDITIONS_LESS_THAN,"小于"),(0,t.default)(N,I.FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL,"小于或等于"),(0,t.default)(N,I.FILTERS_CONDITIONS_BETWEEN,"在此范围"),(0,t.default)(N,I.FILTERS_CONDITIONS_NOT_BETWEEN,"不在此范围"),(0,t.default)(N,I.FILTERS_CONDITIONS_AFTER,"之后"),(0,t.default)(N,I.FILTERS_CONDITIONS_BEFORE,"之前"),(0,t.default)(N,I.FILTERS_CONDITIONS_TODAY,"今天"),(0,t.default)(N,I.FILTERS_CONDITIONS_TOMORROW,"明天"),(0,t.default)(N,I.FILTERS_CONDITIONS_YESTERDAY,"昨天"),(0,t.default)(N,I.FILTERS_VALUES_BLANK_CELLS,"空白单元格"),(0,t.default)(N,I.FILTERS_DIVS_FILTER_BY_CONDITION,"按条件过滤"),(0,t.default)(N,I.FILTERS_DIVS_FILTER_BY_VALUE,"按值过滤"),(0,t.default)(N,I.FILTERS_LABELS_CONJUNCTION,"且"),(0,t.default)(N,I.FILTERS_LABELS_DISJUNCTION,"或"),(0,t.default)(N,I.FILTERS_BUTTONS_SELECT_ALL,"全选"),(0,t.default)(N,I.FILTERS_BUTTONS_CLEAR,"清除"),(0,t.default)(N,I.FILTERS_BUTTONS_OK,"确认"),(0,t.default)(N,I.FILTERS_BUTTONS_CANCEL,"取消"),(0,t.default)(N,I.FILTERS_BUTTONS_PLACEHOLDER_SEARCH,"搜索"),(0,t.default)(N,I.FILTERS_BUTTONS_PLACEHOLDER_VALUE,"值"),(0,t.default)(N,I.FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE,"第二值"),N);O.default.languages.registerLanguageDictionary(u);var S=u;T.default=S},2:function(T,_){T.exports=E}}).___}));
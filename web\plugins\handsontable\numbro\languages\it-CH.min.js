!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).itCH=e()}}((function(){return function e(n,o,i){function r(f,u){if(!o[f]){if(!n[f]){var l="function"==typeof require&&require;if(!u&&l)return l(f,!0);if(t)return t(f,!0);var d=new Error("Cannot find module '"+f+"'");throw d.code="MODULE_NOT_FOUND",d}var a=o[f]={exports:{}};n[f][0].call(a.exports,(function(e){return r(n[f][1][e]||e)}),a,a.exports,e,n,o,i)}return o[f].exports}for(var t="function"==typeof require&&require,f=0;f<i.length;f++)r(i[f]);return r}({1:[function(e,n,o){"use strict";n.exports={languageTag:"it-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"°"},currency:{symbol:"CHF",code:"CHF"}}},{}]},{},[1])(1)}));
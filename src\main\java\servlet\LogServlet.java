package servlet;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import common.BaseServlet;
import common.Result;
import util.SqliteUtil;
import util.TableUtil;
import util.TreeUtil;
import util.Util;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet("/log")
public class LogServlet extends BaseServlet {
	/**
	 * 添加日志
	 *
	 * @param request
	 * @param response
	 */
	public void AddLog(HttpServletRequest request, HttpServletResponse response) {
		Result.execute(() -> {
			JSONObject log = JSONUtil.parseObj(Util.getPostRequestParam(request, "json"));
			String tableName = log.getStr("tableName");
			String tableId = log.getStr("tableId");
			String modelId = TreeUtil.getModelId(tableId);
			String nowTime = DateUtil.now();
			String insertOneLogSql = TableUtil.getInsertOneLogSql();
			insertOneLogSql = StrUtil.format(insertOneLogSql, log.getStr("username"), nowTime,
					log.getStr("moduleType"), tableName, log.getStr("tableId"),
					log.getStr("tablePid"), modelId, log.getStr("operation"),
					log.getStr("content"), log.getStr("reqResult"), log.getStr("userIp"));
			SqliteUtil.executeCommand(insertOneLogSql);
			return "添加日志成功！";
		}, response);
	}
}


.layui-fluid {
    padding: 0;
}

.col-tree {
    overflow-x: auto;
    border-right: 1px solid #c2c2c2;
}

.layui-card-header {
    font-size: 15px;
    font-weight: 600;
    border-bottom: 1px solid #c2c2c2;
    height: 35px;
    line-height: 35px;
}

.layui-card-body {
    overflow: auto;
}

.my-msg {
    float: left;
    color: red;
    padding-left: 15px;
    padding-top: 15px;
}

.my-tbr-div {
    display: none;
}

.my-tbr {
    margin-left: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.my-table {
    display: none;
    float: left;
    width: calc(100% - 30px);
    overflow: auto;
    padding: 0px 15px;
}

.photo-table .layui-table-cell {
    height: auto;
    max-height: none !important;
}

.header-btn {
    height: 18px;
    line-height: 18px;
    position: absolute;
    top: 50%;
    margin-top: -9px;
    color: #808080;
    display: inline-block;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
}

.header-btn:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    background-color: #e2e2e2;
    border-radius: 3px 3px 3px 3px;
}

.fixed-div {
    position: fixed;
    top: 300px;
    z-index: 99999999
}

.fixed-left {
    left: 16px;
}

.fixed-right {
    right: 16px;
}

.fixed-btn {
    width: 250px;
    height: 250px;
    line-height: 250px;
    margin: auto;
    text-align: center;
    background-color: #47a9f2;
    cursor: pointer;
    color: #fff;
    border-radius: 50%;
    user-select: none;
    font-size: 70px;
}
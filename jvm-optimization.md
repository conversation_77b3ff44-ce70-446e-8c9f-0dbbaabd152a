# TableConfirm PDF导出内存优化配置

## JVM参数优化建议

### 1. 基础内存配置
```bash
# 最小堆内存设置为1GB
-Xms1024m

# 最大堆内存设置为2GB（根据服务器内存调整）
-Xmx2048m

# 新生代内存设置为堆内存的1/3
-Xmn512m

# 设置永久代大小（Java 8以下）
-XX:PermSize=256m
-XX:MaxPermSize=512m

# 设置元空间大小（Java 8及以上）
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m
```

### 2. 垃圾回收优化
```bash
# 使用G1垃圾回收器（推荐用于大内存应用）
-XX:+UseG1GC

# 设置G1回收器的目标暂停时间
-XX:MaxGCPauseMillis=200

# 或者使用并行垃圾回收器（适用于吞吐量优先场景）
# -XX:+UseParallelGC
# -XX:ParallelGCThreads=4

# 启用GC日志记录
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:logs/gc.log
```

### 3. 内存溢出处理
```bash
# 内存溢出时生成堆转储文件
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=logs/heapdump.hprof

# 内存溢出时退出JVM
-XX:+ExitOnOutOfMemoryError
```

### 4. 完整启动命令示例
```bash
java -Xms1024m -Xmx2048m -Xmn512m \
     -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
     -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/heapdump.hprof \
     -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:logs/gc.log \
     -jar your-application.jar
```

## Tomcat配置优化

### 1. 修改catalina.sh（Linux）或catalina.bat（Windows）
```bash
# Linux (catalina.sh)
export JAVA_OPTS="$JAVA_OPTS -Xms1024m -Xmx2048m -Xmn512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# Windows (catalina.bat)
set JAVA_OPTS=%JAVA_OPTS% -Xms1024m -Xmx2048m -Xmn512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

### 2. 或者在setenv.sh/setenv.bat中配置
```bash
# setenv.sh
CATALINA_OPTS="-Xms1024m -Xmx2048m -Xmn512m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/heapdump.hprof"
```

## 应用级别优化建议

### 1. 数据库连接池配置
```properties
# 减少数据库连接池大小，避免过多连接占用内存
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
```

### 2. 文件上传限制
```properties
# 限制文件上传大小，避免大文件导致内存溢出
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB
```

### 3. 定期清理临时文件
建议在应用中添加定时任务，定期清理临时文件目录。

## 监控和诊断

### 1. 内存监控工具
- JVisualVM：Java自带的可视化监控工具
- JProfiler：商业性能分析工具
- Eclipse MAT：内存分析工具

### 2. 关键监控指标
- 堆内存使用率
- GC频率和耗时
- 线程数量
- 数据库连接数

### 3. 问题诊断步骤
1. 查看GC日志，分析垃圾回收情况
2. 生成堆转储文件，分析内存占用
3. 检查应用日志中的内存使用情况
4. 使用性能分析工具定位内存泄漏

## 代码级别优化（已实现）

系统已经实现了以下内存优化措施：

### 1. 分批查询和处理
- `buildTreeForPdf()`: PDF导出专用的内存优化树构建
- `buildTreeLightWeight()`: 轻量级树构建，不加载大字段

### 2. 内存监控和检查
- `logMemoryUsage()`: 实时内存使用监控
- `checkMemoryAvailable()`: 操作前内存可用性检查

### 3. 及时资源释放
- 在关键操作后主动清理对象引用
- 适时调用System.gc()建议垃圾回收

### 4. 异常处理
- 捕获OutOfMemoryError并提供友好错误信息
- 防止内存溢出导致整个应用崩溃

## 使用建议

1. **首次部署**：建议使用较大的内存配置（如2GB堆内存）
2. **生产环境**：根据实际使用情况调整内存参数
3. **监控观察**：部署后持续监控内存使用情况
4. **逐步优化**：根据监控数据逐步调整参数

## 故障排除

如果仍然出现内存溢出：

1. 检查是否有大量数据的型号导出
2. 考虑分批导出大型号数据
3. 增加服务器内存或调整JVM参数
4. 联系开发人员进一步优化代码

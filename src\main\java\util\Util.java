package util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.multipart.MultipartFormData;
import cn.hutool.core.net.multipart.UploadFile;
import cn.hutool.core.net.multipart.UploadSetting;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FileUtils;

public class Util {

	public static void main(String[] args) throws Exception {
		generateRandomImages();
	}

	public static JSONObject getPostRequestParams(HttpServletRequest req) {
		return JSONUtil.parseObj(ServletUtil.getBody(req));
	}

	public static String getPostRequestParam(HttpServletRequest req, String key) {
		JSONObject postRequestParams = getPostRequestParams(req);
		return postRequestParams.getStr(key, "");
	}

	public static String getFileUploadPath() {
		Setting setting = new Setting("config.setting");
		String fileStorePath = setting.getStr("fileStorePath");
		FileUtil.mkdir(fileStorePath);
		return fileStorePath;
	}


	/**
	 * 获取固定文件上传临时路径
	 */
	public static String getFileUploadTempPath() {
		String fileTempPath = getTempPath() + "//uploads//";
		FileUtil.mkdir(fileTempPath);
		return fileTempPath;
	}

	public static String getFileTempPath() {
		String fileTempPath = getTempPath() + System.currentTimeMillis() + "//";
		FileUtil.mkdir(fileTempPath);
		return fileTempPath;
	}

	public static String getTempPath() {
		Setting setting = new Setting("config.setting");
		String fileTempPath = setting.getStr("fileTempPath") + "//";
		FileUtil.mkdir(fileTempPath);
		return fileTempPath;
	}

	public static String getPdfFontPath() {
		String fontPath = new ClassPathResource("pdfFont").getAbsolutePath();
		return fontPath;
	}

	/**
	 * base64转图片
	 *
	 * @param base64
	 * @return
	 */
	public static String base64ToImg(String base64) {
		String path = Util.getFileUploadPath();
		String month = DateUtil.format(new Date(), "yyyy-MM");
		String paths = path + "//" + month;
		FileUtil.mkdir(paths);
		String fileName = UUID.randomUUID().toString();
		String targetPath = paths + "//" + fileName;
		FileOutputStream fops;
		base64 = base64.substring(base64.indexOf(",", 1) + 1);
		byte[] buff = DatatypeConverter.parseBase64Binary(base64);
		try {
			fops = new FileOutputStream(targetPath);
			fops.write(buff);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "//" + month + "//" + fileName;
	}

	/**
	 * 处理二维数组中的空值
	 *
	 * @param data
	 */
	public static void removeEmptyValuesFromArray(JSONArray data) {
		for (int i = 0; i < data.size(); i++) {
			JSONArray row = data.getJSONArray(i);
			for (int j = 0; j < row.size(); j++) {
				if (StrUtil.isBlank(row.getStr(j, ""))) {
					row.set(j, "");
				}
			}
		}
	}


	/**
	 * 加密文件
	 */
	public static void encryptFile(File inputFile, File encryptedFile) throws Exception {
		SecretKeySpec secretKey = new SecretKeySpec("cirpointpkg12345".getBytes(), "AES");
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.ENCRYPT_MODE, secretKey);

		FileInputStream inputStream = new FileInputStream(inputFile);
		FileOutputStream outputStream = new FileOutputStream(encryptedFile);
		byte[] buffer = new byte[8192];

		int readBytes;
		while ((readBytes = inputStream.read(buffer)) != -1) {
			byte[] encryptedChunk = cipher.update(buffer, 0, readBytes);
			if (encryptedChunk != null) {
				outputStream.write(encryptedChunk);
			}
		}

		// 处理最终块
		byte[] finalEncryptedBytes = cipher.doFinal();
		outputStream.write(finalEncryptedBytes);

		inputStream.close();
		outputStream.close();
	}

	/**
	 * 解密文件
	 */
	public static void decryptFile(File encryptedFile, File decryptedFile) throws Exception {
		SecretKeySpec secretKey = new SecretKeySpec("cirpointpkg12345".getBytes(), "AES");
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.DECRYPT_MODE, secretKey);

		// 使用try-with-resources确保流正确关闭
		try (FileInputStream inputStream = new FileInputStream(encryptedFile);
			 FileOutputStream outputStream = new FileOutputStream(decryptedFile)) {

			byte[] buffer = new byte[8192];
			int readBytes;
			while ((readBytes = inputStream.read(buffer)) != -1) {
				byte[] decryptedChunk = cipher.update(buffer, 0, readBytes);
				if (decryptedChunk != null) {
					outputStream.write(decryptedChunk);
				}
			}

			// 处理最终块
			byte[] finalDecryptedBytes = cipher.doFinal();
			if (finalDecryptedBytes != null && finalDecryptedBytes.length > 0) {
				outputStream.write(finalDecryptedBytes);
			}

			// 确保数据写入磁盘
			outputStream.flush();
		}
	}

	/**
	 * 备份数据库
	 */
	public static void backupDb() {
		Setting dbSetting = new Setting("db.setting");
		String dbPath = dbSetting.getStr("url").replace("jdbc:sqlite:", "");
		Setting configSetting = new Setting("config.setting");
		String backupPath = configSetting.getStr("fileBackupPath");

		String month = DateUtil.format(new Date(), "yyyy-MM");
		String day = DateUtil.format(new Date(), "yyyy-MM-dd");
		String backFilePath = backupPath + "//db//" + month + "//" + day + ".db";
		FileUtil.copy(dbPath, backFilePath, true);
	}

	/**
	 * 备份文件
	 */
	public static void backupStoreFile() {
		Setting setting = new Setting("config.setting");
		String fileStorePath = setting.getStr("fileStorePath");
		String fileBackupPath = setting.getStr("fileBackupPath");
		FileUtil.mkdir(fileStorePath);
		FileUtil.mkdir(fileBackupPath);
		FileUtil.copy(fileStorePath, fileBackupPath, false);
	}

	/**
	 * 删除临时处理生成的文件 为了避免误删正在生成的文件 所以只删除超过1小时的
	 */
	public static void deleteTempFile() {
		Setting setting = new Setting("config.setting");
		String fileTempPath = setting.getStr("fileTempPath");
		FileUtil.mkdir(fileTempPath);

		// 清理常规临时文件（超过1小时）
		long currentTime = System.currentTimeMillis();
		long expireTime = 60 * 60 * 1000; // 1小时过期时间

		List<File> files = FileUtil.loopFiles(fileTempPath);
		int cleanedCount = 0;
		for (File file : files) {
			long fileTime = file.lastModified();
			if ((currentTime - fileTime) > expireTime) {
				try {
					FileUtil.del(file);
					cleanedCount++;
				} catch (Exception e) {
					// 忽略单个文件删除失败
				}
			}
		}
		FileUtil.cleanEmpty(new File(fileTempPath));

		// 清理hutool临时文件（超过5分钟）
		String hutoolTempPath = fileTempPath + "//hutool//";
		cleanExpiredHutoolTempFiles(hutoolTempPath);

		if (cleanedCount > 0) {
			System.out.println(DateUtil.now() + " - 定时清理临时文件：" + cleanedCount + "个");
		}
	}

	public static JSONArray getWorkType() {
		Setting setting = new Setting("config.setting");
		String workType = setting.getStr("workType");
		List<String> strings = StrUtil.split(workType, "，");
		JSONArray objects = JSONUtil.parseArray(strings);
		return objects;
	}

	public static JSONArray getPostType() {
		Setting setting = new Setting("config.setting");
		String postType = setting.getStr("postType");
		List<String> strings = StrUtil.split(postType, "，");
		JSONArray objects = JSONUtil.parseArray(strings);
		return objects;
	}

	/**
	 * 获取上传文件
	 */
	public static JSONObject getUploadFiles(HttpServletRequest request, String fileUploadPath, Function<JSONArray, JSONObject> successFunction) {
		JSONObject res = new JSONObject();

		// 预防性清理：清理hutool临时目录中的过期文件
		String baseTempPath = Util.getTempPath();
		String hutoolTempPath = baseTempPath + "hutool" + "//";
		cleanExpiredHutoolTempFiles(hutoolTempPath);

		DiskFileItemFactory factory = new DiskFileItemFactory();
		String fileTempPath = Util.getFileTempPath();
		String month = DateUtil.format(new Date(), "yyyy-MM");
		String paths = fileUploadPath + "//" + month;
		FileUtil.mkdir(paths);
		factory.setSizeThreshold(1024 * 1024 * 500);
		factory.setRepository(FileUtil.file(fileTempPath));
		ServletFileUpload servletFileUpload = new ServletFileUpload(factory);
		try {
			List<FileItem> list = servletFileUpload.parseRequest(request);
			JSONArray jsonArray = new JSONArray();
			for (FileItem item : list) {
				InputStream inputStream = item.getInputStream();
				String itemName = item.getName();
				String fileFormat = FileNameUtil.getSuffix(itemName);
				String fileName = UUID.randomUUID().toString();
				String filePath = paths + "//" + fileName;
				File file = FileUtil.file(filePath);
				FileUtil.writeFromStream(inputStream, file);
				String photoSize = FileUtil.readableFileSize(file);
				JSONObject jsonObject = new JSONObject();
				jsonObject.set("size", photoSize);
				jsonObject.set("format", fileFormat);
				jsonObject.set("name", itemName);
				jsonObject.set("path", "//" + month + "//" + fileName);
				jsonObject.set("fullPath", filePath);
				jsonArray.add(jsonObject);
			}
			res.set("success", true);
			res.set("msg", "上传成功！");
			res.set("data", jsonArray);
			JSONObject apply = successFunction.apply(jsonArray);
			if (ObjectUtil.isNotNull(apply)) {
				res = apply;
			}
		} catch (Exception e) {
			res.set("success", false);
			res.set("msg", "上传失败，原因：" + e.getLocalizedMessage());
			throw new RuntimeException(e);
		} finally {
			// 触发垃圾回收，释放文件句柄
			System.gc();
		}
		return res;
	}

	/**
	 * 获取WebUploader上传的文件
	 */
	public static JSONObject getWebUploaderFile(HttpServletRequest request, String fileUploadPath, Function<JSONObject, JSONObject> successFunction) {
		JSONObject res = new JSONObject();
		JSONObject uploadBigFileRes = getUploadBigFile(request);
		if (uploadBigFileRes.getBool("success")) {
			if (uploadBigFileRes.getBool("isAll")) {
				String tempFilePath = uploadBigFileRes.getStr("file");
				String month = DateUtil.format(new Date(), "yyyy-MM");
				String paths = fileUploadPath + "//" + month;
				String fileName = UUID.randomUUID().toString();
				String filePath = paths + "//" + fileName;
				FileUtil.copy(tempFilePath, filePath, true);
				File file = FileUtil.file(filePath);
				String fileFormat = FileNameUtil.getSuffix(tempFilePath);
				String fileSize = FileUtil.readableFileSize(file);
				JSONObject jsonObject = new JSONObject();
				jsonObject.set("size", fileSize);
				jsonObject.set("format", fileFormat);
				jsonObject.set("name", FileNameUtil.getName(file));
				jsonObject.set("path", "//" + month + "//" + fileName);
				jsonObject.set("fullPath", filePath);
				res.set("success", true);
				res.set("msg", "上传成功！");
				res.set("data", jsonObject);
				JSONObject apply = successFunction.apply(jsonObject);
				if (ObjectUtil.isNotNull(apply)) {
					res = apply;
				}
			} else {
				res = uploadBigFileRes;
			}
		} else {
			res = uploadBigFileRes;
		}
		return res;
	}

	/**
	 * 大文件上传
	 *
	 * @param request
	 * @return
	 */
	public static JSONObject getUploadBigFile(HttpServletRequest request) {
		JSONObject res = new JSONObject();
		String baseTempPath = Util.getTempPath();
		String hutoolTempPath = baseTempPath + "hutool" + "//";
		FileUtil.mkdir(hutoolTempPath);

		// 第一层防护：预防性清理hutool临时目录中的过期文件（超过5分钟）
		cleanExpiredHutoolTempFiles(hutoolTempPath);

		String currentRequestTempPath = null;
		try {
			// 获取请求标识，为每个请求创建独立的临时目录
			String reqIdent = getRequestParam(request, "reqIdent");
			if (StrUtil.isNotBlank(reqIdent)) {
				// 第三层防护：为每个请求创建独立的hutool临时空间
				currentRequestTempPath = hutoolTempPath + reqIdent + "//";
				FileUtil.mkdir(currentRequestTempPath);
			} else {
				currentRequestTempPath = hutoolTempPath;
			}

			UploadSetting uploadSetting = new UploadSetting();
			uploadSetting.setTmpUploadPath(currentRequestTempPath);
			MultipartFormData multipart = ServletUtil.getMultipart(request, uploadSetting);

			// 分片序号
			int chunk = Convert.toInt(multipart.getParam("chunk"), -1);
			// 总分片数
			int chunks = Convert.toInt(multipart.getParam("chunks"), -1);
			// 文件名
			String name = multipart.getParam("name");

			reqIdent = multipart.getParam("reqIdent");

			String fileUploadPath = getFileUploadTempPath() + reqIdent;
			FileUtil.mkdir(fileUploadPath);

			UploadFile file = multipart.getFile("file");

			if (chunk == -1 && chunks == -1) {
				//没有分块
				String currentFilePath = fileUploadPath + "//" + name;
				file.write(currentFilePath);
				res.set("success", true);
				res.set("msg", "上传成功！");
				res.set("isAll", true);
				res.set("file", currentFilePath);
				res.set("extraData", JSONUtil.parseObj(multipart.getParam("extraData")));
			} else {
				String currentFileName = chunk + "_" + name;
				String currentFilePath = fileUploadPath + "//" + currentFileName;
				file.write(currentFilePath);

				// 是分片上传时，当上传至最后一个分片时，处理文件合并，chunk 的值 0 - chunks - 1
				if (chunk == (chunks - 1)) {
					// 是最后一个分片,准备合并
					File realFile = new File(fileUploadPath, name);
					// 使用try-with-resources确保流正确关闭
					try (BufferedOutputStream os = new BufferedOutputStream(Files.newOutputStream(realFile.toPath()))) {
						for (int i = 0; i < chunks; i++) {
							// 文件名规则是我们自己定义的
							File temp = new File(fileUploadPath, i + "_" + name);
							// 因为分片上传时并发操作，tomcat拿到请求之后会分配给一个线程去处理，我们不能保证哪个分片先到
							// 如果不存在就一直等
							while (!temp.exists()) {
								// 等100ms
								Thread.sleep(100);
							}
							// 使用流式复制避免大文件内存问题
							try (FileInputStream tempInput = new FileInputStream(temp)) {
								byte[] buffer = new byte[8192]; // 8KB缓冲区
								int bytesRead;
								while ((bytesRead = tempInput.read(buffer)) != -1) {
									os.write(buffer, 0, bytesRead);
								}
								os.flush();
							}
							// 删除临时分片文件
							temp.delete();
						}
						// 最终刷新确保所有数据写入
						os.flush();
					}
					res.set("success", true);
					res.set("msg", "上传成功！");
					res.set("isAll", true);
					res.set("file", realFile.getAbsolutePath());
					res.set("extraData", JSONUtil.parseObj(multipart.getParam("extraData")));
				} else {
					res.set("isAll", false);
					res.set("success", true);
					res.set("msg", "分块上传成功！");
				}
			}
		} catch (Exception e) {
			// 第二层防护：异常安全处理，清理当前请求的临时文件
			if (StrUtil.isNotBlank(currentRequestTempPath) && !currentRequestTempPath.equals(hutoolTempPath)) {
				try {
					FileUtil.del(currentRequestTempPath);
					System.out.println(DateUtil.now() + " - 异常情况下清理临时目录：" + currentRequestTempPath);
				} catch (Exception cleanupException) {
					System.out.println(DateUtil.now() + " - 清理临时目录失败：" + cleanupException.getMessage());
				}
			}
			res.set("success", false);
			res.set("msg", "上传失败，原因：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 触发垃圾回收，释放文件句柄
			System.gc();
		}

		return res;
	}

	/**
	 * 清理hutool临时目录中的过期文件
	 * @param hutoolTempPath hutool临时目录路径
	 */
	private static void cleanExpiredHutoolTempFiles(String hutoolTempPath) {
		try {
			File tempDir = new File(hutoolTempPath);
			if (!tempDir.exists()) {
				return;
			}

			long currentTime = System.currentTimeMillis();
			long expireTime = 5 * 60 * 1000; // 5分钟过期时间

			File[] files = tempDir.listFiles();
			if (files != null) {
				int cleanedCount = 0;
				for (File file : files) {
					if (file.isFile() && (currentTime - file.lastModified()) > expireTime) {
						try {
							if (file.delete()) {
								cleanedCount++;
							}
						} catch (Exception e) {
							// 忽略单个文件删除失败
						}
					} else if (file.isDirectory()) {
						// 清理子目录中的过期文件
						try {
							File[] subFiles = file.listFiles();
							if (subFiles != null) {
								boolean isEmpty = true;
								for (File subFile : subFiles) {
									if ((currentTime - subFile.lastModified()) > expireTime) {
										subFile.delete();
									} else {
										isEmpty = false;
									}
								}
								// 如果子目录为空，删除子目录
								if (isEmpty && file.listFiles().length == 0) {
									file.delete();
								}
							}
						} catch (Exception e) {
							// 忽略子目录清理失败
						}
					}
				}
				if (cleanedCount > 0) {
					System.out.println(DateUtil.now() + " - 清理hutool过期临时文件：" + cleanedCount + "个");
				}
			}
		} catch (Exception e) {
			System.out.println(DateUtil.now() + " - 清理hutool临时文件时发生异常：" + e.getMessage());
		}
	}

	/**
	 * 安全获取请求参数
	 * @param request HTTP请求
	 * @param paramName 参数名
	 * @return 参数值，如果不存在返回空字符串
	 */
	private static String getRequestParam(HttpServletRequest request, String paramName) {
		try {
			String value = request.getParameter(paramName);
			return value != null ? value : "";
		} catch (Exception e) {
			return "";
		}
	}

	public static String dealImgNumShow(List<String> photoShowNums) {
		if (photoShowNums == null || photoShowNums.isEmpty()) {
			return "<span style=\"color:red;\"></span>";
		}
		
		// 表a1-12
		String tableNum = "";
		String firstPhotoNum = photoShowNums.get(0);
		int lastIndexOfDash = firstPhotoNum.lastIndexOf('-');
		
		if (lastIndexOfDash != -1) {
			tableNum = firstPhotoNum.substring(0, lastIndexOfDash);
			if (!tableNum.isEmpty()) {
				tableNum += "-";
			}
		}

		List<Integer> arr = new ArrayList<>();

		for (String photoShowNum : photoShowNums) {
			int num = getNum(photoShowNum);
			arr.add(num);
		}

		List<String> textArr = getTextArr(arr, tableNum);

		StringBuilder spanText = new StringBuilder();
		for (String s : textArr) {
			spanText.append(s).append("<br>");
		}
		
		// 移除最后一个<br>
		if (spanText.length() >= "<br>".length()) {
			spanText.replace(spanText.length() - "<br>".length(), spanText.length(), "");
		}
		
		// 移除第一个<br>，确保有足够长度
		if (spanText.length() >= "<br>".length()) {
			if (spanText.indexOf("<br>") == 0) {
				spanText.replace(0, "<br>".length(), "");
			}
		}
		
		return "<span style=\"color:red;\">".concat(spanText.toString()).concat("</span>");
	}

	private static List<String> getTextArr(List<Integer> arr, String tableNum) {
		List<List<Integer>> newArr = new ArrayList<>();

		for (int i = 0; i < arr.size(); i++) {
			List<Integer> tempArr = new ArrayList<>();
			tempArr.add(arr.get(i));

			while (i + 1 < arr.size() && arr.get(i + 1) - arr.get(i) == 1) {
				tempArr.add(arr.get(++i));
			}

			newArr.add(tempArr);
		}

		List<String> textArr = new ArrayList<>();
		textArr.add("");

		for (List<Integer> a : newArr) {
			if (a.size() == 1) {
				textArr.add(tableNum + "图" + a.get(0));
			} else {
				textArr.add(tableNum + "图" + a.get(0) + "~" + "图" + a.get(a.size() - 1));
			}
		}
		return textArr;
	}

	private static int getNum(String photoShowNum) {
		// 表a1-12-图8
		String[] tempArr = photoShowNum.split("-");
		int num = Integer.parseInt(tempArr[tempArr.length - 1].substring(1));
		return num;
	}

	/**
	 * 自定义解压方法，不进行zip炸弹检测
	 *
	 * @param zipFile zip文件路径
	 * @param destDir 目标目录
	 * @throws IOException IO异常
	 */
	public static void customUnzip(String zipFile, String destDir) throws IOException {
		try (java.util.zip.ZipFile zip = new java.util.zip.ZipFile(zipFile, StandardCharsets.UTF_8)) {
			java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zip.entries();
			while (entries.hasMoreElements()) {
				java.util.zip.ZipEntry entry = entries.nextElement();
				File destFile = new File(destDir, entry.getName());

				// 创建目标文件的父目录
				if (entry.isDirectory()) {
					FileUtil.mkdir(destFile);
					continue;
				}

				// 确保目标文件的父目录存在
				File parent = destFile.getParentFile();
				if (parent != null && !parent.exists()) {
					FileUtil.mkdir(parent);
				}

				// 复制文件内容
				try (java.io.InputStream in = zip.getInputStream(entry);
					 java.io.OutputStream out = Files.newOutputStream(destFile.toPath())) {
					byte[] buffer = new byte[8192];
					int len;
					while ((len = in.read(buffer)) != -1) {
						out.write(buffer, 0, len);
					}
				}
			}
		}
	}

	/**
	 * 清除7天前的导出数据包记录和文件
	 */
	public static void clearExpiredDownloadFiles() {
		String sevenDaysAgo = DateUtil.offsetDay(new Date(), -30).toString("yyyy-MM-dd HH:mm:ss");
		String sql = StrUtil.format("SELECT FILE_PATH FROM CONFIRM_DOWNLOAD WHERE START_TIME < '{}'", sevenDaysAgo);
		JSONArray expiredFiles = SqliteUtil.executeQuery(sql);

		if (!expiredFiles.isEmpty()) {
			// 删除文件
			for (int i = 0; i < expiredFiles.size(); i++) {
				String filePath = expiredFiles.getJSONObject(i).getStr("FILE_PATH");
				if (StrUtil.isNotEmpty(filePath)) {
					Setting setting = new Setting("config.setting");
					String fileStorePath = setting.getStr("fileStorePath");
					File file = new File(fileStorePath + File.separator + filePath);
					if (file.exists()) {
						FileUtil.del(file);
					}
				}
			}

			// 删除数据库记录
			String deleteSql = StrUtil.format("DELETE FROM CONFIRM_DOWNLOAD WHERE START_TIME < '{}'", sevenDaysAgo);
			SqliteUtil.executeCommand(deleteSql);
		}

	}

	/**
	 * 清除指定路径下的过期文件和文件夹
	 *
	 * @return 清除的文件和文件夹数量
	 */
	public static int clearExpiredFiles() {
		Setting setting = new Setting("config.setting");
		String fileStorePath = setting.getStr("fileStorePath");

		File dir = new File(fileStorePath + File.separator + "confirmDownload");
		if (!dir.exists() || !dir.isDirectory()) {
			return 0;
		}

		int count = 0;
		Date expiredDate = DateUtil.offsetDay(new Date(), -7);
		File[] files = dir.listFiles();
		if (files != null) {
			for (File file : files) {
				// 获取文件最后修改时间
				Date lastModified = new Date(file.lastModified());
				if (lastModified.before(expiredDate)) {
					if (FileUtil.del(file)) {
						count++;
					}
				}
			}
		}

		return count;
	}

    /**
     * 生成随机图片
     * 从Picsum Photos API获取图片并保存到指定目录
     */
    public static void generateRandomImages() {
        String targetDir = "E:\\DELL\\Pictures";
        int count = 100;
        
        // 确保目录存在
        FileUtil.mkdir(targetDir);
        
        // 主流图片格式
        String[] formats = {"jpg", "png", "jpeg"};
        // 常见分辨率
        int[][] resolutions = {
            {800, 600},   // 常规
            {1024, 768},  // XGA
            {1280, 720},  // HD
            {1920, 1080}, // Full HD
            {2560, 1440}, // 2K
            {3840, 2160}  // 4K
        };
        
        System.out.println("开始生成随机图片...");
        
        for (int i = 0; i < count; i++) {
            try {
                // 随机选择分辨率和格式
                int[] resolution = resolutions[RandomUtil.randomInt(0, resolutions.length)];
                String format = formats[RandomUtil.randomInt(0, formats.length)];
                
                // 生成随机文件名
                String fileName = String.format("random_image_%dx%d_%s.%s", 
                    resolution[0], resolution[1], 
                    UUID.randomUUID().toString(true).substring(0, 8), 
                    format);
                
                // 构建Picsum Photos API URL，添加格式参数
                String imageUrl = String.format("https://picsum.photos/%d/%d.%s", 
                    resolution[0], resolution[1], format);
                
                // 下载图片
                String targetPath = targetDir + "\\" + fileName;
                HttpUtil.downloadFile(imageUrl, FileUtil.file(targetPath));
                
                System.out.printf("已生成第%d张图片: %s%n", i + 1, fileName);
                
                // 添加短暂延迟，避免请求过快
                Thread.sleep(300);
                
            } catch (Exception e) {
                System.err.printf("生成第%d张图片时发生错误: %s%n", i + 1, e.getMessage());
            }
        }
        
        System.out.println("随机图片生成完成！");
    }
}

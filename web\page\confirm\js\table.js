function renderMsg(type) {
    if (type === 'a' || type === 'b') {
        $("#my-msg").hide();
        $("#my-tbr").show();
        $("#my-table").show();
    } else {
        $("#my-msg").show();
        $("#my-tbr").hide();
        $("#my-table").hide();
    }
}

/**
 * 加载表格
 * @param treeNode
 * @param scrollObj
 */
function reloadTable(treeNode) {
    HotUtil.currentTreeNodeId = treeNode['ID'];
    var type = treeNode['TYPE'];
    renderMsg(type);
    var cb_success = function (data) {
        HotUtil.loadHtmlTable(data, treeNode);
    };
    postAjax("table", "QueryNodeById", {
        id: treeNode['ID']
    }, true, cb_success);
}
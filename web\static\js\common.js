/**
 * ajax请求
 * @param servlet
 * @param method
 * @param RequestData
 * @param async
 * @param cb_success
 * @param cb_error
 */
function ajax(servlet, method, RequestData, async, cb_success, cb_error) {
    var opTime = new Date().getTime();
    var projectName = getProjietName();
    var url = "/" + projectName + "/" + servlet + "?act=" + method;
    $.ajax({
        type: "POST",
        url: url,
        async: async,
        dataType: 'json',
        data: JSON.stringify(RequestData),
        contentType: "application/json;charset=utf-8",
        success: function (data) {
            cb_success(data);
        },
        error: function (xhr, textStatus, error) {
            cb_error(error);
        }
    });
}

/**
 * 获取项目名称
 * @returns {string}
 */
function getProjietName() {
    var projectName = location.href.split("/")[3];
    return projectName;
}

/**
 * 异步请求
 * @param servlet
 * @param method
 * @param RequestData
 * @param showMsg
 * @param cb_success
 * @param cb_fail
 */
function postAjax(servlet, method, RequestData, showMsg, cb_success = () => {
}, cb_fail = () => {
}) {
    var layerIndex;
    if (showMsg) {
        layerIndex = layer.msg("请求中......", {
            icon: 16,
            shade: 0.01,
            time: 0
        });
    }

    ajax(servlet, method, RequestData, true, function (res) {
        if (showMsg) {
            layer.close(layerIndex);
        }
        if (res.success) {
            cb_success(res.data);
            if (showMsg) {
                if (res.msg) {
                    layer.msg(res.msg);
                }
            }
        } else {
            cb_fail();
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (msg) {
        if (showMsg) {
            layer.close(layerIndex);
        }
        layer.alert("请求失败，原因:" + msg, {
            icon: 2
        })
    });
}

function postAjaxNoRes(servlet, method, RequestData) {
    ajax(servlet, method, RequestData, true, function (res) {
    }, function (msg) {
        layer.alert("请求失败，原因:" + msg, {
            icon: 2
        })
    });
}

/**
 * 同步请求
 * @param servlet
 * @param method
 * @param RequestData
 * @param cb_success
 */
function postSyncAjax(servlet, method, RequestData, cb_success) {
    ajax(servlet, method, RequestData, false, cb_success);
}

/**
 * 获取请求url
 * @param servlet
 * @param method
 * @param RequestData
 * @returns {string}
 */
function getReqUrl(servlet, method = "", RequestData = "") {
    var projectName = getProjietName();
    var url = "/" + projectName + "/" + servlet;
    if (method) {
        url += "?act=" + method + RequestData;
    }
    return url;
}

/**
 * 增加一条操作日志
 * @param json
 */
function addConfirmLog(json) {
    json.userIp = sessionStorage.getItem("userIp");
    json.username = sessionStorage.getItem("username");
    json.tableName = "LAUNCH_CONFIRM";
    json.moduleType = "发射场确认";

    postAjax("log", "AddLog", {
        json: json
    }, false);
}

/**
 * 以数组中的对象的某一个key值删除元素
 * @param key
 * @param value
 * @param array
 * @returns {*}
 */
function removeArray(key, value, array) {
    for (var i = 0; i < array.length; i++) {
        if (array[i][key] == value) {
            array.splice(i, 1);
            i = i - 1;
        }
    }
    return array;
}

/**
 * 去除数组空元素
 * @param array
 * @returns {*}
 */
function trimSpace(array) {
    for (var i = 0; i < array.length; i++) {
        if (array[i] == " " || array[i] == null || typeof (array[i]) == "undefined" || array[i].length == 0) {
            array.splice(i, 1);
            i = i - 1;
        }
    }
    return array;
}

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
    return null;
}
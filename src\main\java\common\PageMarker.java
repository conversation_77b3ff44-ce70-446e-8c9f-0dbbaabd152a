package common;

import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;

public class PageMarker implements IEventHandler {

	public PageMarker(PdfFont pdfFont) {
		this.pdfFont = pdfFont;
	}

	private PdfFont pdfFont;

	public PdfFont getPdfFont() {
		return pdfFont;
	}

	public void setPdfFont(PdfFont pdfFont) {
		this.pdfFont = pdfFont;
	}

	@Override
	public void handleEvent(Event event) {
		PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
		PdfDocument pdf = docEvent.getDocument();
		PdfPage page = docEvent.getPage();
		Rectangle pageSize = page.getPageSize();
		Canvas canvas = new Canvas(page, new Rectangle(50, 30));
		float x = (pageSize.getLeft() + pageSize.getRight()) / 2;
		float y = pageSize.getBottom() + 2;
		Paragraph p = new Paragraph("第" + pdf.getPageNumber(page) + "页")
				.setFontSize(12)
				.setFont(pdfFont);
		canvas.showTextAligned(p, x, y, TextAlignment.CENTER);
		canvas.close();
	}
}

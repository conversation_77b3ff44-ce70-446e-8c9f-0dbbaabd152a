var uploader = WebUploader.create({
    // 选完文件后，是否自动上传。
    auto: false,
    // 文件接收服务端。
    server: getReqUrl("tree", "ImportBigZip"),
    // 选择文件的按钮。可选。
    pick: {
        id: '#uploadChoice',
        multiple: false // 设置multiple为false
    },
    timeout: 60 * 60 * 1000,//请求超时时间
    // 配置分片上传
    chunked: true,
    threads: 3, //上传并发数。允许同时最大上传进程数。
    chunkSize: 10 * 1024 * 1024,//分段大小为10Mb
    fileNumLimit: 1,
    formData: {
        reqIdent: reqIdent,
        extraData: JSON.stringify({
            username: sessionStorage.getItem("username"),
            tableId: treeNode['ID'],
            tablePId: treeNode['PID'],
            srcType: treeNode['TYPE']
        })
    }
});
uploader.on('uploadBeforeSend', function (object, data, headers) {

    console.log('uploadBeforeSend:' + loadIndex);
});

// 当有文件被添加进队列之前触发
uploader.on('beforeFileQueued', function (file) {
    // 检查队列中是否已经有文件
    if (uploader.getFiles().length > 0) {
        // 如果有文件，先移除旧的文件
        uploader.removeFile(uploader.getFiles()[0], true);
    }
});

// 当有文件被添加进队列的时候
uploader.on('fileQueued', function (file) {
    fileFlag = true;
    $("#selectedFile").show();
    $("#selectedFileName").text(file.name);
    log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] + "）】上导入数据包(" + file.name + ")";
});

uploader.on('uploadSuccess', function (file, res) {
    if (res.success) {
        log.reqResult = 1;
        layer.closeAll();
        reloadTree(treeNode['PID'], res['rootId']);
        layer.msg("导入成功");
    } else {
        log.reqResult = 0;
        layer.alert(res.msg, {icon: 2});
    }
});

// 文件上传失败，显示上传出错。
uploader.on('uploadError', function (file) {
    log.reqResult = 0;
});

// 完成上传完毕，成功或者失败，先删除进度条。
uploader.on('uploadComplete', function (file) {
    addConfirmLog(log);
});

// 当所有文件上传结束时触发
uploader.on('uploadFinished', function () {
    layer.close(loadIndex);
});
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).srCyrlRS=e()}}(function(){return function i(f,u,l){function d(n,e){if(!u[n]){if(!f[n]){var r="function"==typeof require&&require;if(!e&&r)return r(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var t=u[n]={exports:{}};f[n][0].call(t.exports,function(e){return d(f[n][1][e]||e)},t,t.exports,i,f,u,l)}return u[n].exports}for(var s="function"==typeof require&&require,e=0;e<l.length;e++)d(l[e]);return d}({1:[function(e,n,r){"use strict";n.exports={languageTag:"sr-Cyrl-RS",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"RSD",code:"RSD"}}},{}]},{},[1])(1)});
//# sourceMappingURL=sr-Cyrl-RS.min.js.map

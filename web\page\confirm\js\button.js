/**
 * 初始化表头按钮
 * @param treeNode
 */
function initTbr(treeNode) {
    var tableStatus = treeNode['TABLE_STATUS'];
    if (tableStatus === 'sign') {
        $('#export-img').show();
        $("#table-header").show();
        $('#clear-sign').show();
        $('#export-pdf').show();
        $('#export-excel').show();

        $('#edit-table').hide();
        $('#import-excel').hide();
        $('#confirm-table').hide();
        $('#import-pdf').hide();
    } else {
        $('#edit-table').show();
        $('#import-excel').show();
        $('#clear-sign').hide();
        if (treeNode['HTML_DATA'] !== null && treeNode['HTML_DATA'] !== ''
            && treeNode['HTML_DATA'] !== 'null' && treeNode['HTML_DATA'] !== undefined
            && treeNode['HTML_DATA'] !== 'undefined' && treeNode['HTML_DATA'] !== ' ') {
            $('#export-img').show();
            $('#export-pdf').show();
            $('#export-excel').show();
            $("#table-header").show();
            $('#confirm-table').show();
        } else {
            $('#export-img').hide();
            $('#confirm-table').hide();
            $("#table-header").hide();
            $('#export-pdf').hide();
            $('#export-excel').hide();
        }
        if (treeNode['TYPE'] === 'b') {
            $('#import-pdf').show();
            if (HotUtil.isPdf(treeNode)) {
                $('#export-pdf').show();

                $('#import-excel').hide();
                $('#edit-table').hide();
                $('#export-img').hide();
                $('#confirm-table').hide();
                $('#table-header').hide();
                $('#export-excel').hide();
            }
        } else {
            $('#import-pdf').hide();
        }
    }
    registerTbrEvent(treeNode);
}

function registerTbrEvent(treeNode) {
    //编辑表格
    $('#edit-table').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 1, function () {
            HotUtil.editTable(treeNode);
        });
    });

    //设置表格表头行
    $('#table-header').unbind("click").bind('click', function () {
        HotUtil.updateHeaderRow(treeNode);
    });

    //导入Excel
    $('#import-excel').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.importExcel(treeNode);
        });
    });

    //导出excel
    $('#export-excel').unbind("click").bind('click', function () {
        HotUtil.exportExcel(treeNode);
    });

    //导出PDF
    $('#export-pdf').unbind("click").bind('click', function () {
        if (HotUtil.isPdf(treeNode)) {
            var fileName = treeNode['TABLE_NUM'] + '：' + treeNode['NAME'] + "(" + treeNode['SECURITY_NAME'] + ")";
            HotUtil.downloadFile(fileName, treeNode['FILE_PATH'], treeNode['FILE_FORMAT']);
        } else {
            HotUtil.exportPdf(treeNode);
        }
    });

    //下载所有照片
    $('#export-img').unbind("click").bind('click', function () {
        HotUtil.exportImg(treeNode);
    });

    //导入PDF
    $('#import-pdf').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.importPdf(treeNode);
        });
    });

    //确认表格之后可以签署
    $('#confirm-table').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.lockTable(treeNode);
        });
    });

    //清除表格签名
    $('#clear-sign').unbind("click").bind('click', function () {
        HotUtil.clearLock(treeNode);
    });
}
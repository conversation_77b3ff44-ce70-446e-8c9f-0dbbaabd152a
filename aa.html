<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的博客 - 技术分享与思考</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>TechBlog</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">关于</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">技能</a>
                </li>
                <li class="nav-item">
                    <a href="#blog" class="nav-link">博客</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系</a>
                </li>
                <li class="nav-item">
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- 首页横幅 -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">你好，我是 <span class="highlight">张程序员</span></h1>
                <p class="hero-subtitle">全栈开发工程师 | 技术博主 | 开源爱好者</p>
                <p class="hero-description">
                    热爱编程，专注于现代Web开发技术，分享技术心得与生活感悟
                </p>
                <div class="hero-buttons">
                    <a href="#blog" class="btn btn-primary">阅读博客</a>
                    <a href="#contact" class="btn btn-secondary">联系我</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="code-animation">
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我 -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">关于我</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>
                        我是一名充满热情的全栈开发工程师，拥有5年+的开发经验。
                        专注于JavaScript、Python、Go等技术栈，热爱学习新技术并乐于分享。
                    </p>
                    <p>
                        在这个博客中，我会分享技术教程、项目经验、编程心得以及对技术发展的思考。
                        希望能够帮助更多的开发者成长，同时也期待与大家交流学习。
                    </p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <h3>50+</h3>
                            <p>技术文章</p>
                        </div>
                        <div class="stat-item">
                            <h3>20+</h3>
                            <p>开源项目</p>
                        </div>
                        <div class="stat-item">
                            <h3>1000+</h3>
                            <p>代码提交</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技能展示 -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">技术栈</h2>
            <div class="skills-grid">
                <div class="skill-card">
                    <i class="fab fa-js-square skill-icon"></i>
                    <h3>JavaScript</h3>
                    <p>ES6+, TypeScript, Node.js</p>
                </div>
                <div class="skill-card">
                    <i class="fab fa-react skill-icon"></i>
                    <h3>前端框架</h3>
                    <p>React, Vue.js, Angular</p>
                </div>
                <div class="skill-card">
                    <i class="fas fa-server skill-icon"></i>
                    <h3>后端开发</h3>
                    <p>Python, Go, Java, Spring</p>
                </div>
                <div class="skill-card">
                    <i class="fas fa-database skill-icon"></i>
                    <h3>数据库</h3>
                    <p>MySQL, PostgreSQL, MongoDB</p>
                </div>
                <div class="skill-card">
                    <i class="fab fa-docker skill-icon"></i>
                    <h3>DevOps</h3>
                    <p>Docker, Kubernetes, CI/CD</p>
                </div>
                <div class="skill-card">
                    <i class="fas fa-cloud skill-icon"></i>
                    <h3>云服务</h3>
                    <p>AWS, Azure, 阿里云</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 博客文章 -->
    <section id="blog" class="blog">
        <div class="container">
            <h2 class="section-title">最新文章</h2>
            <div class="blog-grid">
                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">JavaScript</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">深入理解JavaScript异步编程</a></h3>
                        <p>探讨Promise、async/await以及事件循环机制，帮助你更好地掌握JavaScript异步编程...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2024-01-15</span>
                            <span><i class="fas fa-eye"></i> 1.2k</span>
                        </div>
                    </div>
                </article>

                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">React</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">React Hooks最佳实践指南</a></h3>
                        <p>从useState到useEffect，再到自定义Hooks，全面解析React Hooks的使用技巧...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2024-01-10</span>
                            <span><i class="fas fa-eye"></i> 856</span>
                        </div>
                    </div>
                </article>

                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">Python</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">Python装饰器从入门到精通</a></h3>
                        <p>深入学习Python装饰器的工作原理，掌握函数装饰器、类装饰器的实现方式...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2024-01-05</span>
                            <span><i class="fas fa-eye"></i> 1.5k</span>
                        </div>
                    </div>
                </article>

                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">DevOps</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">Docker容器化部署最佳实践</a></h3>
                        <p>学习如何使用Docker进行应用容器化，包括Dockerfile编写、镜像优化等技巧...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2023-12-28</span>
                            <span><i class="fas fa-eye"></i> 932</span>
                        </div>
                    </div>
                </article>

                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">架构</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">微服务架构设计原则</a></h3>
                        <p>探讨微服务架构的设计原则，包括服务拆分、通信方式、数据一致性等关键问题...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2023-12-20</span>
                            <span><i class="fas fa-eye"></i> 1.8k</span>
                        </div>
                    </div>
                </article>

                <article class="blog-card">
                    <div class="blog-image">
                        <div class="blog-category">前端</div>
                    </div>
                    <div class="blog-content">
                        <h3><a href="#">现代前端性能优化策略</a></h3>
                        <p>从代码分割到懒加载，从缓存策略到CDN优化，全面提升前端应用性能...</p>
                        <div class="blog-meta">
                            <span><i class="fas fa-calendar"></i> 2023-12-15</span>
                            <span><i class="fas fa-eye"></i> 1.1k</span>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <!-- 联系方式 -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">联系我</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>让我们一起交流技术</h3>
                    <p>如果你对技术有任何疑问，或者想要合作交流，欢迎通过以下方式联系我：</p>
                    <div class="contact-links">
                        <a href="mailto:<EMAIL>" class="contact-link">
                            <i class="fas fa-envelope"></i>
                            <EMAIL>
                        </a>
                        <a href="https://github.com" class="contact-link">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                        <a href="https://twitter.com" class="contact-link">
                            <i class="fab fa-twitter"></i>
                            Twitter
                        </a>
                        <a href="https://linkedin.com" class="contact-link">
                            <i class="fab fa-linkedin"></i>
                            LinkedIn
                        </a>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <div class="form-group">
                            <input type="text" placeholder="您的姓名" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="您的邮箱" required>
                        </div>
                        <div class="form-group">
                            <textarea placeholder="您的留言" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">发送消息</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 程序员的博客. 保留所有权利.</p>
                <div class="footer-links">
                    <a href="#privacy">隐私政策</a>
                    <a href="#terms">使用条款</a>
                    <a href="#rss">RSS订阅</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
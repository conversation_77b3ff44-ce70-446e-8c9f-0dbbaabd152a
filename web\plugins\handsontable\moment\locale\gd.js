//! moment.js locale configuration
//! locale : Scottish Gaelic [gd]
//! author : <PERSON> : https://github.com/jonashdown
!function(a,n){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?n(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],n):n(a.moment)}(this,(function(a){"use strict";
//! moment.js locale configuration
return a.defineLocale("gd",{months:["Am Faoilleach","An Gearran","Am Màrt","An G<PERSON>an","<PERSON> Cè<PERSON>","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["Faoi","Gear","<PERSON>àrt","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Ògmh","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],monthsParseExact:!0,weekdays:["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>dain","<PERSON><PERSON>aoin","<PERSON><PERSON>ine","<PERSON>sathairne"],weekdaysShort:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],weekdaysMin:["Dò","Lu","Mà","<PERSON>i","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(a){return a+(1===a?"d":a%10==2?"na":"mh")},week:{dow:1,doy:4}})}));
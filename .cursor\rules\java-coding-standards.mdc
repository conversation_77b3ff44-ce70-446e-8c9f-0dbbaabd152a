---
globs: *.java
description: Java代码规范和最佳实践
---

# Java 代码规范

## 基础规范
- 使用Java 8语法特性
- 编码格式统一使用UTF-8
- 包命名使用小写字母，类命名使用PascalCase
- 所有代码注释使用中文

## Servlet开发规范

### 1. Servlet类规范
```java
@WebServlet("/example")
public class ExampleServlet extends BaseServlet {
    // 所有Servlet必须继承BaseServlet
    // 使用@WebServlet注解配置URL映射
}
```

### 2. 方法命名规范
- 业务方法使用驼峰命名法，首字母大写 (如: OpenEdit, SaveData)
- 参数固定为: `HttpServletRequest request, HttpServletResponse response`

### 3. 错误处理
```java
Result.execute(() -> {
    // 业务逻辑代码
    // 统一使用Result.execute包装异常处理
});
```

### 4. 数据库操作
```java
// 查询操作
JSONArray result = SqliteUtil.executeQuery("SELECT * FROM table_name");

// 更新操作  
SqliteUtil.executeUpdate("UPDATE table_name SET column = value");
```

## 依赖库使用规范

### Hutool工具库
- JSON操作: 使用 `cn.hutool.json.JSONUtil`
- 字符串操作: 使用 `cn.hutool.core.util.StrUtil`
- 日期操作: 使用 `cn.hutool.core.date.DateUtil`
- 文件操作: 使用 `cn.hutool.core.io.FileUtil`

### 注意事项
- 避免在Servlet中直接处理文件IO，使用工具类
- 数据库连接统一通过SqliteUtil管理，禁止直接操作Connection
- 所有异常必须通过Result.execute处理，不允许直接抛出
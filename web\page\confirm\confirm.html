<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>发射场确认</title>
    <link rel="Shortcut Icon" href="../../static/img/favicon.ico">
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/handsontable/handsontable.full.min.css" rel="stylesheet" media="screen">
    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
    <link rel="stylesheet" type="text/css" href="../../plugins/webuploader/webuploader.css">
    <link rel="stylesheet" href="../../plugins/preview/preview.css">

    <link rel="stylesheet" type="text/css" href="../../static/css/HotStyle.css">
    <link rel="stylesheet" type="text/css" href="confirm.css">

    <script src="../../plugins/common/jquery-3.7.1.min.js"></script>
    <script src="../../plugins/preview/preview.js"></script>
    <script src="../../plugins/preview/jquery.rotate.min.js"></script>
    <script src="../../plugins/common/jquery.fileDownload.js"></script>
    <script type="text/javascript" src="../../plugins/webuploader/webuploader.min.js"></script>

    <script src="../../plugins/layui/layui.js"></script>
    <script src="../../plugins/handsontable/handsontable.full.min.js"></script>
    <script src="../../plugins/handsontable/languages/zh-CN.js"></script>
    <script src="../../plugins/common/jq-signature.js"></script>
    <script src="../../plugins/common/jquery.contextMenu.min.js"></script>
    <script src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>

</head>
<body>
<div class="layui-fluid">
    <div class="layui-row">
        <div class="layui-col-md3 col-tree">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span class="contract">结构树</span>
                    <span class="header-btn contract" style="right:35px" title="下载列表" id="download-list"><i class="layui-icon layui-icon-download-circle" style="font-size: 18px;"></i></span>
                    <span class="header-btn" style="right:10px" id="toggle-tree" status="show"><i class="layui-icon layui-icon-prev" style="font-size: 14px;"></i></span>
                </div>
                <div class="layui-card-body">
                    <ul id="dpTree" class="ztree contract"></ul>
                </div>
            </div>
        </div>
        <div class="layui-col-md9 col-table">
            <div id="my-msg" class="layui-row my-msg">
                请选择A表或者B表！
            </div>
            <div id="my-tbr" class="layui-row my-tbr-div">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal my-tbr" id="edit-table">
                    <i class="layui-icon">&#xe642;</i> 编辑表格
                </button>
                <button type="button" class="layui-btn layui-btn-sm my-tbr" id="table-header">
                    <i class="layui-icon">&#xe62d;</i> 设置表头行
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4 my-tbr" id="export-pdf">
                    <i class="layui-icon">&#xe66d;</i> 导出PDF
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor8 my-tbr" id="import-pdf">
                    <i class="layui-icon">&#xe62f;</i> 导入PDF
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3 my-tbr" id="export-excel">
                    <i class="layui-icon">&#xe66d;</i> 导出Excel
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor6 my-tbr" id="export-img">
                    <i class="layui-icon">&#xe66d;</i> 下载所有照片
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor5 my-tbr" id="import-excel">
                    <i class="layui-icon">&#xe9aa;</i> 导入Excel
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-warm my-tbr" func="func-launch-lock-table" id="confirm-table">
                    <i class="layui-icon">&#xe679;</i> 锁定
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger my-tbr" func="func-launch-clear-sign" id="clear-sign">
                    <i class="layui-icon">&#x1007;</i> 清除签名
                </button>
                <div id="table-security" class="table-security">
                </div>
            </div>
            <div id="my-table" class="my-table">
            </div>
        </div>
    </div>
</div>
<div class="fixed-div fixed-left layui-hide">
    <div class="layui-btn fixed-btn">
        拍照
    </div>
</div>
<div class="fixed-div fixed-right layui-hide">
    <div class="layui-btn fixed-btn">
        拍照
    </div>
</div>
</body>

<script src="../../static/js/common.js"></script>
<script src="js/constant.js"></script>
<script src="js/HotUtil.js"></script>
<script src="js/treeMenu.js"></script>
<script src="js/button.js"></script>
<script src="js/tree.js"></script>
<script src="js/table.js"></script>
<script src="confirm.js"></script>

<script type="text/html" id="uploadHtml">
    <form class="layui-form" lay-filter="uploadForm">
        <div class="layui-form-item">
            <label class="fieldlabel layui-form-label">文件内容:</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
                    <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="selectedFile" style="display: none;">
            <label class="fieldlabel layui-form-label">已选文件:</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <center>
                <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
                <button id="btn_cancel" class="layui-btn">取消</button>
            </center>
        </div>
    </form>
</script>

<script type="text/html" id="many-upload">
    <div style="padding:12px;">
        <div class="layui-form" style="margin-bottom: 10px;">
            <div class="layui-form-item" style="margin-bottom:0px;">
                <div class="layui-inline" style="margin-bottom:0px;">
                    <div class="layui-upload" style="margin-bottom:2px;">
                        <div id="chooseFile">选择文件</div>
                        <button type="button" class="layui-btn" id="manyUploadStart" style="display: none;">开始上传</button>
                    </div>
                </div>
            </div>
        </div>
        <table id="file-table" lay-filter="file-table"></table>
    </div>
</script>
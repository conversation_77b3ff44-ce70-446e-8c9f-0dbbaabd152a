package util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitDestination;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import common.PageMarker;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.codec.binary.Base64;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import static util.TableUtil.dealObjNull;

public class PdfUtil {

	/**
	 * 获取pdf字体
	 *
	 * @return
	 * @throws IOException
	 */
	public static PdfFont getPdfFont() throws IOException {
		String fontPath = Util.getPdfFontPath();
		PdfFont font = PdfFontFactory.createFont(fontPath + "//simhei.ttf", PdfEncodings.IDENTITY_H);
		return font;
	}

	/**
	 * 将HandsonTable的表格数据导出pdf
	 *
	 * @param data     JSON数据
	 * @param tempPath
	 * @return
	 */
	public static File tableData2Pdf(JSONObject data, String tempPath) throws IOException {
		String tableStr = data.getStr("HTML_DATA", "");
		String saveDataStr = data.getStr("SAVE_DATA", "");
		int headerRow = data.getInt("TABLE_HEADER", 0);
		String tableName = getTableName(data);
		FileUtil.mkdir(tempPath);
		String path = tempPath + "\\" + tableName + ".pdf";
		File file = new File(path);
		PdfDocument pdfDoc = new PdfDocument(new PdfWriter(path));
		PageSize pageSize = PageSize.A4.rotate();
		Document doc = new Document(pdfDoc, pageSize);
		doc.setMargins(10f, 10f, 10f, 10f);
		PdfFont font = getPdfFont();
		// 添加标题
		doc.add(new Paragraph(tableName).setFirstLineIndent(8).setMultipliedLeading(1.2f).setFont(font).setBold())
				.setTextAlignment(TextAlignment.LEFT);
		if (StrUtil.isNotEmpty(saveDataStr)) {
			tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font);
		}

		doc.close();
		return file;
	}

	public static String getTableName(JSONObject data) {
		String tableNum = data.getStr("TABLE_NUM", "");
		String security = data.getStr("SECURITY_NAME", "");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(tableNum)) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		tableName += "（" + security + "）";
		return tableName;
	}

	public static JSONObject getTdImgs(String htmlStr) {
		JSONObject res = JSONUtil.createObj();
		JSONObject tdImgs = JSONUtil.createObj();
		if (StrUtil.isNotBlank(htmlStr)) {
			org.jsoup.nodes.Document doc = Jsoup.parseBodyFragment(htmlStr);
			Element table = doc.getElementsByTag("table").get(0);
			Element tbody = table.getElementsByTag("tbody").get(0);
			Elements trs = tbody.getElementsByTag("tr");
			for (int i = 0; i < trs.size(); i++) {
				Element tr = trs.get(i);
				Elements tds = tr.getElementsByTag("td");
				for (int j = 0; j < tds.size(); j++) {
					Element td = tds.get(j);
					td.attr("img", "false");
					Elements imgs = td.getElementsByTag("img");
					if (!imgs.isEmpty()) {
						int row = StrUtil.isNotBlank(td.attr("row")) ? Convert.toInt(td.attr("row")) : i;
						int col = StrUtil.isNotBlank(td.attr("col")) ? Convert.toInt(td.attr("col")) : j;
						List<String> photoShowNums = new ArrayList<>();
						JSONArray imgArr = new JSONArray();
						for (int x = 0; x < imgs.size(); x++) {
							Element img = imgs.get(x);
							JSONObject imgObj = JSONUtil.createObj();
							String imgType = img.attr("type");
							if ("photo".equals(imgType)) {
								imgObj.set("type", imgType);
								imgObj.set("photoPath", img.attr("src"));
								imgObj.set("photoName", img.attr("photoName"));
								imgObj.set("photoShowNum", img.attr("photoShowNum"));
								imgObj.set("photoId", img.attr("id"));
								imgObj.set("photoformat", img.attr("photoformat"));
								photoShowNums.add(img.attr("photoShowNum"));
								imgArr.set(imgObj);
								Objects.requireNonNull(img.previousElementSibling()).remove();
								Element br2 = img.nextElementSibling();
								Element span = null;
								if (br2 != null) {
									span = br2.nextElementSibling();
								}
								if (br2 != null) {
									br2.remove();
								}
								if (span != null) {
									span.remove();
								}
								img.remove();
							}
						}
						if (!imgArr.isEmpty()) {
							tdImgs.set(row + "-" + col, imgArr);
							String imgNumShow = Util.dealImgNumShow(photoShowNums);
							td.attr("img", "true");
							if (td.childrenSize() > 0) {
								imgNumShow = "<br>" + imgNumShow;
							}
							td.append(imgNumShow);
						}
					}
				}
			}
			res.set("html", table.outerHtml());
			res.set("tdImgs", tdImgs);
		} else {
			res.set("html", "");
			res.set("tdImgs", "{}");
		}
		return res;
	}

	/**
	 * 将html字符串转换为图片
	 *
	 * @param tableStr
	 * @return
	 */
	public static JSONArray html2Images(String tableStr) {
		JSONArray images = new JSONArray();
		if (StrUtil.isNotBlank(tableStr)) {
			org.jsoup.nodes.Document doc = Jsoup.parseBodyFragment(tableStr);
			Element table = doc.getElementsByTag("table").get(0);
			Element tbody = table.getElementsByTag("tbody").get(0);
			Elements trs = tbody.getElementsByTag("tr");
			for (int i = 0; i < trs.size(); i++) {
				Element tr = trs.get(i);
				Elements tds = tr.getElementsByTag("td");
				for (int j = 0; j < tds.size(); j++) {
					Element td = tds.get(j);
					Elements imgs = td.getElementsByTag("img");
					if (!imgs.isEmpty()) {
						JSONObject imgsObj = JSONUtil.createObj();
						if (StrUtil.isNotBlank(td.attr("row"))) {
							imgsObj.set("row", td.attr("row"));
						} else {
							imgsObj.set("row", i);
						}
						if (StrUtil.isNotBlank(td.attr("col"))) {
							imgsObj.set("col", td.attr("col"));
						} else {
							imgsObj.set("col", j);
						}
						JSONArray imgArr = new JSONArray();
						for (int x = 0; x < imgs.size(); x++) {
							Element img = imgs.get(x);
							JSONObject imgObj = JSONUtil.createObj();
							String imgType = img.attr("type");
							imgObj.set("type", imgType);
							imgObj.set("src", img.attr("src"));
							imgObj.set("date", img.attr("date"));
							imgObj.set("class", img.attr("class"));
							if (imgType.equals("photo")) {
								imgObj.set("imgNum", img.attr("photoShowNum"));
							}
							imgArr.set(imgObj);
						}
						imgsObj.set("img", imgArr);
						images.add(imgsObj);
					}
				}
			}
		}
		return images;
	}

	/**
	 * 获取大表格列数
	 *
	 * @return
	 */
	public static int getLargeTableCol() {
		Setting setting = new Setting("config.setting");
		int largeTableCol = setting.getInt("LargeTableCol");
		return largeTableCol;
	}

	/**
	 * 获取列宽
	 *
	 * @param colWidths
	 * @return
	 */
	public static UnitValue[] getColWidths(JSONArray colWidths) {
		UnitValue[] res = new UnitValue[colWidths.size()];
		float total = 0;
		for (int i = 0; i < colWidths.size(); i++) {
			float anfloat = colWidths.getFloat(i);
			total += anfloat;
		}
		for (int i = 0; i < colWidths.size(); i++) {
			float anfloat = colWidths.getFloat(i);
			double div = NumberUtil.mul(NumberUtil.div(anfloat, total), 100);

			UnitValue aUnitValue = new UnitValue(UnitValue.PERCENT, (float) div);
			res[i] = aUnitValue;
		}
		return res;
	}

	/**
	 * 处理图片序号
	 *
	 * @param imgNums
	 * @return
	 */

	public static String dealImgNum(JSONArray imgNums) {
		String tableNum = "";
		try {
			tableNum = imgNums.getStr(0).substring(0, imgNums.getStr(0).lastIndexOf('-'));
		} catch (Exception e) {

		}
		if (StrUtil.isNotBlank(tableNum)) {
			tableNum = tableNum + "-";
		}
		JSONArray arr = new JSONArray();
		for (int i = 0; i < imgNums.size(); i++) {
			String imgNum = imgNums.getStr(i);
			String[] tempArr = imgNum.split("-");
			int num = Convert.toInt(tempArr[tempArr.length - 1].substring(1));
			arr.add(num);
		}
		JSONArray newArr = new JSONArray();
		for (int i = 0; i < arr.size(); i++) {
			JSONArray tempArr = new JSONArray();
			tempArr.add(arr.getInt(i));
			while ((arr.getInt(i + 1, 0) - arr.getInt(i)) == 1) {
				tempArr.add(arr.getInt(i + 1));
				i++;
			}
			newArr.add(tempArr);
		}

		JSONArray textArr = new JSONArray();
		textArr.add("");

		for (int i = 0; i < newArr.size(); i++) {
			JSONArray childArr = newArr.getJSONArray(i);
			if (childArr.size() == 1) {
				textArr.add(tableNum + "图" + childArr.getStr(0));
			} else {
				textArr.add(tableNum + "图" + childArr.getStr(0) + "~图" + childArr.getStr(childArr.size() - 1));
			}
		}
		String res = StrUtil.join(" ", textArr);
		return res;
	}

	public static void addCellContent(Table table, int row, int col, boolean hasColWidth, int headerRow, JSONObject cellObj,
									  com.itextpdf.layout.element.Cell cell, PdfFont font) {
		String rootPath = Util.getFileUploadPath();
		String className = cellObj.getStr("className", "htMiddle htCenter");
		if (className.contains("c-bold")) {
			cell.setBold();
		}
		// 默认居中
		cell.setTextAlignment(TextAlignment.CENTER)
				.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.MIDDLE);

		if (className.contains("htLeft")) {
			cell.setTextAlignment(TextAlignment.LEFT);
		}
		if (className.contains("htCenter")) {
			cell.setTextAlignment(TextAlignment.CENTER);
		}
		if (className.contains("htRight")) {
			cell.setTextAlignment(TextAlignment.RIGHT);
		}
		if (className.contains("htJustify")) {
			cell.setTextAlignment(TextAlignment.JUSTIFIED);
		}
		if (className.contains("htTop")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.TOP);
		}
		if (className.contains("htMiddle")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.MIDDLE);
		}
		if (className.contains("htBottom")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.BOTTOM);
		}
		if (className.contains("font-size")) {
			int size = 13;
			String[] classes = className.split(" ");
			for (String cla : classes) {
				if (cla.contains("font-size")) {
					size = Convert.toInt(cla.split("-")[2]);
				}
			}
			cell.setFontSize(size);
		}

		if (className.contains("font-color")) {
			Color color = getFontColor(className);
			cell.setFontColor(color);
		}
//		cell.setKeepTogether(true);
		cell.add(new Paragraph(cellObj.getStr("text")).setMultipliedLeading(1.2f).setFont(font));

		JSONArray imgArr = cellObj.getJSONArray("img");
		if (!imgArr.isEmpty()) {
			try {
				JSONArray imgNumArr = new JSONArray();
				for (int x = 0; x < imgArr.size(); x++) {
					JSONObject imgObj = imgArr.getJSONObject(x);
					String src = imgObj.getStr("src");
					String imgType = imgObj.getStr("type");
					String imgClass = imgObj.getStr("class", "");
					String date = imgObj.getStr("date", "");
					if (imgType.equals("sign")) {
						Image img;
						if (src.contains("data:")) {
							img = new Image(
									ImageDataFactory.create(Base64.decodeBase64(src.substring(src.indexOf("base64,") + 7))));
						} else {
							img = new Image(ImageDataFactory.create(rootPath + src.substring(5)));
						}
						int maxWidth = 80;
						int maxHeight = 40;
						if (imgClass.contains("test-sign")) {
							maxWidth = 120;
							maxHeight = 60;
						}
						if (hasColWidth) {
							//842是页面宽度 减去20 是页面左右边距 822
							float pageWidth = 822;
							int colSpan = cell.getColspan();
							float cellTotalWidth = 0;
							for (int c = 0; c < colSpan; c++) {
								cellTotalWidth += table.getColumnWidth(col + c).getValue();
							}
							float cellWidth = Convert.toFloat(NumberUtil.div(NumberUtil.mul(pageWidth, cellTotalWidth), 100));
							if (cellWidth < maxWidth) {
//								img.setWidth(cellWidth);
								img.setAutoScale(true);
							} else {
								img.setMaxWidth(maxWidth);
								img.setMaxHeight(maxHeight);
							}
						} else {
							img.setMaxWidth(maxWidth);
							img.setMaxHeight(maxHeight);
						}
						cell.add(img);

						if (StrUtil.isNotEmpty(date)) {
							cell.add(new Paragraph(date).setMultipliedLeading(1.2f).setFont(font))
									.setTextAlignment(TextAlignment.LEFT);
						}
					} else {
						String imgNum = imgObj.getStr("imgNum");
						imgNumArr.add(imgNum);
					}
				}
				if (!imgNumArr.isEmpty()) {
					cell.add(new Paragraph(dealImgNum(imgNumArr)).setMultipliedLeading(1.2f).setFont(font).setFontColor(new DeviceRgb(255, 0, 0)));
				}
			} catch (MalformedURLException e) {
				e.printStackTrace();
			}
		}
		if (row < headerRow) {
			Color bgColour = new DeviceRgb(230, 230, 230);
			table.addHeaderCell(cell.setBold().setBackgroundColor(bgColour));
		} else {
			table.addCell(cell);
		}
	}

	private static Color getFontColor(String className) {
		Color color = ColorConstants.BLACK;
		String[] classes = className.split(" ");
		for (String cla : classes) {
			if (cla.contains("font-color")) {
				String colorName = cla.split("-")[2];
				if (colorName.equals("purple")) {
					color = new DeviceRgb(128, 0, 128);
				} else if (colorName.equals("green")) {
					color = new DeviceRgb(0, 128, 0);
				} else if (colorName.equals("orange")) {
					color = new DeviceRgb(255, 165, 0);
				} else if (colorName.equals("deeppink")) {
					color = new DeviceRgb(255, 20, 147);
				}
			}
		}
		return color;
	}

	/**
	 * 将表格数据转换为PDF表格
	 *
	 * @param doc         PDF文档对象
	 * @param tableStr    表格字符串
	 * @param saveDataStr 保存数据字符串
	 * @param headerRow   表头行
	 * @param font        PDF字体对象
	 */
	public static void tableDataToPdfTable(Document doc, String tableStr, String saveDataStr, int headerRow,
										   PdfFont font) {

		// 将HTML字符串转换为图片数组
		JSONArray images = html2Images(tableStr);
		// 解析保存数据字符串为JSON对象
		JSONObject saveData = dealObjNull(saveDataStr);
		// 获取表格数据数组
		JSONArray tableData = saveData.getJSONArray("tableData");
		// 获取列宽度数组
		JSONArray colWidths = saveData.getJSONArray("colWidths");
		int tableColNum = 0;
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);
			if (row.size() > tableColNum) {
				tableColNum = row.size();
			}
			for (int j = 0; j < row.size(); j++) {
				String text = row.getStr(j, "");
				row.set(j, JSONUtil.createObj().set("type", "text").set("text", text).set("img", new JSONArray()));
			}
		}
		// 获取合并单元格数组
		JSONArray mergeds = saveData.getJSONArray("merged");
		for (int i = 0; i < mergeds.size(); i++) {
			JSONObject merged = mergeds.getJSONObject(i);

			int row = merged.getInt("row");
			int col = merged.getInt("col");
			int rowspan = merged.getInt("rowspan");
			int colspan = merged.getInt("colspan");

			for (int r = row; r < rowspan + row; r++) {
				for (int c = col; c < colspan + col; c++) {
					if (!(r == row && c == col)) {
						tableData.getJSONArray(r).set(c,
								JSONUtil.createObj().set("type", "del").set("text", "").set("img", new JSONArray()));
					}
				}
			}
			JSONObject mergedObj = JSONUtil.createObj();
			mergedObj.set("type", "merged");
			mergedObj.set("rowspan", rowspan);
			mergedObj.set("colspan", colspan);
			mergedObj.set("img", new JSONArray());
			mergedObj.set("text", tableData.getJSONArray(row).getJSONObject(col).getStr("text"));
			tableData.getJSONArray(row).set(col, mergedObj);
		}
		// 获取元数据数组
		JSONArray metas = saveData.getJSONArray("meta");
		if (metas != null) {
			for (int i = 0; i < metas.size(); i++) {
				JSONObject meta = metas.getJSONObject(i);
				int row = meta.getInt("row");
				int col = meta.getInt("col");
				String className = meta.getStr("className", "htMiddle htCenter");
				JSONArray arr = tableData.getJSONArray(row);
				if (arr != null) {
					JSONObject obj = arr.getJSONObject(col);
					if (obj != null) {
						obj.set("className", className);
					}
				}
			}
		}

		// 将图片数组添加到表格数据中
		for (int i = 0; i < images.size(); i++) {
			JSONObject image = images.getJSONObject(i);
			int row = image.getInt("row");
			int col = image.getInt("col");
			JSONArray imgArr = image.getJSONArray("img");
			JSONObject cellObj = tableData.getJSONArray(row).getJSONObject(col);
			cellObj.set("img", imgArr);
		}
		// 获取最大列数
		int largeTableCol = getLargeTableCol();
		// 创建表格对象
		Table table = null;
		boolean hasColWidth = ObjectUtil.isNotNull(colWidths);
		if (!hasColWidth) {
			if (tableColNum > largeTableCol) {
				table = new Table(tableColNum, true);
			} else {
				table = new Table(tableColNum);
			}
		} else {
			// 获取列宽度数组
			UnitValue[] widths = getColWidths(colWidths);
			if (tableColNum > largeTableCol) {
				table = new Table(widths, true);
			} else {
				table = new Table(widths);
			}
		}

		table.useAllAvailableWidth();
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);

			for (int j = 0; j < row.size(); j++) {

				JSONObject cellObj = row.getJSONObject(j);
				String cellType = cellObj.getStr("type");
				if (cellType.equals("text")) {
					// 创建单元格对象
					com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell();
					// 添加单元格内容
					addCellContent(table, i, j, hasColWidth, headerRow, cellObj, cell, font);
				} else if (cellType.equals("merged")) {
					// 创建合并单元格对象
					com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell(
							cellObj.getInt("rowspan"), cellObj.getInt("colspan"));
					// 添加单元格内容
					addCellContent(table, i, j, hasColWidth, headerRow, cellObj, cell, font);
				}
			}
		}

		doc.add(new Paragraph());
		doc.add(table);
		if (tableColNum > largeTableCol) {
			table.flush();
			table.complete();
		}
		if (images.size() > 0) {
			String rootPath = Util.getFileUploadPath();
			// 创建图片表格对象
			Table imgTable = new Table(new float[]{200, 200});
			imgTable.useAllAvailableWidth();
			for (int i = 0; i < images.size(); i++) {
				JSONObject image = images.getJSONObject(i);
				JSONArray imgArr = image.getJSONArray("img");
				for (int j = 0; j < imgArr.size(); j++) {
					JSONObject imgObj = imgArr.getJSONObject(j);
					String imgType = imgObj.getStr("type");
					if (imgType.equals("photo")) {
						String imgNum = imgObj.getStr("imgNum");
						String src = imgObj.getStr("src");
						src = src.substring(5);

						// 图片压缩
						//String tempPath = rootPath + File.separator + "pdfTempImage" + File.separator + src;
						//FileUtil.mkdir(tempPath);
						//Img.from(FileUtil.file(rootPath + src))
						//    .setQuality(0.3)//压缩比率
						//    .write(FileUtil.file(tempPath));
						try {
							if (FileUtil.exist(rootPath + src)) {
								Image img = new Image(ImageDataFactory.create(rootPath + src));
								img.setMaxWidth(380);
								img.setMaxHeight(500);
								com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell();
								cell.setBorder(Border.NO_BORDER);
								cell.setTextAlignment(TextAlignment.CENTER);
								cell.add(img.setTextAlignment(TextAlignment.CENTER));
								cell.add(new Paragraph(imgNum).setTextAlignment(TextAlignment.CENTER).setMultipliedLeading(1.2f)
										.setFont(font));
								imgTable.addCell(cell);
							}
						} catch (Exception e) {
							System.out.println("插入图片出错 = " + e.getLocalizedMessage());
							e.printStackTrace();
						}

					}
				}
			}
			doc.add(new Paragraph());
			doc.add(imgTable);
		}
	}

	/**
	 * 校验上传的excel文件 能否正常导出pdf
	 * 测试在有表头行的情况下才会导出失败
	 *
	 * @return
	 */
	public static boolean isExportPdf(String tableData) {
		JSONObject data = new JSONObject();
		data.set("SAVE_DATA", tableData);
		data.set("TABLE_HEADER", "1");
		data.set("NAME", System.currentTimeMillis() + "");
		boolean flag = true;
		try {
			File file = tableData2Pdf(data, Util.getFileTempPath());
			FileUtil.del(file);
		} catch (Exception e) {
			flag = false;
		}
		return flag;
	}

	/**
	 * 导出失败的节点
	 *
	 * @param errorNode
	 * @return
	 */
	public static String exportPdfErrorMsg(JSONArray errorNode) {
		StringBuilder errorNodes = new StringBuilder();
		for (int i = 0; i < errorNode.size(); i++) {
			errorNodes.append(errorNode.getStr(i)).append(",");
		}
		errorNodes = new StringBuilder(errorNodes.substring(0, errorNodes.length() - 1));
		String msg = "部分节点导出失败，如下：" + errorNodes;
		return msg;
	}

	/**
	 * 导出树状结构到pdf
	 *
	 * @param nodes
	 */

	public static JSONObject exportTreeToPdf(JSONArray nodes, String filePath) {
		JSONObject res = new JSONObject();
		System.out.println(DateUtil.now() + " - [PDF生成] 开始PDF文件生成，节点数量: " + nodes.size());
		TreeUtil.logMemoryUsage("PDF生成开始前");

		try {
			// 检查内存是否足够
			TreeUtil.checkMemoryAvailable("PDF文件生成", 50);

			String fileTempPath = filePath + System.currentTimeMillis() + ".pdf";
			File file = FileUtil.file(fileTempPath);
			PdfDocument pdfDoc = new PdfDocument(new PdfWriter(fileTempPath));
			PdfOutline rootOutline = pdfDoc.getOutlines(false);
			Document doc = new Document(pdfDoc, PageSize.A4.rotate());
			doc.setMargins(10f, 10f, 10f, 10f);
			PdfFont font = getPdfFont();
			pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE, new PageMarker(font));
			JSONArray errorNode = new JSONArray();

			TreeUtil.logMemoryUsage("PDF文档初始化后");

			// 使用优化的节点处理方法
			nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, nodes, font);

			TreeUtil.logMemoryUsage("PDF内容生成后");

			if (pdfDoc.getNumberOfPages() != 0) {
				pdfDoc.removePage(pdfDoc.getNumberOfPages());
			}
			doc.close();
			pdfDoc.close();
			if (errorNode.isEmpty()) {
				res.set("success", true);
				if (!nodes.isEmpty()) {
					file = renameRootName(nodes, file);
				}
				res.set("data", file.getAbsolutePath());
			} else {
				res.set("success", false);
				res.set("msg", exportPdfErrorMsg(errorNode));
			}
		} catch (Exception e) {
			e.printStackTrace();
			res.set("success", false);
			res.set("msg", e.getMessage());
		}
		return res;
	}

	/**
	 * 重命名根节点
	 *
	 * @param nodes
	 * @param file
	 * @return
	 */
	public static File renameRootName(JSONArray nodes, File file) {
		JSONObject rootObj = nodes.getJSONObject(0).getJSONObject("data");
		String tableNum = rootObj.getStr("TABLE_NUM", "");
		String tableName = rootObj.getStr("NAME", "") + "（" + rootObj.getStr("SECURITY_NAME") + "）";
		if (StrUtil.isNotBlank(tableNum)) {
			tableName = tableNum + "：" + tableName;
		}
		file = FileUtil.rename(file, tableName, true, true);
		return file;
	}

	/**
	 * 节点转pdf（内存优化版本）
	 */
	public static void nodeToPdfOptimized(JSONArray errorNode, PdfDocument pdfDoc, Document doc,
								 PdfOutline rootOutline, JSONArray nodes, PdfFont font) {
		System.out.println(DateUtil.now() + " - [PDF节点处理] 开始处理节点，总数: " + nodes.size());
		int processedCount = 0;

		for (int i = 0; i < nodes.size(); i++) {
			JSONObject node = nodes.getJSONObject(i);

			// 每处理50个节点检查一次内存
			if (processedCount % 50 == 0) {
				TreeUtil.logMemoryUsage("已处理" + processedCount + "个节点");

				// 如果内存使用率超过85%，强制GC
				Runtime runtime = Runtime.getRuntime();
				long usedMemory = runtime.totalMemory() - runtime.freeMemory();
				double usedPercent = (double) usedMemory / runtime.maxMemory() * 100;
				if (usedPercent > 85) {
					System.out.println(DateUtil.now() + " - [PDF节点处理] 内存使用率过高，执行GC");
					System.gc();
					try {
						Thread.sleep(100);
					} catch (InterruptedException e) {
						Thread.currentThread().interrupt();
					}
				}
			}

			processNodeToPdf(errorNode, pdfDoc, doc, rootOutline, node, font);
			processedCount++;
		}

		System.out.println(DateUtil.now() + " - [PDF节点处理] 节点处理完成，总计: " + processedCount);
	}

	/**
	 * 处理单个节点转PDF
	 */
	private static void processNodeToPdf(JSONArray errorNode, PdfDocument pdfDoc, Document doc,
								 PdfOutline rootOutline, JSONObject node, PdfFont font) {
		JSONArray children = node.getJSONArray("children");
		if (ObjectUtil.isNull(children)) {
			children = new JSONArray();
		}
		JSONObject nodeData = node.getJSONObject("data");
		String type = nodeData.getStr("TYPE");
		String name = nodeData.getStr("NAME");
		String tableNum = nodeData.getStr("TABLE_NUM");
		String sort = nodeData.getStr("SORT");
		String securityName = nodeData.getStr("SECURITY_NAME");

		if (type.equals("model")) {
			if (!children.isEmpty()) {
				nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, children, font);
			}
		} else if (type.equals("project")) {
			String title = sort + "-" + name;
			doc.add(new Paragraph(title).setFirstLineIndent(0).setMultipliedLeading(1.2f).setFont(font).setBold())
					.setTextAlignment(TextAlignment.LEFT);
			PdfOutline projectOutLine = rootOutline.addOutline(title);
			projectOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
					pdfDoc.getLastPage().getPageSize().getTop())));
			if (!children.isEmpty()) {
				nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, children, font);
			}
		} else if (type.equals("a")) {
			String title = tableNum + "：" + name + "（" + securityName + "）";
			doc.add(new Paragraph(title).setFirstLineIndent(8).setMultipliedLeading(1.2f).setFont(font).setBold())
					.setTextAlignment(TextAlignment.LEFT);
			PdfOutline aOutLine = rootOutline.addOutline(title);
			aOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
					pdfDoc.getLastPage().getPageSize().getTop())));
			boolean flag = addTable(doc, font, nodeData);
			if (!flag) {
				errorNode.add(title);
			}
			if (!children.isEmpty()) {
				nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, children, font);
			}
		} else if (type.equals("b")) {
			String title = tableNum + "：" + name + "（" + securityName + "）";
			doc.add(new Paragraph(title).setFirstLineIndent(16).setMultipliedLeading(1.2f).setFont(font).setBold())
					.setTextAlignment(TextAlignment.LEFT);

			PdfOutline bOutLine = rootOutline.addOutline(title);
			bOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
					pdfDoc.getLastPage().getPageSize().getTop())));
			boolean flag = false;
			String nodeFilePath = nodeData.getStr("FILE_PATH", "");
			if (nodeData.getStr("FILE_FORMAT", "").equalsIgnoreCase("pdf") && StrUtil.isNotBlank(nodeFilePath)) {
				String fileUploadPath = Util.getFileUploadPath();
				String filePath = fileUploadPath + nodeFilePath;
				try {
					PdfDocument pdfToInsert = new PdfDocument(new PdfReader(filePath));
					for (int pageNumber = 1; pageNumber <= pdfToInsert.getNumberOfPages(); pageNumber++) {
						PdfPage page = pdfToInsert.getPage(pageNumber);

						PdfFormXObject pdfFormXObject = page.copyAsFormXObject(pdfDoc);
						Image image = new Image(pdfFormXObject);
						image.setHorizontalAlignment(HorizontalAlignment.CENTER);
						if (image.getImageHeight() > image.getImageWidth()) {
							image.setRotationAngle(Math.toRadians(90));
						}
						doc.add(image);
					}
					pdfToInsert.close();
					doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					flag = true;
				} catch (IOException e) {
					e.printStackTrace();
				}
			} else {
				flag = addTable(doc, font, nodeData);
			}
			if (!flag) {
				errorNode.add(title);
			}
			if (!children.isEmpty()) {
				nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, children, font);
			}
		}
	}

	/**
	 * 节点转pdf（保留原方法以兼容性）
	 */
	public static void nodeToPdf(JSONArray errorNode, PdfDocument pdfDoc, Document doc,
								 PdfOutline rootOutline, JSONArray nodes, PdfFont font) {
		// 调用优化版本
		nodeToPdfOptimized(errorNode, pdfDoc, doc, rootOutline, nodes, font);
	}

	public static boolean addTable(Document doc, PdfFont font, JSONObject nodeData) {
		String saveDataStr = nodeData.getStr("SAVE_DATA", "");
		boolean flag = true;
		if (StrUtil.isNotBlank(saveDataStr)) {
			String tableStr = nodeData.getStr("HTML_DATA");
			int headerRow = nodeData.getInt("TABLE_HEADER", 0);
			try {
				tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font);
				doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			} catch (Exception e) {
				e.printStackTrace();
				flag = false;
			}
		}
		return flag;
	}

	public static JSONObject exportPdfZip(String id) {
		JSONObject res = new JSONObject();
		JSONArray nodes = SqliteUtil.executeQuery(TreeUtil.getQueryAllChildSql(id));
		JSONArray errorNode = new JSONArray();
		String fileTempPath = Util.getFileTempPath();
		for (int i = 0; i < nodes.size(); i++) {
			JSONObject node = nodes.getJSONObject(i);
			String name = node.getStr("NAME");
			if ("a".equals(node.getStr("TYPE")) || "b".equals(node.getStr("TYPE"))) {
				try {
					String nodeFilePath = node.getStr("FILE_PATH", "");
					if (node.getStr("FILE_FORMAT", "").equalsIgnoreCase("pdf") && StrUtil.isNotBlank(nodeFilePath)) {
						String fileUploadPath = Util.getFileUploadPath();
						String filePath = fileUploadPath + nodeFilePath;
						FileUtil.copy(filePath, fileTempPath + getTableName(node) + ".pdf", true);
					} else {
						tableData2Pdf(node, fileTempPath);
					}
				} catch (IOException e) {
					errorNode.add(name);
				}
			}
		}
		File resFile = ZipUtil.zip(fileTempPath, Charset.forName("GBK"));
		if (!nodes.isEmpty()) {
			String newFileName = getTableName(nodes.getJSONObject(0));
			resFile = FileUtil.rename(resFile, newFileName, true, true);
		}
		if (!errorNode.isEmpty()) {
			res.set("success", false);
			res.set("msg", exportPdfErrorMsg(errorNode));
		} else {
			res.set("data", resFile.getAbsolutePath());
			res.set("success", true);
			res.set("msg", "导出成功！");
		}
		return res;
	}
}

//! moment.js locale configuration
//! locale : Konkani Latin script [gom-latn]
//! author : The Discoverer : https://github.com/WikiDiscoverer
!function(a,e){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?e(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],e):e(a.moment)}(this,(function(a){"use strict";
//! moment.js locale configuration
function e(a,e,n,r){var t={s:["thoddea sekondamni","thodde sekond"],ss:[a+" sekondamni",a+" sekond"],m:["eka mintan","ek minut"],mm:[a+" mintamni",a+" mintam"],h:["eka voran","ek vor"],hh:[a+" voramni",a+" voram"],d:["eka disan","ek dis"],dd:[a+" disamni",a+" dis"],M:["eka mhoinean","ek mhoino"],MM:[a+" mhoineamni",a+" mhoine"],y:["eka vorsan","ek voros"],yy:[a+" vorsamni",a+" vorsam"]};return r?t[n][0]:t[n][1]}return a.defineLocale("gom-latn",{months:{standalone:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),format:"Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Fuddlo] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fattlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:e,ss:e,m:e,mm:e,h:e,hh:e,d:e,dd:e,M:e,MM:e,y:e,yy:e},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(a,e){return"D"===e?a+"er":a},week:{dow:0,doy:3},meridiemParse:/rati|sokallim|donparam|sanje/,meridiemHour:function(a,e){return 12===a&&(a=0),"rati"===e?a<4?a:a+12:"sokallim"===e?a:"donparam"===e?a>12?a:a+12:"sanje"===e?a+12:void 0},meridiem:function(a,e,n){return a<4?"rati":a<12?"sokallim":a<16?"donparam":a<20?"sanje":"rati"}})}));
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).itCH=e()}}(function(){return function t(f,u,l){function d(n,e){if(!u[n]){if(!f[n]){var o="function"==typeof require&&require;if(!e&&o)return o(n,!0);if(a)return a(n,!0);var i=new Error("Cannot find module '"+n+"'");throw i.code="MODULE_NOT_FOUND",i}var r=u[n]={exports:{}};f[n][0].call(r.exports,function(e){return d(f[n][1][e]||e)},r,r.exports,t,f,u,l)}return u[n].exports}for(var a="function"==typeof require&&require,e=0;e<l.length;e++)d(l[e]);return d}({1:[function(e,n,o){"use strict";n.exports={languageTag:"it-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"°"},currency:{symbol:"CHF",code:"CHF"}}},{}]},{},[1])(1)});
//# sourceMappingURL=it-CH.min.js.map

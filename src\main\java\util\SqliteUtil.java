package util;

import cn.hutool.db.Entity;
import cn.hutool.db.Session;
import cn.hutool.db.ds.DSFactory;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import javax.sql.DataSource;

public class SqliteUtil {

	private static Connection connection = null;

	/**
	 * 连接数据库
	 */
	public static void connect() {

		try {
			DataSource ds = DSFactory.get();
			connection = ds.getConnection();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	static {
		connect();
	}


	/**
	 * 查询数据库
	 *
	 * @param sql
	 * @return
	 */
	public static JSONArray executeQuery(String sql) {
		List<Entity> entityList;
		try {
			entityList = SqlExecutor.query(connection, sql, new EntityListHandler());
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		JSONArray arr = JSONUtil.parseArray(entityList);
		return arr;
	}

	/**
	 * 更新数据库（增加、删除、修改）
	 *
	 * @param sql
	 * @return
	 */
	public static int executeCommand(String sql) {
		int res = -1;
		try {
			res = SqlExecutor.execute(connection, sql);
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return res;
	}

	public static void executeUpdateTransaction(Entity entity, Entity where) {
		Session session = Session.create();
		try {
			session.beginTransaction();
			session.update(entity, where);
			session.commit();
		} catch (SQLException e) {
			session.quietRollback();
		}
	}

	public static void executeInsertTransaction(Entity entity) {
		Session session = Session.create();
		try {
			session.beginTransaction();
			session.insert(entity);
			session.commit();
		} catch (SQLException e) {
			session.quietRollback();
		}
	}
}

